/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React from 'react';
import {I18nextProvider} from 'react-i18next';
import {Provider} from 'react-redux';
import {PersistGate} from 'redux-persist/integration/react';
import i18n from './src/i18n';
import store, {persistor} from './src/redux/store.ts';

import {StatusBar} from 'react-native';
import 'react-native-gesture-handler';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import Navigation from './src/navigation/Navigation.tsx';
import {KeyboardProvider} from 'react-native-keyboard-controller';

const App = () => {
  return (
    <Provider store={store}>
      <PersistGate persistor={persistor}>
        <KeyboardProvider>
          <I18nextProvider i18n={i18n}>
            <GestureHandlerRootView style={{flex: 1}}>
              <StatusBar
                translucent
                backgroundColor="transparent"
                barStyle="dark-content"
              />
              <Navigation />
            </GestureHandlerRootView>
          </I18nextProvider>
        </KeyboardProvider>
      </PersistGate>
    </Provider>
  );
};

export default App;
