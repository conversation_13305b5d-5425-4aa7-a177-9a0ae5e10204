import React, {useEffect} from 'react';
import {StyleSheet, View} from 'react-native';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';

interface WaveformProps {
  color?: string;
  animated?: boolean;
}

const BAR_COUNT = 20;
const DURATION = 500;
const Waveform: React.FC<WaveformProps> = ({
  color = '#DE773D',
  animated = true,
}) => {
  const amplitudes = Array.from({length: BAR_COUNT}, () => useSharedValue(30));

  useEffect(() => {
    amplitudes.forEach((amplitude, index) => {
      if (animated) {
        amplitude.value = withRepeat(
          withTiming(50 + Math.random() * 50, {
            duration: DURATION,
            delay: index * 100,
            easing: Easing.inOut(Easing.ease),
          }),
          -1,
          true,
        );
      } else {
        amplitude.value = 30;
      }
    });
  }, [animated]);

  return (
    <View style={styles.container}>
      {amplitudes.map((amplitude, index) => {
        const animatedStyle = useAnimatedStyle(() => ({
          height: amplitude.value,
        }));

        return (
          <Animated.View
            key={index}
            style={[styles.bar, animatedStyle, {backgroundColor: color}]}
          />
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 100,
  },
  bar: {
    width: 5,
    marginHorizontal: 5,
    borderRadius: 5,
  },
});

export default Waveform;