import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {ActivityIndicator, Pressable, StyleSheet, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import Animated, {
  cancelAnimation,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import {Theme} from '../../src/themes';

const barHeights = [
  10, 20, 40, 20, 10, 10, 20, 40, 20, 10, 10, 20, 40, 20, 10, 10, 20, 40, 20,
  10,
];

export const WaveBar = memo(
  ({index, isRecording}: {index: number; isRecording: boolean}) => {
    const scaleY = useSharedValue(1);

    useEffect(() => {
      if (isRecording) {
        scaleY.value = withRepeat(
          withTiming(1.5, {
            duration: 300,
          }),
          -1,
          true,
        );
      } else {
        cancelAnimation(scaleY);
        scaleY.value = withTiming(1, {duration: 100});
      }
    }, [isRecording, scaleY]);

    const animatedStyle = useAnimatedStyle(
      () => ({
        transform: [{scaleY: scaleY.value}],
      }),
      [],
    );

    return (
      <Animated.View
        style={[styles.bar, {height: barHeights[index]}, animatedStyle]}
      />
    );
  },
);

export const RecordButtonWithRipple = memo(
  ({
    callBackRecording,
    disabled = false,
    loading = false,
  }: {
    callBackRecording?: () => void;
    disabled?: boolean;
    loading?: boolean;
  }) => {
    const [isRecording, setIsRecording] = useState<boolean>(false);
    const callBackRecordingRef = useRef(callBackRecording);

    useEffect(() => {
      callBackRecordingRef.current = callBackRecording;
    }, [callBackRecording]);

    const toggleRecording = useCallback(() => {
      setIsRecording(prev => !prev);
      callBackRecordingRef.current?.();
    }, [disabled]);

    const leftBars = useMemo(() => {
      if (!isRecording) return null;
      return [0, 1, 2, 3, 4, 5, 6, 7, 8, 9].map(i => (
        <WaveBar key={`left-${i}`} index={i} isRecording={isRecording} />
      ));
    }, [isRecording]);

    const rightBars = useMemo(() => {
      if (!isRecording) return null;
      return [10, 11, 12, 13, 14, 15, 16, 17, 18, 19].map(i => (
        <WaveBar key={`right-${i}`} index={i} isRecording={isRecording} />
      ));
    }, [isRecording]);

    const recordButton = useMemo(
      () => (
        <Pressable onPress={toggleRecording} disabled={disabled || loading}>
          <FastImage
            source={Theme.icons.audio}
            style={styles.icon}
            resizeMode="contain"
          />
          <View
            style={{
              ...StyleSheet.absoluteFillObject,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            {loading ? (
              <ActivityIndicator color="#fff" size="large" />
            ) : (
              <FastImage
                source={Theme.icons.mic}
                style={{width: 20, height: 32}}
                resizeMode="cover"
              />
            )}
          </View>
        </Pressable>
      ),
      [toggleRecording, loading],
    );

    return (
      <View style={styles.container}>
        <View style={styles.waveWrapper}>
          <View style={styles.waveSide}>{leftBars}</View>
          {recordButton}
          <View style={styles.waveSide}>{rightBars}</View>
        </View>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  waveWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  waveSide: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 2,
  },
  icon: {
    width: 80,
    height: 80,
  },
  disabledIcon: {
    opacity: 0.5,
  },
  bar: {
    width: 6,
    backgroundColor: '#fff',
    borderRadius: 4,
  },
});
