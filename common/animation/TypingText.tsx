import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type { TextStyle } from 'react-native';
import { Animated, InteractionManager, Text } from 'react-native';
import {
  countMatchingCharacters,
  delay,
  repeatFunctionNTimes,
} from '../../src/utils/typing';

type CursorProps = {
  blinkSpeed: number | undefined;
  cursorStyle: TextStyle | undefined;
  style: TextStyle | undefined;
};

const Cursor: React.FC<CursorProps> = ({
  blinkSpeed = 500,
  cursorStyle,
  style,
}) => {
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(opacity, {
          toValue: 1,
          duration: 0,
          useNativeDriver: false,
          isInteraction: false,
        }),
        Animated.delay(blinkSpeed),
        Animated.timing(opacity, {
          toValue: 0,
          duration: 0,
          useNativeDriver: false,
          isInteraction: false,
        }),
        Animated.delay(blinkSpeed),
      ]),
    ).start();
  }, [blinkSpeed, opacity]);

  return (
    <Animated.Text style={{opacity, ...style, ...cursorStyle}}>|</Animated.Text>
  );
};

interface BaseSequenceItem {
  delayBetweenSequence?: number;
  deleteCount?: number;
  deletionSpeed?: number;
  typeSpeed?: number;
}

type SequenceItem =
  | (BaseSequenceItem & {text: string; action?: never})
  | (BaseSequenceItem & {action: () => void; text?: never})
  | (BaseSequenceItem & {text: string; action: () => void});

interface TypeAnimationProps {
  sequence: SequenceItem[];
  delayBetweenSequence?: number;
  splitter?: (str: string) => string[];
  repeat?: number;
  loop?: boolean;
  blinkSpeed?: number;
  style?: TextStyle;
  cursorStyle?: TextStyle;
  cursor?: boolean;
  direction?: 'front' | 'back';
  preRenderText?: string;
  initialDelay?: number;
  onCharTyped?: (data: {character: string; currentText: string}) => void;
  onCharDeleted?: (data: {character: string; currentText: string}) => void;
  deletionSpeed?: number;
  typeSpeed?: number;
}

const TypeAnimation: React.FC<TypeAnimationProps> = ({
  sequence,
  delayBetweenSequence = 100,
  splitter = str => str.split(/(?=\S)/),
  repeat = 1,
  loop,
  blinkSpeed,
  style,
  cursorStyle,
  cursor = true,
  direction = 'front',
  preRenderText = '',
  initialDelay = 0,
  onCharTyped,
  onCharDeleted,
  typeSpeed = 100,
  deletionSpeed = 100,
}) => {
  const [text, setText] = useState<string>(preRenderText);
  let currentText = preRenderText;

  const typeLetters = (textToType: string, speed = typeSpeed) => {
    const isFront = direction === 'front';
    const textArray = splitter(textToType);
    if (!isFront) {
      textArray.reverse();
    }
    return new Promise<void>(async resolve => {
      for (const character of textArray) {
        if (isFront) {
          setText(currText => {
            const word = `${currText}${character ?? ''}`;
            if (character) {
              const data = {
                character,
                currentText: word,
              };
              if (onCharTyped) {
                onCharTyped(data);
              }
            }
            return word;
          });
        } else {
          setText(currText => {
            const word = `${character ?? ''}${currText}`;
            if (character) {
              const data = {
                character,
                currentText: word,
              };
              if (onCharTyped) {
                onCharTyped(data);
              }
            }
            return word;
          });
        }
        await delay(speed);
      }
      resolve();
    });
  };

  const deleteLetters = (count: number, speed = deletionSpeed) => {
    return new Promise<void>(resolve => {
      let i = 0;
      const interval = setInterval(() => {
        if (i >= count) {
          clearInterval(interval);
          resolve();
        } else {
          if (direction === 'front') {
            setText(currtext => {
              const word = `${currtext.substring(0, currtext.length - 1)}`;
              const character = currtext[currtext.length - 1];
              if (character) {
                const data = {
                  character,
                  currentText: word,
                };
                if (onCharDeleted) {
                  onCharDeleted(data);
                }
              }
              return word;
            });
          } else {
            setText(currtext => {
              const word = `${currtext.substring(1, currtext.length)}`;
              const character = currtext[0];
              if (character) {
                const data = {
                  character,
                  currentText: word,
                };
                if (onCharDeleted) {
                  onCharDeleted(data);
                }
              }
              return word;
            });
          }
        }
        i++;
      }, speed);
    });
  };

  const runSequence = async () => {
    for (const data of sequence) {
      if (data?.action) {
        data.action();
        if (data?.delayBetweenSequence) {
          await delay(data?.delayBetweenSequence ?? delayBetweenSequence);
        }
      } else if (currentText) {
        const count = countMatchingCharacters(
          currentText,
          data?.text ?? '',
          direction,
        );

        const del = data?.deleteCount ?? currentText.length - count;

        await deleteLetters(del, data?.deletionSpeed);
        if (direction === 'front') {
          currentText =
            currentText.substring(0, currentText.length - del) +
            data?.text?.substring(count, data?.text?.length);
        } else {
          currentText =
            data?.text?.substring(0, data?.text?.length - count) +
            currentText.substring(del, currentText.length);
        }

        await typeLetters(
          direction === 'front'
            ? data?.text?.substring(count, data?.text?.length)
            : data?.text?.substring(0, data?.text?.length - count),
          data?.typeSpeed,
        );
        await delay(data?.delayBetweenSequence ?? delayBetweenSequence);
      } else {
        currentText = data?.text;
        await typeLetters(data?.text, data?.typeSpeed);
        await delay(data?.delayBetweenSequence ?? delayBetweenSequence);
      }
    }
  };

  const firstFunction = useCallback(() => {
    if (loop) {
      const run = async () => {
        await runSequence();
        run();
      };
      run();
    } else {
      repeatFunctionNTimes(runSequence, repeat);
    }
  }, []);

  useEffect(() => {
    const handle = InteractionManager.createInteractionHandle();
    if (initialDelay) {
      setTimeout(() => {
        firstFunction();
      }, initialDelay);
    } else {
      firstFunction();
    }

    return () => InteractionManager.clearInteractionHandle(handle);
  }, []);

  const cursorComponent = useMemo(() => {
    return cursor ? (
      <Cursor blinkSpeed={blinkSpeed} style={style} cursorStyle={cursorStyle} />
    ) : null;
  }, [cursor, blinkSpeed, style, cursorStyle]);

  return (
    <Text style={{...style}}>
      {direction === 'back' && cursorComponent}
      {text}
      {direction === 'front' && cursorComponent}
    </Text>
  );
};

export default TypeAnimation;
