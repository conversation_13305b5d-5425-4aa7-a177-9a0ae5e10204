import {reactotronRedux} from 'reactotron-redux';
import Reactotron from 'reactotron-react-native';

const reactotron = Reactotron.configure({
  name: 'ISpeaking App',
})
  .use(reactotronRedux())
  .useReactNative({networking: true})
  .connect();

if (__DEV__) {
  const originalLog = console.log;
  console.log = (...args) => {
    originalLog(...args);
    Reactotron.log(...args);
  };
}

export default reactotron;
