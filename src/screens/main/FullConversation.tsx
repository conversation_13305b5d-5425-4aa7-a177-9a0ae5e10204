import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {goBack} from '../../navigation/NavigationServices';

type Message = {
  id: number;
  sender: 'teacher' | 'student';
  text: string;
  audio?: boolean;
  translation?: boolean;
  percent?: number;
  corrected?: string;
  translated?: string;
};

const fakeMessages: Message[] = [
  {
    id: 1,
    sender: 'teacher',
    text: 'Hi, how are you today, <PERSON>h <PERSON>?',
    audio: true,
    translation: true,
  },
  {
    id: 2,
    sender: 'student',
    text: "I'm good, thank you. How [does] you do?",
    percent: 85,
    corrected: 'How do you do?',
    translated: 'Bạn thế nào?',
  },
  {
    id: 3,
    sender: 'teacher',
    text: 'What did you do at school today?',
    audio: true,
    translation: true,
  },
];

type MessageBubbleProps = {
  message: Message;
};

const MessageBubble: React.FC<MessageBubbleProps> = ({message}) => {
  const isTeacher = message.sender === 'teacher';
  const bubbleStyle = isTeacher ? styles.teacherBubble : styles.studentBubble;

  return (
    <View style={[styles.messageBubble, bubbleStyle]}>
      <Text style={styles.messageText}>{message.text}</Text>
      {message.audio && <Text style={styles.iconFallback}>🔊</Text>}
      {message.translation && <Text style={styles.iconFallback}>🌐</Text>}
      {message.percent !== undefined && (
        <Text style={styles.percentText}>✔ {message.percent}%</Text>
      )}
    </View>
  );
};

type CorrectionBoxProps = {
  corrected: string;
  translated?: string;
};

const CorrectionBox: React.FC<CorrectionBoxProps> = ({
  corrected,
  translated,
}) => (
  <View style={styles.correctionBox}>
    <Text style={styles.correctionTitle}>It's better to say:</Text>
    <Text style={styles.correctionText}>{corrected}</Text>
    {translated && <Text style={styles.translatedText}>{translated}</Text>}
    <Text style={styles.iconFallback}>🔊 🌐</Text>
  </View>
);

export const FullConversation: React.FC = () => {
  return (
    <SafeAreaView edges={['top']} style={styles.container}>
      <View style={styles.board}>
        <TouchableOpacity style={styles.closeButton} onPress={goBack}>
          <Text style={styles.iconFallback}>❌</Text>
        </TouchableOpacity>
        <Text style={styles.header}>FULL CONVERSATION</Text>

        <ScrollView style={styles.scrollArea}>
          {fakeMessages.map((msg: Message) => (
            <View key={msg.id}>
              <MessageBubble message={msg} />
              {msg.corrected && (
                <CorrectionBox
                  corrected={msg.corrected}
                  translated={msg.translated}
                />
              )}
            </View>
          ))}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fef3c7',
    alignItems: 'center',
    paddingTop: 16,
  },
  board: {
    width: '90%',
    height: '95%',
    backgroundColor: '#065f46',
    borderRadius: 16,
    alignItems: 'center',
    paddingTop: 16,
    paddingHorizontal: 16,
    paddingBottom: 24,
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: 12,
    left: 12,
    zIndex: 10,
  },
  header: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  scrollArea: {
    width: '100%',
  },
  messageBubble: {
    borderRadius: 16,
    padding: 12,
    marginVertical: 6,
    maxWidth: '80%',
  },
  teacherBubble: {
    backgroundColor: '#ffedd5',
    alignSelf: 'flex-start',
  },
  studentBubble: {
    backgroundColor: '#fdba74',
    alignSelf: 'flex-end',
  },
  messageText: {
    fontSize: 16,
    color: '#000',
  },
  percentText: {
    color: '#15803d',
    marginTop: 4,
  },
  correctionBox: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: '#fb923c',
    padding: 12,
    borderRadius: 16,
    marginVertical: 6,
    maxWidth: '80%',
    alignSelf: 'flex-start',
  },
  correctionTitle: {
    color: '#1f2937',
  },
  correctionText: {
    color: '#000',
    fontWeight: '600',
  },
  translatedText: {
    color: '#16a34a',
    marginTop: 4,
  },
  iconFallback: {
    marginTop: 4,
    fontSize: 16,
  },
});
