import React, {useRef} from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  View,
  ActivityIndicator,
} from 'react-native';
import {scale, verticalScale} from 'react-native-size-matters';
import {SvgIcons} from '../../../assets/svg/index.tsx';
import CustomModal from '../../components/CustomModal.tsx';
import {RolePlayBackground} from '../../components/RolePlay/RolePlayBackground.tsx';
import {RolePlayCardList} from '../../components/RolePlay/RolePlayCardList.tsx';
import {RolePlayContentModal} from '../../components/RolePlay/RolePlayContentModal.tsx';
import {RolePlayMissionView} from '../../components/RolePlay/RolePlayMissionView.tsx';
import {RolePlayQuitModal} from '../../components/RolePlay/RolePlayQuitModal.tsx';

import {SpeechBubble} from '../../components/RolePlay/RolePlayBubble.tsx';
import {
  OptimizedScrollCurtainMemo,
  OptimizedScrollCurtainRef,
} from '../../components/RolePlay/OptimizedScrollCurtain.tsx';
import {useRolePlay} from '../../hooks/useRolePlay.ts';
import {Theme} from '../../themes/index.ts';
import {
  heightScreen,
  HIT_SLOP,
  initTop,
  isAndroid,
  widthScreen,
} from '../../utils/Scale.ts';
import TextApp from '../../components/TextApp/index.tsx';
import FastImage from 'react-native-fast-image';

const RolePlay: React.FC = () => {
  const scrollCurtainRef = useRef<OptimizedScrollCurtainRef>(null);

  const {
    modalVisible,
    modalContent,
    showMission,
    quitModal,
    ccActive,
    ideaActive,
    startNowLoading,
    recordingLoading,
    speechBubbleRef,
    rolePlayResponse,
    trasnY,
    opacity,
    rolePlayData,
    apiLoading,
    recording,
    coins,
    handleChooseCard,
    handleCloseModal,
    closeQuitModal,
    handleQuit,
    onBackPress,
    handleStartNow,
    handleCCPress,
    handleIdeaPress,
    handleRecordingCallback,
    handleRandomCard,
    startIdleTimeout,
    cancelIdleTimeout,
  } = useRolePlay(scrollCurtainRef);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.back}
          onPress={onBackPress}
          hitSlop={HIT_SLOP}>
          <SvgIcons.ArrowLeft />
        </TouchableOpacity>
        <View style={styles.pear}>
          <FastImage source={Theme.icons.gem} style={styles.pearlIcon} />
          <TextApp
            text={coins}
            preset="text_md_regular"
            textColor="#fff"
            style={{marginLeft: 4, lineHeight: 24}}
          />
        </View>
      </View>

      <RolePlayBackground
        modalContent={modalContent}
        trasnY={trasnY}
        opacity={opacity}
      />

      {showMission ? (
        <RolePlayMissionView
          ccActive={ccActive}
          ideaActive={ideaActive}
          apiLoading={apiLoading}
          recordingLoading={recordingLoading}
          recording={recording}
          onCCPress={handleCCPress}
          onIdeaPress={handleIdeaPress}
          onRecordingCallback={handleRecordingCallback}
          scrollCurtainRef={scrollCurtainRef}
          answerText={rolePlayResponse?.answerText || ''}
          scores={rolePlayResponse?.pronunciationScore}
          audio={rolePlayResponse?.answerPath}
          disable={rolePlayResponse?.status === 1}
        />
      ) : (
        <RolePlayCardList
          rolePlayData={rolePlayData}
          onChooseCard={handleChooseCard}
          isLoading={apiLoading}
          handleRandomCard={handleRandomCard}
        />
      )}

      <CustomModal
        visible={modalVisible}
        children={
          <RolePlayContentModal
            modalContent={modalContent}
            onClose={handleCloseModal}
            onStartNow={handleStartNow}
          />
        }
      />

      <RolePlayQuitModal
        visible={quitModal}
        onClose={closeQuitModal}
        onQuit={handleQuit}
      />

      <SpeechBubble
        ref={speechBubbleRef}
        startIdleTimeout={startIdleTimeout}
        cancelIdleTimeout={cancelIdleTimeout}
      />

      <OptimizedScrollCurtainMemo
        ref={scrollCurtainRef}
        missions={modalContent?.mission || []}
        enableGesture={showMission}
      />

      {startNowLoading && (
        <View style={styles.loadingOverlay} pointerEvents="auto">
          <View style={styles.loadingBox}>
            <ActivityIndicator size="large" color="#535862" />
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: widthScreen,
    height: heightScreen,
  },
  header: {
    flex: 1,
    width: '100%',
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    top: isAndroid ? verticalScale(30) : initTop - 10,
    zIndex: 3,
  },
  back: {
    width: 36,
    height: 36,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: Theme.radius.radius_md,
    borderWidth: 1,
    borderColor: '#D5D7DA',
  },
  pear: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pearlIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  },
  loadingBox: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default RolePlay;
