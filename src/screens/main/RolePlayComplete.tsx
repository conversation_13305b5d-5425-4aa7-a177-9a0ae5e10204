import React, {useEffect, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {Theme} from '../../themes';
import Animated<PERSON>ottieView from 'lottie-react-native';
import {
  heightScreen,
  HIT_SLOP,
  initTop,
  isAndroid,
  widthScreen,
} from '../../utils/Scale';
import {goBack} from '../../navigation/NavigationServices';
import {SvgIcons} from '../../../assets/svg';
import {scale, verticalScale} from 'react-native-size-matters';
import {
  Canvas,
  useFont,
  Text as SkiaText,
  Image as SkiaImage,
  Group,
  useImage,
} from '@shopify/react-native-skia';
import TextApp from '../../components/TextApp';
import {useReduxDispatch, useTypedSelector} from '../../redux/store';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {APP_SCREEN, RootStackParamList} from '../../navigation/screenType';
import {Icons} from '../../themes/icons';
import {fetchDataInfo} from '../../redux/reducer/fetchData';

type RolePlayCompleteProps = NativeStackScreenProps<
  RootStackParamList,
  APP_SCREEN.ROLE_PLAY_COMPLETED
>;

export const RolePlayComplete: React.FC<RolePlayCompleteProps> = ({route}) => {
  const {coins} = route.params;
  const dispatch = useReduxDispatch();
  const {data} = useTypedSelector(state => state.profile);
  const [showLottie, setShowLottie] = useState<boolean>(false);

  const fontBig = useFont(
    require('../../../assets/fonts/SFRounded-Bold.ttf'),
    48,
  );
  const fontMedium = useFont(
    require('../../../assets/fonts/SFRounded-Bold.ttf'),
    30,
  );
  const fontSmall = useFont(
    require('../../../assets/fonts/SFRounded-Bold.ttf'),
    20,
  );

  const skillImage = useImage(Icons.gem);

  useEffect(() => {
    dispatch(fetchDataInfo());
    const timeout = setTimeout(() => {
      setShowLottie(true);
    }, 1500);

    return () => clearTimeout(timeout);
  }, []);

  return (
    <View style={{flex: 1}}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.back}
          onPress={goBack}
          hitSlop={HIT_SLOP}>
          <SvgIcons.ArrowLeft />
        </TouchableOpacity>
        <View style={styles.pear}>
          <FastImage source={Theme.icons.gem} style={styles.pearlIcon} />
          <TextApp
            text={data?.coins}
            preset="text_md_regular"
            textColor="#fff"
            style={{marginLeft: 4, lineHeight: 24}}
          />
        </View>
      </View>
      <FastImage
        source={Theme.images.bgRolePlayCompleted}
        resizeMode="stretch"
        style={{width: widthScreen, height: heightScreen, position: 'absolute'}}
      />
      <Canvas style={{width: '100%', height: '100%'}}>
        <Group transform={[{scale: widthScreen / 375}]}>
          <SkiaText
            x={145}
            y={293}
            text={'3/3'}
            font={fontBig}
            color="#FF0000"
          />
          <SkiaText
            x={145}
            y={355}
            text={'+50'}
            font={fontMedium}
            color="#680000"
          />
          <SkiaText
            x={210}
            y={355}
            text={'Exp'}
            font={fontSmall}
            color="#181D27"
          />
          <SkiaText
            x={145}
            y={425}
            text={`+${coins}`}
            font={fontMedium}
            color="#680000"
          />
          <SkiaImage
            image={skillImage}
            x={220}
            y={402}
            width={24.3189}
            height={24.4038}
          />
        </Group>
      </Canvas>
      <View style={styles.absoluteView}>
        {showLottie && (
          <AnimatedLottieView
            source={require('../../../assets/lotties/congrats.json')}
            style={styles.lottie}
            autoPlay
            speed={0.7}
          />
        )}
      </View>
    </View>
  );
};
// ...existing code...
// Di chuyển styles xuống cuối file
const styles = StyleSheet.create({
  lottie: {
    width: widthScreen,
    height: heightScreen,
  },
  absoluteView: {
    ...StyleSheet.absoluteFillObject,
    width: widthScreen,
    height: '85%',
    top: 0,
  },
  header: {
    flex: 1,
    width: '100%',
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    top: isAndroid ? verticalScale(30) : initTop,
    zIndex: 1,
  },
  back: {
    width: 36,
    height: 36,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: Theme.radius.radius_md,
    borderWidth: 1,
    borderColor: '#D5D7DA',
  },
  pear: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pearlIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
});
