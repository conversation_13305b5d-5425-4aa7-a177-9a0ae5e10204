import React from 'react';
import Header from '../../../components/Header.tsx';
import {ImageBackground, StyleSheet} from 'react-native';
import {heightScreen, initTop, isAndroid} from '../../../utils/Scale.ts';
import TextApp from '../../../components/TextApp';
import {verticalScale} from 'react-native-size-matters';

const NotificationSetting: React.FC = () => {
  return (
    <ImageBackground
      resizeMode={'stretch'}
      source={require('../../../../assets/images/settings/bgsetting.webp')}
      style={styles.container}>
      <Header title={''} />
      <TextApp
        text={'Notification'}
        preset={'text_xl_semibold'}
        textColor={'#395500'}
        style={{
          position: 'absolute',
          textAlign: 'center',
          width: '100%',
          top: isAndroid ? verticalScale(38) : initTop + 6,
        }}
      />
    </ImageBackground>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: heightScreen + (isAndroid ? 0 : 130),
  },
  boxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
  },
});
export default NotificationSetting;
