import React, {useState} from 'react';
import Header from '../../../components/Header.tsx';
import {ImageBackground, StyleSheet, View} from 'react-native';
import {
  heightScreen,
  initTop,
  isAndroid,
  widthScreen,
} from '../../../utils/Scale.ts';
import TextApp from '../../../components/TextApp';
import {
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import {KeyboardAwareScrollView} from 'react-native-keyboard-controller';
import Spacer from '../../../components/Spacer.tsx';
import Input from '../../../components/Input.tsx';
import Animated, {SlideInRight} from 'react-native-reanimated';
import {PasswordRule} from '../../../components/PasswordRule.tsx';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useForgotPassword} from '../../../hooks/unAuth/useForgotPassword.ts';
import {useTranslate} from '../../../hooks/useTranslate.ts';
import {useTheme} from '../../../hooks/useTheme.ts';

import ImageButton from '../../../components/ImageButton.tsx';
import {fetchUpdateProfile} from '../../../redux/reducer/fetchData.ts';
import Toast from 'react-native-toast-message';
import {useReduxDispatch, useTypedSelector} from '../../../redux/store.ts';
import {goBack} from '../../../navigation/NavigationServices.ts';

const ChangePassword: React.FC = () => {
  const {
    status,
    formData,
    errors,
    onChangeTextInput,
    onFocusInput,
    onBlurInput,
    rules,
    loading,
  } = useForgotPassword();
  const {t} = useTranslate();
  const theme = useTheme();
  const dispatch = useReduxDispatch();
  const [currentPassword, setCurrentPassword] = useState('');
  const isPasswordValid =
    rules?.validateLength && rules?.hasNumber && rules?.isMatch;
  const data: any = useTypedSelector(state => state.profile.data);
  const isDisabledResetPassword =
    !currentPassword ||
    !formData.password ||
    !formData.confirmPassword ||
    !isPasswordValid;

  const handleSubmit = async () => {
    const payload = {
      currentPassword: currentPassword,
      password: formData.password,
    };
    const res = await dispatch(
      fetchUpdateProfile({
        data: payload,
        id: data?.id,
      }),
    );
    if (res?.payload?.code === 0) {
      Toast.show({text1: 'updated successfully', type: 'customSuccess'});
      goBack();
      return;
    } else if (res?.payload?.code === 1) {
      Toast.show({text1: `${res?.payload?.msgCode}`, type: 'customError'});
    }
  };
  return (
    <ImageBackground
      resizeMode={'stretch'}
      source={require('../../../../assets/images/settings/bgsetting.webp')}
      style={styles.container}>
      <Header title={''} />
      <TextApp
        text={'Change password'}
        preset={'text_xl_semibold'}
        textColor={'#395500'}
        style={{
          position: 'absolute',
          textAlign: 'center',
          width: '100%',
          top: isAndroid ? verticalScale(38) : initTop + 6,
        }}
      />
      <SafeAreaView style={styles.content}>
        <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
          <Animated.View
            key={status}
            entering={SlideInRight}
            style={styles.centerBox}>
            <Input
              label={'Current password'}
              isPassword
              placeholder={''}
              value={currentPassword}
              onChangeText={val => setCurrentPassword(val)}
              error={t(errors.password)}
              inputStyle={{
                borderColor: !currentPassword
                  ? theme.border_error
                  : theme.border_primary,
                backgroundColor: '#FFFFFF',
              }}
            />
            <Input
              label={t('forgot.createNewPassword.passwordLabel')}
              isPassword
              placeholder={t('forgot.createNewPassword.passwordPlaceholder')}
              value={formData?.password}
              onChangeText={val => onChangeTextInput('password', val)}
              onFocus={() => onFocusInput('password')}
              onBlur={() => onBlurInput('password')}
              error={t(errors.password)}
              inputStyle={{
                borderColor:
                  isDisabledResetPassword && formData?.password
                    ? theme.border_error
                    : theme.border_primary,
                backgroundColor: '#FFFFFF',
              }}
            />

            <Spacer size={5} />
            <Input
              label={t('forgot.createNewPassword.confirmPassword')}
              isPassword
              placeholder={t('forgot.createNewPassword.confirmPassword')}
              value={formData?.confirmPassword}
              onChangeText={val => onChangeTextInput('confirmPassword', val)}
              onFocus={() => onFocusInput('confirmPassword')}
              onBlur={() => onBlurInput('confirmPassword')}
              error={t(errors.confirmPassword)}
              inputStyle={{
                borderColor: isDisabledResetPassword
                  ? theme.border_error
                  : theme.border_primary,
                backgroundColor: '#FFFFFF',
              }}
            />
            <Spacer size={5} />
            <PasswordRule
              validateLength={rules?.validateLength}
              hasNumber={rules?.hasNumber}
              isMatch={rules?.isMatch}
            />
          </Animated.View>
        </KeyboardAwareScrollView>
        {loading && <View style={styles.overlay} />}
        <View
          style={{
            position: 'absolute',
            width: widthScreen,
            justifyContent: 'center',

            bottom: 50,
          }}>
          <ImageButton
            disabled={isDisabledResetPassword}
            onPress={() => handleSubmit()}
            title="Update"
            source={require('../../../../assets/images/btnUpdate.webp')}
            widthVal="100%"
          />
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: heightScreen + (isAndroid ? 0 : 130),
  },
  boxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
  },
  content: {
    flex: 1,
    paddingTop: 80,
    paddingHorizontal: 36,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: scale(16),
    marginTop: isAndroid ? verticalScale(5) : 0,
  },
  nameApp: {
    textAlign: 'center',
  },
  inputBox: {
    marginTop: moderateVerticalScale(32),
    marginHorizontal: scale(8),
  },
  mt: {
    height: moderateVerticalScale(48),
    borderWidth: 1,
  },
  centerBox: {
    paddingHorizontal: scale(16),
    paddingTop: verticalScale(30),
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'transparent',
    zIndex: 9999,
  },
  inputStyle: {
    backgroundColor: '#FFFFFF',
  },
});
export default ChangePassword;
