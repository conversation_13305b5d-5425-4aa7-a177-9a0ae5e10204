import React, {useEffect, useRef, useState} from 'react';
import {Image, ImageBackground, StyleSheet, View} from 'react-native';
import {moderateScale, moderateVerticalScale} from 'react-native-size-matters';
import {SvgIcons} from '../../../assets/svg';
import Header from '../../components/Header';
import {useReduxDispatch, useTypedSelector} from '../../redux/store';
import {Theme} from '../../themes';
import {heightScreen, isAndroid, widthScreen} from '../../utils/Scale';
import {goBack, navigate} from '../../navigation/NavigationServices';
import {APP_SCREEN} from '../../navigation/screenType';
import TextApp from '../../components/TextApp';
import {Images} from '../../themes/images';
import Animated, {SlideInUp, SlideOutUp} from 'react-native-reanimated';
import BoardProfile from '../../components/BoardProfile.tsx';
import SvgCharacter from '../../../assets/svg/profile/SvgCharacter.tsx';

import ViewShot from 'react-native-view-shot';
import {
  fetchSaveAssetsWardrobe,
  fetchUpdateAvatar,
} from '../../redux/reducer/fetchData.ts';
import FastImage from 'react-native-fast-image';
import {getImages} from '../../utils/getImages.ts';

const getImageByTypeAndId = (data: any, key: string, id?: number | string) => {
  if (!data || !id) {
    return;
  }
  const assets = data.find((item: any) => item.typeCode === key)?.wardrobes;
  return assets.find((item: any) => item.id === id)?.path;
};
const scaleX = widthScreen / 1125;
const scaleY = heightScreen / 2436;
const BoardName = ({name, lv}: {name: string; lv: string}) => (
  <Animated.View
    entering={SlideInUp.duration(1000)}
    exiting={SlideOutUp.duration(1000)}
    style={styles.boardNameContainer}>
    <Image
      source={Images.boardName}
      style={styles.boardNameImage}
      resizeMode="stretch"
    />
    <TextApp
      text={name}
      style={styles.boardNameText}
      preset="text_md_semibold"
      textColor="#6A2000"
    />
    <TextApp
      text={lv}
      style={styles.boardLevelText}
      preset="text_sm_semibold"
      textColor="#6A2000"
    />
  </Animated.View>
);

const Profile: React.FC = () => {
  const viewShotRef: any = useRef<ViewShot>(null);
  const dispatch = useReduxDispatch();
  const data = useTypedSelector(state => state.profile.data);
  const character = useTypedSelector(state => state.profile.character);
  const [checkUpdate, setCheckUpdate] = useState(false);
  const prevCharacter = useRef(JSON.stringify(character));

  const Assets = useTypedSelector(state => state.profile.assetsCharacter);
  useEffect(() => {
    if (JSON.stringify(character) !== prevCharacter.current) {
      setCheckUpdate(true);
    }
  }, [character]);
  const handleCapture = async () => {
    try {
      const uri = await viewShotRef.current.capture();
      dispatch(fetchUpdateAvatar({fileUri: uri, id: data?.id}));
      dispatch(
        fetchSaveAssetsWardrobe({
          wardrobeIds: [
            character.faceId,
            character.pantsId,
            character.shirtId,
            character.accessoryId,
            character.petsId,
          ],
        }),
      );
    } catch (err) {
      console.error('Capture error:', err);
    }
  };
  return (
    <ImageBackground
      style={styles.container}
      source={Theme.images.bgProfile}
      resizeMode="stretch">
      <Header
        title={''}
        rightIcon={<SvgIcons.Setting />}
        onBackPress={() => {
          goBack();
          if (checkUpdate) {
            handleCapture();
          }
        }}
        onPressRight={() => navigate(APP_SCREEN.SETTINGS)}
      />
      <BoardName name={data?.name || ''} lv={data?.userLevelName || ''} />
      <View style={styles.characterWrapper}>
        <ViewShot ref={viewShotRef}>
          <SvgCharacter
            accessory={getImageByTypeAndId(
              Assets,
              'GLASSES',
              character?.accessoryId,
            )}
            pants={getImageByTypeAndId(Assets, 'PANTS', character?.pantsId)}
            shirt={getImageByTypeAndId(Assets, 'SHIRT', character?.shirtId)}
            face={getImageByTypeAndId(Assets, 'FACE', character?.faceId)}
          />
        </ViewShot>
        <FastImage
          source={
            character?.petsId
              ? getImages(
                  getImageByTypeAndId(Assets, 'PET', character.petsId),
                  true,
                )
              : require('../../../assets/svg/profile/snake.webp')
          }
          style={styles.petImg}
          resizeMode={'stretch'}
        />
      </View>

      <BoardProfile
        streak="01"
        pearl={data?.coins || ''}
        exp={data?.exp || ''}
      />
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    width: widthScreen,
    height: heightScreen,
  },
  characterWrapper: {
    alignSelf: 'center',
    marginTop: 12 * scaleY,
    marginBottom: 120 * scaleY,
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  avatar: {
    width: 117,
    height: 161,
  },
  boardNameContainer: {
    alignItems: 'center',
  },
  boardNameImage: {
    width: moderateScale(200),
    height: moderateVerticalScale(106),
  },
  boardNameText: {
    position: 'absolute',
    bottom: isAndroid ? 20 : 26,
  },
  boardLevelText: {
    position: 'absolute',
    bottom: isAndroid ? 2 : 12,
  },

  achievementBoard: {
    width: '100%',
    flex: 1,
    marginTop: 16,
  },

  logoutButton: {
    backgroundColor: '#FF8612',
    height: 48,
    borderRadius: Theme.radius.radius_md,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Theme.colors.border,
  },
  logoutText: {
    textAlign: 'center',
    fontWeight: '600',
  },
  petImg: {
    height: 149 * scaleY,
    width: 134 * scaleX,
  },
});

export default Profile;
