import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  ListRenderItem,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import {SvgIcons} from '../../../assets/svg';
import Spacer from '../../components/Spacer';
import TextApp from '../../components/TextApp';
import {useTheme} from '../../hooks/useTheme';
import {goBack, navigate} from '../../navigation/NavigationServices';
import {APP_SCREEN} from '../../navigation/screenType';
import {
  fetchListNotification,
  fetchReadMessage,
  fetchClassDetail,
} from '../../redux/reducer/fetchData';
import {useReduxDispatch} from '../../redux/store';
import {Theme} from '../../themes';
import {HIT_SLOP, initTop, isAndroid, SCREEN_WIDTH} from '../../utils/Scale';
import moment from 'moment';
import SVGNotification from '../../../assets/svg/notification/Notfication';

export const Notification: React.FC = () => {
  const theme = useTheme();
  const [data, setData] = useState<NotificationItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const dispatch = useReduxDispatch();

  useEffect(() => {
    fetchNotification();
  }, []);

  const fetchNotification = async () => {
    const result = await dispatch(fetchListNotification({page: 0, size: 10}));
    if (fetchListNotification.fulfilled.match(result)) {
      setData(result?.payload?.data);
    }
    setLoading(false);
  };

  const handleDetailNotification = async (item: NotificationItem) => {
    const classId = JSON.parse(item?.info).classId;
    if (item?.readMessage !== 1) {
      await dispatch(fetchReadMessage({id: item?.id}));
      setData(prev =>
        prev.map(noti =>
          noti.id === item.id ? {...noti, readMessage: 1} : noti,
        ),
      );
    }
    const result = await dispatch(fetchClassDetail({classId}));

    if (fetchClassDetail.fulfilled.match(result)) {
      const {code, data} = result?.payload || {};
      if (code === 0) {
        navigate(APP_SCREEN.YOUR_CLASS, {
          classId,
          classData: data,
        });
      }
    }
  };

  const renderItem: ListRenderItem<NotificationItem> = ({item}) => {
    const isReaded = item?.readMessage === 1;
    return (
      <>
        <TouchableOpacity
          style={[styles.itemContainer, !isReaded && styles.highlightedItem]}
          onPress={handleDetailNotification?.bind(null, item)}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <TextApp
              preset="text_s_regular"
              text={moment(item?.createdDate, 'DD/MM/YYYY HH:mm:ss').format(
                'HH:mm DD/MM/YYYY',
              )}
              textColor={theme.text_pleaceholder_subtle}
            />
            {!isReaded && <SvgIcons.Bell fill={'#EFCD62'} />}
          </View>
          <View style={styles.title}>
            <TextApp
              preset="text_sm_regular"
              text={item?.content}
              style={{lineHeight: 20}}
              textColor={theme.text_white}
            />
          </View>
          <Spacer size={4} />
        </TouchableOpacity>
        {isReaded && <View style={styles.separator} />}
      </>
    );
  };
  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.back} onPress={goBack} hitSlop={HIT_SLOP}>
        <SvgIcons.ArrowLeft />
      </TouchableOpacity>
      <SVGNotification
        children={
          <>
            {loading ? (
              <View style={styles.loading}>
                <ActivityIndicator size={'large'} color={'#fff'} />
              </View>
            ) : (
              <FlatList
                data={data}
                keyExtractor={item => item.id}
                renderItem={renderItem}
                showsVerticalScrollIndicator={false}
              />
            )}
          </>
        }
      />
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2DD86',
  },
  back: {
    width: 36,
    height: 36,
    position: 'absolute',
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: Theme.radius.radius_md,
    borderWidth: 1,
    borderColor: '#D5D7DA',
    left: scale(16),
    top: isAndroid ? verticalScale(30) : initTop,
    zIndex: 1,
  },
  wrapper: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    position: 'absolute',
  },
  itemContainer: {
    borderRadius: Theme.radius.radius_xl,
    paddingHorizontal: scale(Theme.spacing.spacing_2xl),
    paddingVertical: scale(Theme.spacing.spacing_lg),
  },
  highlightedItem: {
    backgroundColor: '#5F7D2B',
    marginTop: moderateVerticalScale(12),
  },
  separator: {
    height: 1,
    backgroundColor: '#6A8A2C',
  },
  title: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: moderateVerticalScale(6),
  },
  loading: {
    marginTop: moderateVerticalScale(SCREEN_WIDTH / 2.5),
    justifyContent: 'center',
    alignItems: 'center',
  },
});
