import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import Animated, {SlideInRight, SlideOutRight} from 'react-native-reanimated';
import {Theme} from '../../themes';
import {KeyboardAwareScrollView} from 'react-native-keyboard-controller';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import {SvgIcons} from '../../../assets/svg/index.tsx';
import Button from '../../components/Button.tsx';
import {ChangeLanguageButton} from '../../components/ChangeLanguageButton.tsx';
import Input from '../../components/Input.tsx';
import {PasswordRule} from '../../components/PasswordRule.tsx';
import Spacer from '../../components/Spacer.tsx';
import TextApp from '../../components/TextApp/index.tsx';
import {OtpInput} from '../../components/form/OtpInput.tsx';
import {useForgotPassword} from '../../hooks/unAuth/useForgotPassword.ts';
import {useTheme} from '../../hooks/useTheme.ts';
import {useTranslate} from '../../hooks/useTranslate.ts';
import {goBack} from '../../navigation/NavigationServices.ts';
import {HIT_SLOP, isAndroid} from '../../utils/Scale.ts';
import {isValidEmail} from '../../utils/index.ts';

const ForgotPassword: React.FC = () => {
  const {
    otpInputRef,
    status,
    formData,
    focused,
    errors,
    handleVerifyOTP,
    onChangeTextInput,
    onFocusInput,
    onBlurInput,
    handleSendOTPCode,
    handleReSendOTP,
    handleCreateNewPassword,
    rules,
    loading,
  } = useForgotPassword();
  const {t} = useTranslate();
  const theme = useTheme();

  const isDisabled = !formData?.email || !isValidEmail(formData.email);

  const isPasswordValid =
    rules?.validateLength && rules?.hasNumber && rules?.isMatch;

  const isDisabledResetPassword =
    !formData.password || !formData.confirmPassword || !isPasswordValid;

  return (
    <SafeAreaView style={styles.conatiner}>
      <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <TouchableOpacity
            onPress={goBack}
            hitSlop={HIT_SLOP}
            style={{
              paddingTop: isAndroid ? scale(10) : 0,
            }}>
            <SvgIcons.ArrowLeft />
          </TouchableOpacity>
          <ChangeLanguageButton />
        </View>
        {status === 'first' && (
          <View style={styles.centerBox}>
            <TextApp
              text={t('forgot.title')}
              preset="display_sm_semibold"
              style={styles.nameApp}
              textColor={theme.text_primary}
            />
            <Spacer size={4} />
            <TextApp
              text={t('forgot.subtitle')}
              preset="text_sm_regular"
              style={[styles.nameApp, {marginHorizontal: scale(50)}]}
              textColor={theme.text_quaternary}
            />
            <View style={styles.inputBox}>
              <Input
                label={t('forgot.emailLabel')}
                iconLeft={<SvgIcons.Mail />}
                placeholder={t('forgot.emailPlaceholder')}
                value={formData?.email}
                onChangeText={val => onChangeTextInput('email', val)}
                onFocus={() => onFocusInput('email')}
                onBlur={() => onBlurInput('email')}
                error={t(errors.email)}
                keyboardType="email-address"
                autoCapitalize="none"
                inputStyle={{
                  borderColor: errors.email
                    ? theme.border_error
                    : focused?.email
                      ? theme.border_brand
                      : theme.border_primary,
                }}
              />
              <Spacer size={20} />
              <Button
                title={t('forgot.sendCode')}
                onPress={handleSendOTPCode}
                disabled={isDisabled}
                style={[
                  styles.mt,
                  {
                    backgroundColor: isDisabled
                      ? theme.bg_disabled
                      : theme.bg_brand_solid,
                    borderColor: isDisabled
                      ? theme.border_disabled_subtle
                      : theme.bg_brand_solid,
                  },
                ]}
                textColor={isDisabled ? theme.fg_disabled : theme.text_white}
                loading={loading}
              />
            </View>
          </View>
        )}
        {status === 'second' && (
          <Animated.View
            key={status}
            entering={SlideInRight}
            style={styles.centerBox}>
            <TextApp
              text={t('forgot.otp.title')}
              preset="display_sm_semibold"
              style={styles.nameApp}
              textColor={theme.text_primary}
            />
            <Spacer size={4} />
            <TextApp
              text={t('forgot.otp.description')}
              preset="text_sm_regular"
              style={[styles.nameApp, {marginHorizontal: scale(50)}]}
              textColor={theme.text_quaternary}
            />
            <Spacer size={32} />
            <OtpInput
              ref={otpInputRef}
              loading={loading}
              handleSubmitOTP={handleVerifyOTP}
              handleResendOTP={handleReSendOTP}
            />
          </Animated.View>
        )}
        {status === 'third' && (
          <Animated.View
            key={status}
            entering={SlideInRight}
            style={styles.centerBox}>
            <TextApp
              text={t('forgot.createNewPassword.title')}
              preset="display_sm_semibold"
              style={styles.nameApp}
              textColor={theme.text_primary}
            />
            <Spacer size={4} />
            <TextApp
              text={t('forgot.createNewPassword.description')}
              preset="text_sm_regular"
              style={[styles.nameApp, {marginHorizontal: scale(50)}]}
              textColor={theme.text_quaternary}
            />
            <Spacer size={32} />
            <Input
              label={t('forgot.createNewPassword.passwordLabel')}
              isPassword
              placeholder={t('forgot.createNewPassword.passwordPlaceholder')}
              value={formData?.password}
              onChangeText={val => onChangeTextInput('password', val)}
              onFocus={() => onFocusInput('password')}
              onBlur={() => onBlurInput('password')}
              error={t(errors.password)}
              inputStyle={{
                borderColor: errors.password
                  ? theme.border_error
                  : focused?.password
                    ? theme.border_brand
                    : theme.border_primary,
              }}
            />

            <Spacer size={5} />
            <Input
              label={t('forgot.createNewPassword.confirmPassword')}
              isPassword
              placeholder={t('forgot.createNewPassword.confirmPassword')}
              value={formData?.confirmPassword}
              onChangeText={val => onChangeTextInput('confirmPassword', val)}
              onFocus={() => onFocusInput('confirmPassword')}
              onBlur={() => onBlurInput('confirmPassword')}
              error={t(errors.confirmPassword)}
              inputStyle={{
                borderColor: errors.confirmPassword
                  ? theme.border_error
                  : focused?.confirmPassword
                    ? theme.border_brand
                    : theme.border_primary,
              }}
            />
            <Spacer size={5} />
            <PasswordRule
              validateLength={rules?.validateLength}
              hasNumber={rules?.hasNumber}
              isMatch={rules?.isMatch}
            />
            <Spacer size={12} />
            <Spacer size={20} />
            <Button
              title={t('forgot.createNewPassword.submit')}
              onPress={handleCreateNewPassword}
              disabled={isDisabledResetPassword}
              style={[
                styles.mt,
                {
                  backgroundColor: isDisabledResetPassword
                    ? theme.bg_disabled
                    : theme.bg_brand_solid,
                  borderColor: isDisabledResetPassword
                    ? theme.border_disabled_subtle
                    : theme.bg_brand_solid,
                },
              ]}
              textColor={
                isDisabledResetPassword ? theme.fg_disabled : theme.text_white
              }
              loading={loading}
            />
          </Animated.View>
        )}
      </KeyboardAwareScrollView>
      {loading && <View style={styles.overlay} />}
    </SafeAreaView>
  );
};
const styles = StyleSheet.create({
  conatiner: {
    flex: 1,
    backgroundColor: Theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: scale(16),
    marginTop: isAndroid ? verticalScale(5) : 0,
  },
  nameApp: {
    textAlign: 'center',
  },
  inputBox: {
    marginTop: moderateVerticalScale(32),
    marginHorizontal: scale(8),
  },
  mt: {
    height: moderateVerticalScale(48),
    borderWidth: 1,
  },
  centerBox: {
    paddingHorizontal: scale(16),
    paddingTop: verticalScale(30),
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'transparent',
    zIndex: 9999,
  },
});
export default ForgotPassword;
