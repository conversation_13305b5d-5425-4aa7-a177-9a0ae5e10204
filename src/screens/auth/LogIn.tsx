import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import { SvgIcons } from '../../../assets/svg/index.tsx';
import Button from '../../components/Button.tsx';
import { ChangeLanguageButton } from '../../components/ChangeLanguageButton.tsx';
import Checkbox from '../../components/Checkbox.tsx';
import Input from '../../components/Input.tsx';
import Spacer from '../../components/Spacer.tsx';
import TextApp from '../../components/TextApp/index.tsx';
import { useLogin } from '../../hooks/unAuth/useLogin.ts';
import { useTheme } from '../../hooks/useTheme.ts';
import { useTranslate } from '../../hooks/useTranslate.ts';
import { Theme } from '../../themes';
import { isAndroid, isIOS } from '../../utils/Scale.ts';

const LogIn: React.FC = () => {
  const {
    handleLoginWithGoogle,
    handleLoginWithApple,
    handleForgotPassword,
    handleRegister,
    isCheckBox,
    errors,
    formData,
    focused,
    onChangeTextInput,
    onBlurInput,
    onFocusInput,
    handleRememberPass,
    handleSubmit,
    loading,
  } = useLogin();
  const theme = useTheme();
  const {t} = useTranslate();

  const isDisabled = !formData?.username || !formData?.password;

  return (
    <SafeAreaView style={styles.container}>
      <ChangeLanguageButton
        style={{marginTop: isAndroid ? verticalScale(5) : 0}}
      />
      <KeyboardAwareScrollView
        testID="aware_scroll_view_container"
        showsVerticalScrollIndicator={false}>
        <Spacer size={56} />
        <TextApp
          text={t('login.title')}
          preset="display_sm_semibold"
          style={styles.nameApp}
          textColor={theme.text_primary}
        />
        <View style={styles.inputBox}>
          <Input
            label={t('login.usernameLabel')}
            iconLeft={<SvgIcons.People />}
            placeholder={t('login.usernamePlaceholder')}
            value={formData?.username}
            onChangeText={val => onChangeTextInput('username', val)}
            onFocus={() => onFocusInput('username')}
            onBlur={() => onBlurInput('username')}
            error={t(errors.username)}
            autoCapitalize="none"
            inputStyle={{
              borderColor: errors.username
                ? theme.border_error
                : focused?.username
                  ? theme.border_brand
                  : theme.border_primary,
            }}
          />
          <Spacer size={10} />
          <Input
            label={t('login.passwordLabel')}
            isPassword
            placeholder={t('login.passwordPlaceholder')}
            value={formData?.password}
            onChangeText={val => onChangeTextInput('password', val)}
            onFocus={() => onFocusInput('password')}
            onBlur={() => onBlurInput('password')}
            error={t(errors.password)}
            autoCapitalize="none"
            inputStyle={{
              borderColor: errors.password
                ? theme.border_error
                : focused?.password
                  ? theme.border_brand
                  : theme.border_primary,
            }}
          />
        </View>
        <Spacer size={10} />
        <View
          style={{
            flex: 1,
            marginHorizontal: scale(8),
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          <Checkbox
            checked={isCheckBox}
            onPress={handleRememberPass}
            title={t('login.remember')}
          />
          <TouchableOpacity onPress={handleForgotPassword}>
            <TextApp
              preset="text_sm_semibold"
              text={t('login.forgotPassword')}
              textColor={theme.text_brand_secondary}
            />
          </TouchableOpacity>
        </View>
        <Spacer size={32} />
        <Button
          title={t('login.loginButton')}
          onPress={handleSubmit}
          disabled={isDisabled}
          style={[
            styles.mt,
            {
              backgroundColor: isDisabled
                ? theme.bg_disabled
                : theme.bg_brand_solid,
              borderColor: isDisabled
                ? theme.border_disabled_subtle
                : theme.bg_brand_solid,
            },
          ]}
          textColor={isDisabled ? theme.fg_disabled : theme.text_white}
          loading={loading}
        />
        <Spacer size={24} />
        <View style={styles.RC}>
          <TextApp
            preset="text_sm_regular"
            text={t('login.noAccount')}
            style={styles.txtW}
            textColor={'#858597'}
          />
          <TouchableOpacity onPress={handleRegister}>
            <TextApp
              preset="text_sm_semibold"
              text={t('login.signUp')}
              style={styles.forgotPass}
              textColor={theme.text_brand_secondary}
            />
          </TouchableOpacity>
        </View>
        <Spacer size={32} />
        <View style={styles.dividerBox}>
          <View style={styles.dividerH} />
          <TextApp
            preset="text_xs_regular"
            text={t('login.orLoginWith')}
            style={styles.txtOr}
            textColor={theme.text_tertiary}
          />
          <View style={styles.dividerH} />
        </View>
        <Spacer size={12} />
        <View style={styles.socialBox}>
          <TouchableOpacity
            style={[
              styles.btnSocial,
              {
                borderColor: theme.border_primary,
                backgroundColor: theme.bg_primary,
              },
            ]}
            onPress={handleLoginWithGoogle}>
            <SvgIcons.Google />
          </TouchableOpacity>
          {/* {isIOS && (
            <TouchableOpacity
              style={[
                styles.btnSocial,
                {
                  backgroundColor: '#000',
                },
              ]}>
              <SvgIcons.Apple />
            </TouchableOpacity>
          )} */}
        </View>
      </KeyboardAwareScrollView>
      {loading && <View style={styles.overlay} />}
    </SafeAreaView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Theme.colors.background,
    paddingHorizontal: scale(16),
  },
  socialBox: {
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 10,
    marginHorizontal: scale(10),
  },
  nameApp: {
    textAlign: 'center',
  },
  dividerBox: {
    width: '94%',
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
  },
  dividerH: {
    flex: 1,
    height: 1,
    backgroundColor: Theme.colors.border,
  },
  txtOr: {
    marginHorizontal: scale(15),
  },
  inputBox: {
    marginTop: moderateVerticalScale(32),
    marginHorizontal: scale(8),
  },
  mt: {
    height: moderateVerticalScale(48),
    marginHorizontal: scale(8),
    borderWidth: 1,
  },
  txtW: {
    textAlign: 'center',
    marginRight: scale(4),
  },
  RC: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  forgotPass: {},
  btnSocial: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: Theme.radius.radius_md,
    height: moderateVerticalScale(44),
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'transparent',
    zIndex: 9999,
  },
});
export default LogIn;
