import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Theme } from '../../themes';

import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import { SvgIcons } from '../../../assets/svg/index.tsx';
import Button from '../../components/Button.tsx';
import { ChangeLanguageButton } from '../../components/ChangeLanguageButton.tsx';
import Checkbox from '../../components/Checkbox.tsx';
import Input from '../../components/Input.tsx';
import { PasswordRule } from '../../components/PasswordRule.tsx';
import Spacer from '../../components/Spacer.tsx';
import TextApp from '../../components/TextApp/index.tsx';
import { useRegister } from '../../hooks/unAuth/useRegister.ts';
import { useTheme } from '../../hooks/useTheme.ts';
import { useTranslate } from '../../hooks/useTranslate.ts';
import { goBack } from '../../navigation/NavigationServices.ts';
import { HIT_SLOP, isAndroid } from '../../utils/Scale.ts';

const Register: React.FC = () => {
  const {
    formData,
    onChangeTextInput,
    onFocusInput,
    onBlurInput,
    termsAccepted,
    errors,
    handleChangeTerms,
    handleRegister,
    handleLogin,
    focused,
    loading,
    rules,
  } = useRegister();
  const theme = useTheme();
  const {t} = useTranslate();

  const isPasswordValid =
    rules?.validateLength && rules?.hasNumber && rules?.isMatch;

  const isDisabled =
    !formData.username ||
    !formData.email ||
    !formData.password ||
    !formData.confirmPassword ||
    !isPasswordValid ||
    !termsAccepted;

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAwareScrollView
        showsVerticalScrollIndicator={false}
        bottomOffset={20}>
        <View style={styles.header}>
          <TouchableOpacity
            onPress={goBack}
            hitSlop={HIT_SLOP}
            style={{
              paddingTop: isAndroid ? scale(10) : 0,
            }}>
            <SvgIcons.ArrowLeft stroke="#A4A7AE" />
          </TouchableOpacity>
          <ChangeLanguageButton />
        </View>
        <View
          style={{paddingHorizontal: scale(16), paddingTop: verticalScale(30)}}>
          <TextApp
            text={t('signup.title')}
            preset="display_sm_semibold"
            style={styles.nameApp}
            textColor={theme.text_primary}
          />
          <Spacer size={4} />
          <TextApp
            text={t('signup.subtitle')}
            preset="text_sm_regular"
            style={styles.nameApp}
            textColor={theme.text_quaternary}
          />
          <View style={styles.inputBox}>
            <Input
              label={t('signup.usernameLabel')}
              iconLeft={<SvgIcons.Mail />}
              placeholder={t('signup.usernamePlaceholder')}
              value={formData?.username}
              onChangeText={val => onChangeTextInput('username', val)}
              onFocus={() => onFocusInput('username')}
              onBlur={() => onBlurInput('username')}
              error={t(errors.username)}
              autoCapitalize="none"
              maxLength={20}
              inputStyle={{
                borderColor: errors.username
                  ? theme.border_error
                  : focused?.username
                    ? theme.border_brand
                    : theme.border_primary,
              }}
            />
            <Spacer size={10} />
            <Input
              label={t('signup.emailLabel')}
              iconLeft={<SvgIcons.Mail />}
              placeholder={t('signup.emailPlaceholder')}
              value={formData?.email}
              onChangeText={val => onChangeTextInput('email', val)}
              onFocus={() => onFocusInput('email')}
              onBlur={() => onBlurInput('email')}
              error={t(errors.email)}
              autoCapitalize="none"
              inputStyle={{
                borderColor: errors.email
                  ? theme.border_error
                  : focused?.email
                    ? theme.border_brand
                    : theme.border_primary,
              }}
            />
            <Spacer size={10} />
            <Input
              label={t('signup.passwordLabel')}
              isPassword
              placeholder={t('signup.passwordPlaceholder')}
              value={formData?.password}
              onChangeText={val => onChangeTextInput('password', val)}
              onFocus={() => onFocusInput('password')}
              onBlur={() => onBlurInput('password')}
              error={t(errors.password)}
              autoCapitalize="none"
              inputStyle={{
                borderColor: errors.password
                  ? theme.border_error
                  : focused?.password
                    ? theme.border_brand
                    : theme.border_primary,
              }}
            />
            {formData?.password && (
              <>
                <Spacer size={10} />
                <Input
                  label={t('signup.confirmPassword')}
                  isPassword
                  placeholder={t('signup.confirmPassword')}
                  value={formData?.confirmPassword}
                  onChangeText={val =>
                    onChangeTextInput('confirmPassword', val)
                  }
                  onFocus={() => onFocusInput('confirmPassword')}
                  onBlur={() => onBlurInput('confirmPassword')}
                  inputStyle={{
                    borderColor: focused?.confirmPassword
                      ? theme.border_brand
                      : theme.border_primary,
                  }}
                  autoCapitalize="none"
                />
                <Spacer size={5} />
                <PasswordRule
                  validateLength={rules?.validateLength}
                  hasNumber={rules?.hasNumber}
                  isMatch={rules?.isMatch}
                />
                <Spacer size={12} />
              </>
            )}
          </View>
          <Spacer size={20} />
          <Button
            title={t('signup.title')}
            onPress={handleRegister}
            disabled={isDisabled}
            style={[
              styles.mt,
              {
                backgroundColor: isDisabled
                  ? theme.bg_disabled
                  : theme.bg_brand_solid,
                borderColor: isDisabled
                  ? theme.border_disabled_subtle
                  : theme.bg_brand_solid,
              },
            ]}
            textColor={isDisabled ? theme.fg_disabled : theme.text_white}
            loading={loading}
          />
          <Spacer size={10} />
          <View style={{marginHorizontal: scale(8)}}>
            <Checkbox
              checked={termsAccepted}
              onPress={handleChangeTerms}
              title={t('signup.agree_message')}
            />
          </View>
          <Spacer size={32} />
          <View style={styles.RC}>
            <TextApp
              preset="text_sm_regular"
              text={t('signup.hadAccount')}
              style={styles.txtW}
              textColor={'#858597'}
            />
            <TouchableOpacity onPress={handleLogin}>
              <TextApp
                preset="text_sm_semibold"
                text={t('login.title')}
                textColor={theme.text_brand_secondary}
              />
            </TouchableOpacity>
          </View>
          <Spacer size={32} />
        </View>
      </KeyboardAwareScrollView>
      {loading && <View style={styles.overlay} />}
    </SafeAreaView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: scale(16),
    marginTop: isAndroid ? verticalScale(5) : 0,
  },
  nameApp: {
    textAlign: 'center',
  },
  inputBox: {
    marginTop: moderateVerticalScale(32),
    marginHorizontal: scale(8),
  },
  mt: {
    height: moderateVerticalScale(48),
    marginHorizontal: scale(8),
    borderWidth: 1,
  },
  txtW: {
    textAlign: 'center',
    marginRight: scale(4),
  },
  RC: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'transparent',
    zIndex: 9999,
  },
});
export default Register;
