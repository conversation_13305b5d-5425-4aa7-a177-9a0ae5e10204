import React from 'react';
import {
  Dimensions,
  ListRenderItem,
  NativeScrollEvent,
  NativeSyntheticEvent,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import FastImage, {Source} from 'react-native-fast-image';
import Animated, {
  interpolate,
  interpolateColor,
  runOnJS,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import {ChangeLanguageButton} from '../components/ChangeLanguageButton.tsx';
import {default as TextApp} from '../components/TextApp/index.tsx';
import {useTheme} from '../hooks/useTheme.ts';
import {useTranslate} from '../hooks/useTranslate.ts';
import {navigate} from '../navigation/NavigationServices.ts';
import {APP_SCREEN} from '../navigation/screenType.ts';
import {Theme} from '../themes';
import {HIT_SLOP, isAndroid, SCREEN_WIDTH} from '../utils/Scale.ts';

const {width} = Dimensions.get('window');

type SlideItem = {
  key: string;
  title: string;
  description: string;
  image: Source;
};

const WellCome: React.FC = () => {
  const theme = useTheme();
  const {t} = useTranslate();
  const scrollX = useSharedValue(0);

  const slideTexts = t('onboarding.slides', {returnObjects: true}) as Array<{
    title: string;
    description: string;
  }>;

  const slides: SlideItem[] = slideTexts.map((item, index) => ({
    key: `${index + 1}`,
    title: item.title,
    description: item.description,
    image: Theme.images.bg_onboarding,
  }));

  const keyExtractor = (item: SlideItem) => item?.key;

  const handleSignUp = () => {
    navigate(APP_SCREEN.REGISTER);
  };

  const handleLogin = () => {
    navigate(APP_SCREEN.LOGIN);
  };

  const renderItem: ListRenderItem<SlideItem> = ({item}) => (
    <View style={styles.slide}>
      <FastImage source={item?.image} style={styles.image} resizeMode="cover" />
      <TextApp
        preset="display_xs_semibold"
        style={styles.title}
        text={item?.title}
        textColor={theme.text_primary}
      />
      <TextApp
        preset="text_md_regular"
        style={styles.description}
        text={item?.description}
        textColor={theme.text_primary}
      />
    </View>
  );

  const scrollHandler = useAnimatedScrollHandler(event => {
    scrollX.value = event.contentOffset.x;
  });

  const getAnimatedDotStyle = (index: number) => {
    return useAnimatedStyle(() => {
      const progress = scrollX.value / SCREEN_WIDTH;
      const width = interpolate(
        progress,
        [index - 1, index, index + 1],
        [8, 24, 8],
        'clamp',
      );

      const backgroundColor = interpolateColor(
        progress,
        [index - 1, index, index + 1],
        [theme.bg_quaternary, theme.bg_brand_solid, theme.bg_quaternary],
      );

      return {
        width,
        backgroundColor,
      };
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <View
        style={{
          height: verticalScale(SCREEN_WIDTH / 5),
          width,
        }}>
        <ChangeLanguageButton
          style={{
            marginTop: isAndroid ? verticalScale(5) : 0,
            marginHorizontal: scale(16),
          }}
        />
      </View>

      <Animated.FlatList
        data={slides}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
      />

      <View style={styles.footer}>
        <View style={styles.dotContainer}>
          {slides.map((_, index) => {
            const animatedDotStyle = getAnimatedDotStyle(index);
            return (
              <Animated.View
                key={index}
                style={[styles.dot, animatedDotStyle]}
              />
            );
          })}
        </View>
        <Animated.View style={styles.buttonGroup}>
          <TouchableOpacity
            style={[styles.signupBtn, {backgroundColor: theme.bg_brand_solid}]}
            onPress={handleSignUp}
            hitSlop={HIT_SLOP}>
            <TextApp
              preset="text_md_semibold"
              style={styles.signupText}
              text={t('onboarding.signup')}
              textColor={theme.text_white}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.loginBtn,
              {backgroundColor: theme.bg_brand_secondary},
            ]}
            onPress={handleLogin}
            hitSlop={HIT_SLOP}>
            <TextApp
              preset="text_md_semibold"
              style={styles.loginText}
              text={t('onboarding.login')}
              textColor={theme.text_brand_secondary}
            />
          </TouchableOpacity>
        </Animated.View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Theme.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  slideWrapper: {
    width,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  slide: {
    width,
    paddingHorizontal: scale(24),
  },
  image: {
    height: moderateVerticalScale(327),
    width: '100%',
    borderRadius: 27,
  },
  title: {
    textAlign: 'center',
    lineHeight: Theme.spacing.spacing_4xl,
    marginTop: Theme.spacing.spacing_xl,
    marginBottom: Theme.spacing.spacing_2xl,
  },
  description: {
    textAlign: 'center',
    lineHeight: Theme.spacing.spacing_3xl,
  },
  dotContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: verticalScale(30),
  },
  dot: {
    width: scale(6),
    height: scale(6),
    borderRadius: scale(4),
    marginHorizontal: scale(2),
  },
  dotActive: {
    backgroundColor: '#FB923C',
    width: scale(16),
  },
  buttonGroup: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: scale(24),
    paddingBottom: verticalScale(15),
    gap: 15,
  },
  signupBtn: {
    flex: 1,
    height: moderateVerticalScale(48),
    borderRadius: Theme.radius.radius_md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginBtn: {
    flex: 1,
    height: moderateVerticalScale(48),
    borderRadius: Theme.radius.radius_md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  signupText: {
    lineHeight: Theme.spacing.spacing_2xl,
  },
  loginText: {
    lineHeight: Theme.spacing.spacing_2xl,
  },
  footer: {
    height: verticalScale(SCREEN_WIDTH / 4.5),
    justifyContent: 'space-between',
  },
});

export default WellCome;
