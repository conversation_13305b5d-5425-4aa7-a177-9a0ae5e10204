interface RolePlayCard {
  id: string;
  rlFigureId: string;
  gradeId: number;
  questioner: string;
  title: string;
  answerer: string;
  missionSummary: string;
  coin: number;
  rlFigureCode: string;
  createdDate: string;
  createdBy: string;
  modifiedDate: string;
  modifiedBy: string;
  mission: MissionRolePlay[];
}

interface MissionRolePlay {
  value: string;
  key: string;
  status?: boolean;
}
