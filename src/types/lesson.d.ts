interface Lesson {
  bottom: number;
  description: string;
  id: string;
  index: number;
  isDone: boolean;
  left: number;
  levelName: string;
  name: string;
  numOfExcercises: number;
  numOfPass: number;
  ordBy: string;
  thumbnailPath: string;
  unitId: string;
  unitName: string;
  data: LessonData[];
  duration: number;
  exAssignId: string;
  deadline: string;
  attemptLimit: string;
  mistakeLimit: number;
}

interface LessonData {
  answer: string;
  answerByStudent: any;
  answers: string[];
  audio: string;
  exerciseName: string;
  exerciseType: ExerciseType;
  id: string;
  image: string;
  index: number;
  isPassExcercise: boolean;
  mediaType: string;
  options: string[];
  pairLst: {
    left: string[];
    right: string[];
  };
  pairs: {word: string; match: string}[];
  question: string | any[]; // Can be string or array for different exercise types
  questions?: any[]; // Optional questions array for new data format
  templateCode: string;
  templateId: string;
  templateName: string;
  title: string;
  typePair: string;
  status?: string;
  subType: string;
}

type ExerciseType =
  | 'pronunciation'
  | 'fill_in_the_blank'
  | 'multiple_choice'
  | 'matching_pairs'
  | 'drag_and_drop'
  | 'arrangement'
  | 'arrarrangement_dialogue'
  | 'dropdown';

interface AnswerByStudent {
  assessment: Assessment;
  is_pass: boolean;
  overall_score: number;
  overall_score_grade: string;
  target_phrase: string;
  word_scores: WordScore[];
}

interface Assessment {
  Content: AssessmentCategory;
  Fluency: AssessmentCategory;
  Pronunciation: AssessmentCategory;
  overall_score: number;
  phoneme_error_rate: number;
}

interface AssessmentCategory {
  explanation: string[];
  num_matched_words: number | null;
  num_mispronounced_words: number | null;
  num_words: number | null;
  score: number;
  words_per_minute: number | null;
}

interface WordScore {
  word: string;
  word_normalised: string;
  score: number;
  score_grade: string;
  num_wrong_phonemes: number;
  phoneme_scores: {
    phoneme: string;
    phoneme_ipa: string;
    score: number;
    score_grade: string;
    sounds_like: string;
    sounds_like_ipa: string;
  }[];
  boundaries: {
    end_ms: number;
    start_ms: number;
  };
  indices: {
    end: number;
    start: number;
  };
}

interface DataFinish {
  answerByStudent: any;
  excerciseType: string;
  id: string;
  index: number;
  isPassExcercise: boolean;
  answers: string[];
  question: string | any[];
  questions?: any[];
}
