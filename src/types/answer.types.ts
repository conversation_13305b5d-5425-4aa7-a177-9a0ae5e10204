export type AnswerValue = string | number | boolean;

export interface StudentAnswer {
  id?: number;
  answer: AnswerValue;
  index?: number;
}

export type StudentAnswers = string[] | StudentAnswer[] | AnswerValue[];

export interface AnswerDisplayConfig {
  showCorrectAnswer: boolean;
  showStudentAnswer: boolean;
  highlightIncorrect: boolean;
  underlineCorrect: boolean;
}

export interface UniversalResultAnswerProps {
  question?: string;
  answers: AnswerValue[] | AnswerValue | string;
  studentAnswers: StudentAnswers;
  isArrangement?: boolean;
  displayConfig?: Partial<AnswerDisplayConfig>;
  excerciseType?: ExerciseType;
}

export interface AnswerComparisonResult {
  isCorrect: boolean;
  correctAnswer: AnswerValue;
  studentAnswer: AnswerValue;
  index: number;
}

export interface TextSegment {
  type: 'text' | 'answer';
  content: string;
  isCorrect?: boolean;
  index?: number;
}

export enum ExerciseType {
  PRONUNCIATION = 'pronunciation',
  FILL_IN_THE_BLANK = 'fill_in_the_blank',
  MULTIPLE_CHOICE = 'multiple_choice',
  MATCHING_PAIRS = 'matching_pairs',
  DRAG_AND_DROP = 'drag_and_drop',
  ARRANGEMENT = 'arrangement',
  ARRANGEMENT_DIALOGUE = 'arrarrangement_dialogue',
  DROPDOWN = 'dropdown',
}

export const DEFAULT_ANSWER_DISPLAY_CONFIG: AnswerDisplayConfig = {
  showCorrectAnswer: true,
  showStudentAnswer: true,
  highlightIncorrect: true,
  underlineCorrect: true,
};

export interface NormalizedAnswers {
  [key: number]: AnswerValue;
}

export interface EnhancedDataFinish {
  answerByStudent: any;
  excerciseType: string;
  id: string;
  index: number;
  isPassExcercise: boolean;
  answers: AnswerValue[] | AnswerValue;
  question: string | any[];
  questions?: any[];
  accuracy?: number;
}
