interface ClassData {
  id: string;
  teacherId: string;
  teacherName: string;
  status: number;
  name: string;
  description: string;
  modifiedDate: string;
  modifiedBy: string;
  gradeId: number;
  gradeName: string;
  orgId: string;
  orgName: string;
  deadline: string;
  thumbnailPath: string;
  isStar: boolean;
  scheduleDate: string;
  numberOfStudents: number;
  endDate: string;
  levelId: string;
  levelName: string;
  attemptTime: string;
  missons: any;
}
