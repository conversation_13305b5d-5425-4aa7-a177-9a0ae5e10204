interface FormLoginData {
  username: string;
  password: string;
}

interface FormRegisterData {
  username: string;
  email: string;
  phone: string | number;
  password: string;
}

interface ProfileData {
  coins: number;
  email: string;
  enabled: boolean;
  exp: number;
  id: string;
  modifiedBy: string;
  modifiedDate: string;
  name: string;
  phoneNumber: string;
  photoUrl: string;
  userLevel: number;
  username: string;
  userLevelName: string;
  authorities: string[];
}

interface SignInGoogleInterface {
  id_token?: string;
  deviceId?: string | null | undefined;
  source?: string;
  platform?: string;
}
