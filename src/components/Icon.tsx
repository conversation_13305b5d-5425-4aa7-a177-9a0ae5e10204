import React from 'react';
import {Image, ImageStyle, StyleSheet} from 'react-native';

interface IconProps {
    source: any; // Hỗ trợ cả require() và { uri: '' }
    size?: number;
    style?: ImageStyle;
}

const Icon: React.FC<IconProps> = ({source, size = 24, style}) => {
    return <Image source={source} style={[styles.icon, {width: size, height: size}, style]} resizeMode="contain"/>;
};

const styles = StyleSheet.create({
    icon: {
        width: 24,
        height: 24,
    },
});

export default Icon;
