import * as React from 'react';
import Svg, {Defs, LinearGradient, Path, Rect, Stop} from 'react-native-svg';

const ProgressMission = ({progress}: {progress: number}) => {
  const numberP = progress > 0 ? progress : 0.08;

  return (
    <Svg width={165} height={30} fill="none">
      <Path
        fill="url(#a)"
        stroke="#75390C"
        strokeMiterlimit={10}
        strokeWidth={3}
        d="M153.149 24.6h-131.5c-1.714 0-3.101-1.365-3.101-3.05V8.28c0-1.685 1.387-3.05 3.1-3.05H153.15c5.44 0 9.851 4.338 9.851 9.687 0 5.349-4.411 9.686-9.851 9.686V24.6Z"
      />
      <Path
        fill="url(#b)"
        stroke="#75390C"
        strokeMiterlimit={10}
        strokeWidth={3}
        d="M154.4 18.882H23.431a.722.722 0 0 1-.722-.723v-6.546c0-.4.323-.722.722-.722H154.4a3.995 3.995 0 0 1 3.996 3.995 3.994 3.994 0 0 1-3.996 3.996Z"
      />
      <Rect
        x="21.361"
        y="11.191"
        width={135.7 * numberP}
        height="6.05"
        rx="3"
        fill="url(#c)"
      />

      <Path
        fill="url(#d)"
        stroke="#75390C"
        strokeMiterlimit={10}
        strokeWidth={3}
        d="M14.887 27.774c7.117 0 12.887-5.77 12.887-12.887C27.774 7.77 22.004 2 14.887 2 7.77 2 2 7.77 2 14.887c0 7.117 5.77 12.887 12.887 12.887Z"
      />
      <Path
        fill="url(#e)"
        d="M24.214 12.16a1.258 1.258 0 0 0-1.025-.864l-4.734-.67-2.131-4.314a1.258 1.258 0 0 0-1.138-.707c-.489 0-.924.27-1.138.707l-2.103 4.294-4.763.69c-.48.069-.875.4-1.025.864-.15.464-.028.96.323 1.304l3.435 3.325-.816 4.743a1.27 1.27 0 0 0 1.845 1.34l4.226-2.24 4.258 2.24c.431.226.944.19 1.336-.097.395-.287.589-.763.504-1.243l-.823-4.71 3.447-3.358a1.27 1.27 0 0 0 .322-1.304Z"
      />
      <Path
        fill="url(#f)"
        d="m15.743 6.599 2.13 4.315a.623.623 0 0 0 .465.338l4.762.69a.616.616 0 0 1 .343 1.05l-3.446 3.358a.622.622 0 0 0-.178.545l.815 4.742a.617.617 0 0 1-.896.65l-4.258-2.24a.625.625 0 0 0-.573 0l-4.258 2.24a.617.617 0 0 1-.896-.65l.816-4.742a.619.619 0 0 0-.178-.545l-3.447-3.358a.614.614 0 0 1 .343-1.05l4.763-.69a.605.605 0 0 0 .464-.338l2.13-4.315a.616.616 0 0 1 1.107 0h-.008Z"
      />
      <Path
        fill="url(#g)"
        d="m22.06 12.254-4.138-.602a.527.527 0 0 1-.403-.294l-1.849-3.75a.535.535 0 0 0-.96 0l-1.849 3.75a.546.546 0 0 1-.404.294l-4.136.602a.534.534 0 0 0-.327.185c2.191.695 4.625 1.082 7.196 1.082 2.57 0 5.005-.387 7.196-1.082a.523.523 0 0 0-.327-.185Z"
      />
      <Defs>
        <LinearGradient
          id="a"
          x1={62.341}
          x2={62.341}
          y1={5.003}
          y2={31.885}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#FFFA6A" />
          <Stop offset={0.03} stopColor="#FFF200" />
          <Stop offset={1} stopColor="#FF6900" />
        </LinearGradient>
        <LinearGradient
          id="b"
          x1={23.758}
          x2={53.851}
          y1={14.886}
          y2={14.886}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#6A1601" stopOpacity={0} />
          <Stop offset={0.99} stopColor="#4B0C00" />
        </LinearGradient>
        <LinearGradient
          id="c"
          x1={57.778}
          x2={57.778}
          y1={22.472}
          y2={13.815}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#FF6900" />
          <Stop offset={1} stopColor="#FFF700" />
        </LinearGradient>
        <LinearGradient
          id="d"
          x1={14.887}
          x2={14.887}
          y1={-4.857}
          y2={37.557}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#FFFA6A" />
          <Stop offset={0.03} stopColor="#FFF200" />
          <Stop offset={1} stopColor="#FF6900" />
        </LinearGradient>
        <LinearGradient
          id="e"
          x1={15.19}
          x2={15.19}
          y1={1.053}
          y2={30.984}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#6A1601" stopOpacity={0} />
          <Stop offset={1} stopColor="#6A1601" />
        </LinearGradient>
        <LinearGradient
          id="f"
          x1={15.19}
          x2={15.19}
          y1={12.334}
          y2={24.692}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#FFFA6A" />
          <Stop offset={0.03} stopColor="#FFF200" />
          <Stop offset={1} stopColor="#FF6900" />
        </LinearGradient>
        <LinearGradient
          id="g"
          x1={15.19}
          x2={15.19}
          y1={3.621}
          y2={14.025}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#fff" />
          <Stop offset={1} stopColor="#fff" stopOpacity={0} />
        </LinearGradient>
      </Defs>
    </Svg>
  );
};
export default ProgressMission;
