import React from 'react';
import {Image, ImageStyle, StyleSheet} from 'react-native';

interface AvatarProps {
    source: any;
    size?: number;
    style?: ImageStyle;
}

const Avatar: React.FC<AvatarProps> = ({source, size = 50, style}) => {
    return <Image source={source} style={[styles.avatar, {width: size, height: size, borderRadius: size / 2}, style]}/>;
};

const styles = StyleSheet.create({
    avatar: {
        borderWidth: 1,
        borderColor: '#ccc',
    },
});

export default Avatar;
