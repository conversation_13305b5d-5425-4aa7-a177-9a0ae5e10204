import React from 'react';
import {StyleSheet, Text} from 'react-native';
import {FontFamily} from '../themes/typography';
import {useTheme} from '../hooks/useTheme';
import {
  UniversalResultAnswerProps,
  ExerciseType,
  DEFAULT_ANSWER_DISPLAY_CONFIG,
} from '../types/answer.types';
import {
  parseTextWithBlanks,
  processArrangementAnswers,
  isSingleWordQuestion,
} from '../utils/answer.utils';

const UniversalResultAnswer: React.FC<UniversalResultAnswerProps> = ({
  question,
  answers,
  studentAnswers,
  isArrangement = false,
  displayConfig = DEFAULT_ANSWER_DISPLAY_CONFIG,
  excerciseType = ExerciseType.FILL_IN_THE_BLANK,
}) => {
  const theme = useTheme();

  if (isArrangement || excerciseType === ExerciseType.ARRANGEMENT) {
    const getArrangementType = () => {
      if (Array.isArray(answers) && answers.length > 1) {
        return 'multiple_sentences';
      }

      const answerString = String(
        Array.isArray(answers) ? answers[0] : answers,
      );

      const hasSpaces = answerString.includes(' ');
      if (hasSpaces) {
        const words = answerString.trim().split(/\s+/);
        const isSpacedWord = words.every(word => word.length === 1);
        if (isSpacedWord) {
          return 'spaced_word';
        } else {
          return 'sentence';
        }
      }
      return 'single_word';
    };

    const arrangementType = getArrangementType();
    if (arrangementType === 'multiple_sentences' && Array.isArray(answers)) {
      return (
        <>
          {answers.map((sentence, index) => {
            const studentArray = Array.isArray(studentAnswers)
              ? studentAnswers
              : [];
            const sentenceStr = String(sentence);
            const studentSentence =
              index < studentArray.length ? String(studentArray[index]) : '';
            const isCorrect = sentenceStr === studentSentence;
            const dialogueLabel = index % 2 === 0 ? 'A:' : 'B:';
            return (
              <Text
                key={index}
                style={[
                  styles.line,
                  {
                    color: isCorrect
                      ? theme.text_secondary
                      : theme.text_success_primary,
                  },
                ]}>
                {dialogueLabel} {sentenceStr}
              </Text>
            );
          })}
        </>
      );
    }

    const answerString = String(Array.isArray(answers) ? answers[0] : answers);
    if (arrangementType === 'spaced_word') {
      const correctChars = answerString.trim().split(' ');
      const studentArray = Array.isArray(studentAnswers) ? studentAnswers : [];

      return (
        <Text style={styles.wrapper}>
          {correctChars.map((char, index) => {
            const isCharCorrect =
              index < studentArray.length &&
              String(studentArray[index]) === char;

            return (
              <Text
                key={index}
                style={{
                  color: isCharCorrect
                    ? theme.text_secondary
                    : theme.text_success_primary,
                  textDecorationLine: isCharCorrect ? 'none' : 'underline',
                  fontSize: 16,
                  fontFamily: FontFamily.medium,
                }}>
                {char}
              </Text>
            );
          })}
        </Text>
      );
    }

    if (arrangementType === 'sentence') {
      const correctWords = answerString.trim().split(/\s+/);
      const studentArray = Array.isArray(studentAnswers) ? studentAnswers : [];

      return (
        <Text style={styles.wrapper}>
          {correctWords.map((word, index) => {
            const isWordCorrect = String(studentArray[index]) === word;

            return (
              <Text
                key={index}
                style={{
                  color: isWordCorrect
                    ? theme.text_secondary
                    : theme.text_success_primary,
                  textDecorationLine: isWordCorrect ? 'none' : 'underline',
                  fontSize: 16,
                  fontFamily: FontFamily.medium,
                }}>
                {word + (index < correctWords.length - 1 ? ' ' : '')}
              </Text>
            );
          })}
        </Text>
      );
    }

    const results = processArrangementAnswers(
      answers,
      studentAnswers,
      isSingleWordQuestion(answerString),
    );

    return (
      <Text style={styles.wrapper}>
        {results.map((result, index) => (
          <Text
            key={index}
            style={{
              color: result.isCorrect
                ? theme.text_secondary
                : theme.text_success_primary,
            }}>
            {result.correctAnswer + ' '}
          </Text>
        ))}
      </Text>
    );
  }

  const segments = parseTextWithBlanks(
    question || '',
    Array.isArray(answers) ? answers : [answers],
    studentAnswers,
  );

  return (
    <Text style={styles.wrapper}>
      {segments.map((segment, index) => {
        if (segment.type === 'answer') {
          return (
            <Text
              key={index}
              style={[
                segment.isCorrect
                  ? {color: theme.text_secondary}
                  : {
                      color: theme.text_success_primary,
                      textDecorationLine: displayConfig.underlineCorrect
                        ? 'underline'
                        : 'none',
                    },
              ]}>
              {segment.content}
            </Text>
          );
        }

        return (
          <Text key={index} style={{color: theme.text_secondary}}>
            {segment.content}
          </Text>
        );
      })}
    </Text>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flexWrap: 'wrap',
    fontSize: 16,
    fontFamily: FontFamily.medium,
    lineHeight: 24,
    marginTop: 2,
  },
  line: {
    fontSize: 16,
    fontFamily: FontFamily.medium,
    lineHeight: 28,
    marginBottom: 6,
  },
  error: {
    fontSize: 14,
    fontFamily: FontFamily.medium,
    color: '#FF6B6B',
    fontStyle: 'italic',
  },
});

export default UniversalResultAnswer;
