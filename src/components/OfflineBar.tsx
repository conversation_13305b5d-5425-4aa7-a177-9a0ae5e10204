import React from 'react';
import {StyleSheet, View} from 'react-native';
import {CustomBottomSheet} from './BottomSheet';
import {Theme} from '../themes';
import TextApp from './TextApp';
import {moderateVerticalScale, scale} from 'react-native-size-matters';
import {SvgIcons} from '../../assets/svg';
import {isIOS} from '../utils/Scale';
import {useTypedSelector} from '../redux/store';

export const OffLineBar = () => {
  const visible = useTypedSelector(state => state.offline.visible);

  return (
    <CustomBottomSheet
      visible={visible}
      isOverlay
      overlayColor={{backgroundColor: 'transparent'}}
      containerStyle={styles.container}>
      <View style={styles.offlineContainer}>
        <SvgIcons.WifiOffline />
        <TextApp
          preset="text_sm_regular"
          text={'You are offline'}
          style={{marginLeft: scale(10), lineHeight: 20}}
        />
      </View>
    </CustomBottomSheet>
  );
};
const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    bottom: isIOS ? 40 : 20,
    left: 0,
    right: 0,
    paddingBottom: Theme.spacing.spacing_none,
    zIndex: 99,
  },
  background: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  offlineContainer: {
    backgroundColor: '#FEF3F2',
    paddingVertical: moderateVerticalScale(Theme.spacing.spacing_lg),
    paddingHorizontal: scale(Theme.spacing.spacing_lg),
    borderRadius: Theme.radius.radius_2xl,
    flexDirection: 'row',
    alignItems: 'center',
  },
});
