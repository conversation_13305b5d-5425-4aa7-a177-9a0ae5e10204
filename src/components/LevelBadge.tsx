import React from 'react';
import {StyleSheet, View} from 'react-native';
import {moderateScale, scale, verticalScale} from 'react-native-size-matters';
import {FontFamily} from '../themes/typography';
import TextApp from './TextApp';
import {Theme} from '../themes';

interface LevelBadgeProps {
  label: string;
  size?: 'default' | 'small';
}

export const LevelBadge: React.FC<LevelBadgeProps> = ({
  label,
  size = 'default',
}) => {
  const isSmall = size === 'small';
  return (
    <View style={[styles.container, isSmall && styles.containerSmall]}>
      <View style={[styles.circleLeft]} />
      <View style={styles.textContainer}>
        <TextApp
          preset="text_sm_medium"
          text={label}
          style={[styles.text]}
          textColor={'#B55F0D'}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderColor: '#F99A3D',
    backgroundColor: '#FFEECD',

    justifyContent: 'center',
    borderRadius: Theme.radius.radius_full,
    paddingHorizontal: 10,
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  textContainer: {
    zIndex: 1,
    backgroundColor: '#FFEECD',
    height: '100%',
    justifyContent: 'center',
    paddingHorizontal: 5,
    borderRadius: moderateScale(6),
  },
  text: {
    color: '#B55F0D',
    lineHeight: 16,
  },
  circleLeft: {
    width: scale(5),
    height: scale(5),
    borderRadius: moderateScale(5),

    backgroundColor: '#F99A3D',
  },
  circleRight: {
    width: scale(20),
    height: verticalScale(20),
    borderRadius: moderateScale(30),
    borderWidth: 2,
    borderColor: '#F99A3D',
    backgroundColor: '#FFEECD',
    position: 'absolute',
    right: -9,
  },
  containerSmall: {
    height: verticalScale(20),
  },
  circleSmall: {
    width: scale(15),
    height: verticalScale(15),
  },
  textSmall: {
    fontSize: 10,
    fontFamily: FontFamily.bold,
  },
});
