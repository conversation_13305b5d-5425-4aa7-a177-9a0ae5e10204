import React from 'react';
import {Image, StyleSheet, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {Theme} from '../themes';
import {moderateVerticalScale, scale} from 'react-native-size-matters';
import TextApp from './TextApp';
import Spacer from './Spacer';
import AnimatedLottieView from 'lottie-react-native';
import {heightScreen, widthScreen} from '../utils/Scale.ts';
import {Images} from '../themes/images.ts';

interface LoadResourceProps {
  text?: string;
}

export const LoadResource: React.FC<LoadResourceProps> = ({
  text = '“All good things come to those who wait”',
}) => {
  return (
    <View style={styles.container}>
      <View>
        <Image style={{width: 279, height: 191}} source={Images.chatBubble} />
        <TextApp
          text={text}
          preset={'text_lg_semibold'}
          textColor={'#414651'}
          style={{
            position: 'absolute',
            width: 200,
            textAlign: 'center',
            top: 26,
            left: 36,
          }}
        />
      </View>
      <FastImage
        source={Theme.images.thought}
        style={{width: scale(196), height: moderateVerticalScale(295)}}
        resizeMode="contain"
      />
      <Spacer size={20} />
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <TextApp preset="text_md_semibold" text={'Loading'} />
        <AnimatedLottieView
          source={require('../../assets/lotties/loading.json')}
          style={styles.lottie}
          autoPlay
          speed={0.7}
        />
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    width: widthScreen,
    height: heightScreen,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    zIndex: 9999,
    backgroundColor: '#FCF2E8',
  },
  lottie: {
    width: scale(40),
    height: scale(50),
  },
});
