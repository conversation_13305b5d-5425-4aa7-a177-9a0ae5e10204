import React from 'react';
import Animated, {ZoomIn, ZoomOut} from 'react-native-reanimated';
import ReviewSelectTemplate from './templates/ReviewSelectTemplate';
import WellDone from './templates/WellDone';
import {goBack, replace} from '../navigation/NavigationServices';
import {APP_SCREEN} from '../navigation/screenType';

interface ReviewQuestionContentProps {
  currentPage: number;
  done: boolean;
  data: any[];
  passed: number;
  total: number;
  onPreview: () => void;
  styles: any;
  reviewItem?: any;
}

const ReviewQuestionContent: React.FC<ReviewQuestionContentProps> = React.memo(
  ({currentPage, done, data, passed, total, onPreview, styles, reviewItem}) => {
    return (
      <Animated.View
        key={currentPage}
        entering={ZoomIn}
        exiting={ZoomOut}
        style={styles.centerBox}>
        {done ? (
          <WellDone
            onPress={() => goBack()}
            answer={`${passed}/${total}`}
            onPreview={() => {
              replace(APP_SCREEN.REVIEW_QUESTIONS, {
                item: reviewItem,
                isDone: true,
              });
            }}
          />
        ) : (
          <ReviewSelectTemplate data={data[currentPage]} />
        )}
      </Animated.View>
    );
  },
);

ReviewQuestionContent.displayName = 'ReviewQuestionContent';

export default ReviewQuestionContent;
