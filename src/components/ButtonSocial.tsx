import React from 'react';
import {Image, StyleSheet, Text, TouchableOpacity} from 'react-native';
import {Theme} from '../themes';

interface ButtonSocialProps {
  title: string;
  iconSource: any;

  onPress: () => void;
}

const ButtonSocial: React.FC<ButtonSocialProps> = ({
  title,
  iconSource,

  onPress,
}) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={0.75}
      style={[styles.button]}>
      <Image style={styles.icon} source={iconSource} />
      <Text style={styles.text}>{title}</Text>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 8,
    marginHorizontal: 5,
    backgroundColor: Theme.colors.blue['50'],
    borderWidth: 0.5,
    borderColor: Theme.colors.border,
    justifyContent: 'center',
    marginTop: 12,
    width: '100%',
  },
  text: {
    color: Theme.colors.textPrimary,

    fontSize: 16,
    fontWeight: 'bold',
  },
  icon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    resizeMode: 'contain',
    position: 'absolute',
    top: '45%',
    left: 12,
  },
});
export default ButtonSocial;
