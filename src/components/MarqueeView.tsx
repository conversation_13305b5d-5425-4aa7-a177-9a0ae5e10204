import React, {
  cloneElement,
  ReactElement,
  useCallback,
  useEffect,
  useMemo,
  useRef,
} from 'react';
import {
  Animated,
  Easing,
  LayoutChangeEvent,
  PixelRatio,
  ScrollView,
  StyleProp,
  View,
  ViewStyle,
} from 'react-native';

interface MarqueeViewProps {
  children: ReactElement;
  style?: StyleProp<ViewStyle>;
  speed?: number;
  delay?: number;
  autoPlay?: boolean;
  playing?: boolean;
  minLength?: number;
}

export const MarqueeView: React.FC<MarqueeViewProps> = ({
  style,
  children,
  speed = 0.1,
  delay = 0,
  autoPlay = true,
  minLength = 5,
}) => {
  const containerWidth = useRef<number>(0);
  const contentWidth = useRef<number>(0);
  const offsetX = useRef(new Animated.Value(0));
  const contentRef = useRef<any>(null);
  const isPlaying = useRef<boolean>(autoPlay);
  const animatedValue = useRef<Animated.CompositeAnimation | null>(null);
  const reset = useRef<boolean>(false);
  const requestStart = useRef<boolean>(false);

  useEffect(() => {
    const listener = offsetX.current.addListener(({value}: {value: number}) => {
      if (
        parseFloat(value.toFixed(1)) <= -contentWidth.current &&
        !reset.current
      ) {
        reset.current = true;
        const outScreenValue = containerWidth.current;
        animatedValue.current = Animated.timing(offsetX.current, {
          toValue: outScreenValue,
          duration: 0,
          delay,
          easing: Easing.linear,
          useNativeDriver: true,
        });
        animatedValue.current.start(() => {
          start(outScreenValue);
        });
      }
    });
    return () => {
      offsetX.current.removeListener(listener);
      contentRef.current = null;
    };
  }, []);

  //   const stop = useCallback(() => {
  //     animatedValue.current?.stop();
  //     isPlaying.current = false;
  //   }, []);

  const start = useCallback(
    (currentOffset: number = 0) => {
      const value = -(contentWidth.current * 2);
      const durationByValue =
        PixelRatio.getPixelSizeForLayoutSize(-value + currentOffset) / speed;
      if (!contentWidth.current) {
        requestStart.current = true;
        return;
      }
      animatedValue.current = Animated.timing(offsetX.current, {
        toValue: value,
        duration: durationByValue,
        delay,
        easing: Easing.linear,
        useNativeDriver: true,
      });
      isPlaying.current = true;
      reset.current = false;
      requestStart.current = false;
      animatedValue.current.start();
    },
    [speed, delay],
  );

  const textContent = children.props?.text || '';
  const shouldAnimate = textContent.length >= minLength;

  const childrenCloned = useMemo(
    () =>
      cloneElement(children, {
        ...children.props,
        onLayout: (event: LayoutChangeEvent) => {
          const {width} = event.nativeEvent.layout;
          if (width === contentWidth.current) {
            return;
          }
          contentWidth.current = width;
          if ((autoPlay || requestStart.current) && shouldAnimate) {
            start();
          }
        },
      }),
    [children, autoPlay, start, shouldAnimate],
  );

  const measureContainerView = useCallback((event: LayoutChangeEvent) => {
    const {width} = event.nativeEvent.layout;
    if (containerWidth.current === width) {
      return;
    }
    containerWidth.current = width;
  }, []);

  return (
    <View onLayout={measureContainerView} style={style}>
      {shouldAnimate ? (
        <ScrollView
          horizontal={true}
          bounces={false}
          scrollEnabled={false}
          showsHorizontalScrollIndicator={false}>
          <Animated.View
            style={{
              transform: [{translateX: offsetX.current}],
            }}>
            {childrenCloned}
          </Animated.View>
        </ScrollView>
      ) : (
        <View style={{justifyContent: 'center', alignItems: 'center'}}>
          {children}
        </View>
      )}
    </View>
  );
};
