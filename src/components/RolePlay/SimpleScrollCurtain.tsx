import React, {
  useCallback,
  useState,
  forwardRef,
  useImperativeHandle,
  memo,
} from 'react';
import {StyleSheet, View, ImageBackground, TouchableOpacity} from 'react-native';
import {Gesture, GestureDetector} from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import {
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import {Theme} from '../../themes';
import {isIOS} from '../../utils/Scale';
import RopeRingRolePlay, { RopeRingRolePlayRef } from './RopeRingRolePlay';

export interface SimpleScrollCurtainRef {
  animateDown: () => void;
  animateUp: () => void;
  animateTaskCompleted: () => void;
  isExpanded: boolean;
}

interface SimpleScrollCurtainProps {
  children?: React.ReactNode;
  enableGesture?: boolean;
}

export const SimpleScrollCurtain = forwardRef<
  SimpleScrollCurtainRef,
  SimpleScrollCurtainProps
>(({children, enableGesture = true}, ref) => {
  const [isPulledDown, setIsPulledDown] = useState<boolean>(false);
  const ropeRingRef = React.useRef<RopeRingRolePlayRef>(null);

  const translateY = useSharedValue(-240);
  const dragY = useSharedValue(0);
  const contentOpacity = useSharedValue(0);

  const HIDDEN_POSITION = -240;
  const VISIBLE_POSITION = 0;
  const MAX_DRAG = moderateVerticalScale(200);
  const THRESHOLD = moderateVerticalScale(50);

  const animateToPulledDown = useCallback(() => {
    'worklet';
    translateY.value = withTiming(VISIBLE_POSITION, {
      duration: 300,
    });
    dragY.value = withTiming(0, {
      duration: 300,
    });
    contentOpacity.value = withTiming(1, {
      duration: 200,
    });
    runOnJS(setIsPulledDown)(true);
  }, [translateY, dragY, contentOpacity, VISIBLE_POSITION]);

  const animateToHidden = useCallback(() => {
    'worklet';
    translateY.value = withTiming(HIDDEN_POSITION, {
      duration: 300,
    });
    dragY.value = withTiming(0, {
      duration: 300,
    });
    contentOpacity.value = withTiming(0, {
      duration: 200,
    });
    runOnJS(setIsPulledDown)(false);
  }, [translateY, dragY, contentOpacity, HIDDEN_POSITION]);

  // Toggle function for tap
  const handleToggle = useCallback(() => {
    if (isPulledDown) {
      animateToHidden();
    } else {
      animateToPulledDown();
    }
  }, [isPulledDown, animateToHidden, animateToPulledDown]);

  const resetDrag = useCallback(() => {
    'worklet';
    dragY.value = withTiming(0, {
      duration: 200,
    });
  }, [dragY]);

  const panGesture = Gesture.Pan()
    .minDistance(1)
    .activeOffsetY([-5, 5])
    .enabled(enableGesture)
    .onUpdate(event => {
      const currentDrag = event.translationY;

      if (!isPulledDown) {
        if (currentDrag > 0) {
          dragY.value = Math.min(currentDrag, MAX_DRAG);
        } else {
          const resistance = Math.abs(currentDrag) * 0.3;
          dragY.value = -Math.min(resistance, 0);
        }
      } else {
        if (currentDrag < 0) {
          dragY.value = Math.max(currentDrag, -MAX_DRAG);
        } else {
          const resistance = currentDrag * 0.3;
          dragY.value = Math.min(resistance, 0);
        }
      }
    })
    .onEnd(event => {
      const velocity = event.velocityY;
      const shouldPullDown =
        !isPulledDown && (event.translationY > THRESHOLD || velocity > 500);
      const shouldPushUp =
        isPulledDown && (event.translationY < -THRESHOLD || velocity < -500);

      if (shouldPullDown) {
        animateToPulledDown();
      } else if (shouldPushUp) {
        animateToHidden();
      } else {
        resetDrag();
      }
    });

  const animatedContainerStyle = useAnimatedStyle(() => {
    const totalTranslateY = translateY.value + dragY.value;

    return {
      transform: [
        {
          translateY: totalTranslateY,
        },
      ],
    };
  });

  const animatedContentStyle = useAnimatedStyle(() => {
    const totalTranslateY = translateY.value + dragY.value;
    const progress =
      (totalTranslateY - HIDDEN_POSITION) /
      (VISIBLE_POSITION - HIDDEN_POSITION);
    const clampedProgress = Math.max(0, Math.min(1, progress));

    return {
      opacity: clampedProgress,
    };
  });

  useImperativeHandle(
    ref,
    () => ({
      animateDown: () => {
        animateToPulledDown();
      },
      animateUp: () => {
        animateToHidden();
      },
      animateTaskCompleted: () => {
        ropeRingRef.current?.shake();
      },
      
      isExpanded: isPulledDown,
    }),
    [animateToPulledDown, animateToHidden, isPulledDown],
  );

  if (!enableGesture) return null;

  return (
    <GestureDetector gesture={panGesture}>
      <Animated.View style={[styles.container, animatedContainerStyle]}>
        <TouchableOpacity
          style={styles.parchmentContainer}
          onPress={handleToggle}
          activeOpacity={0.8}
        >
          <View style={styles.outerImageContainer}>
            <ImageBackground
              source={Theme.images.bgMissionRolePlay}
              style={styles.outerImage}
              resizeMode="cover">
              <Animated.View
                style={[styles.innerImageContainer, animatedContentStyle]}>
                <ImageBackground
                  source={Theme.images.missionRolePlayFrame}
                  style={styles.innerImage}
                  resizeMode="contain">
                  {children}
                </ImageBackground>
              </Animated.View>
            </ImageBackground>
          </View>
        </TouchableOpacity>
        {enableGesture && (
          <View style={styles.pullHandle}>
            <RopeRingRolePlay ref={ropeRingRef} />
          </View>
        )}
      </Animated.View>
    </GestureDetector>
  );
});

export const SimpleScrollCurtainMemo = memo(SimpleScrollCurtain);

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: -25,
    left: 0,
    right: 0,
    height: isIOS ? 380 : 360,
    zIndex: 2,
    backgroundColor: 'transparent',
  },
  parchmentContainer: {
    flex: 1,
  },
  parchmentContent: {
    flex: 1,
  },

  outerImageContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  outerImage: {
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  innerImageContainer: {
    marginTop: 80,
    width: scale(320),
    height: verticalScale(200),
    alignSelf: 'center',
  },
  innerImage: {
    width: '100%',
    height: '100%',
  },
  contentArea: {
    position: 'absolute',
    top: '15%',
    left: '10%',
    right: '10%',
    bottom: '15%',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingHorizontal: scale(15),
    paddingVertical: verticalScale(10),
  },

  pullHandle: {
    position: 'absolute',
    right: 0,
    bottom: -115,
    zIndex: -1,
  },
  handleRing: {
    width: scale(30),
    height: scale(80),
  },
});
