import React from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import {scale} from 'react-native-size-matters';
import {RolePlayCard} from './RolePlayCard';
import {Theme} from '../../themes';
import {heightScreen} from '../../utils/Scale';

interface RolePlayCardListProps {
  rolePlayData: RolePlayCard[];
  onChooseCard: (item: RolePlayCard) => void;
  isLoading?: boolean;
  handleRandomCard: () => void;
}

export const RolePlayCardList: React.FC<RolePlayCardListProps> = ({
  rolePlayData,
  onChooseCard,
  isLoading,
  handleRandomCard,
}) => {
  if (isLoading) {
    return (
      <View style={styles.loadingView}>
        <ActivityIndicator color={'#fff'} size={'large'} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {rolePlayData.map((item, index) => (
        <RolePlayCard
          key={index}
          color={'#6D4800'}
          href={(Theme.images as any)[item.rlFigureCode]}
          onPressCard={() => onChooseCard(item)}
        />
      ))}
      <TouchableOpacity style={styles.extraCard} onPress={handleRandomCard}>
        <FastImage
          source={Theme.images.rolePlayCardCharactor5}
          style={styles.extraCardImage}
          resizeMode="contain"
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    gap: scale(13),
    justifyContent: 'center',
    position: 'absolute',
    alignSelf: 'center',
    bottom: (255 / 2436) * heightScreen,
  },
  loadingView: {
    position: 'absolute',
    bottom: (340 / 2436) * heightScreen,
    alignSelf: 'center',
  },
  extraCard: {
    width: 80,
    height: 100,
  },
  extraCardImage: {
    width: '100%',
    height: '100%',
  },
});
