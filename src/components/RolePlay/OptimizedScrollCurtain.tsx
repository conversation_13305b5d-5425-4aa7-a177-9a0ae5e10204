import React, {
  forwardRef,
  memo,
  useCallback,
  useImperativeHandle,
  useRef,
} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {SvgIcons} from '../../../assets/svg';
import {isIOS} from '../../utils/Scale';
import TextApp from '../TextApp';
import {
  SimpleScrollCurtainMemo,
  SimpleScrollCurtainRef,
} from './SimpleScrollCurtain';
export interface OptimizedScrollCurtainRef {
  show: () => void;
  hide: () => void;
  toggle: () => void;
  animateTaskCompleted: () => void;
}

interface OptimizedScrollCurtainProps {
  enableGesture?: boolean;
  missions: MissionRolePlay[];
}

export const OptimizedScrollCurtain = forwardRef<
  OptimizedScrollCurtainRef,
  OptimizedScrollCurtainProps
>(({enableGesture = true, missions}, ref) => {
  const simpleScrollRef = useRef<SimpleScrollCurtainRef>(null);

  const handleTap = useCallback(() => {
    if (enableGesture) {
      if (simpleScrollRef.current?.isExpanded) {
        simpleScrollRef.current?.animateUp();
      } else {
        simpleScrollRef.current?.animateDown();
      }
    }
  }, [enableGesture]);

  useImperativeHandle(
    ref,
    () => ({
      show: () => {
        setTimeout(() => {
          simpleScrollRef.current?.animateDown();
        }, 50);
      },
      hide: () => {
        simpleScrollRef.current?.animateUp();
      },
      toggle: () => {
        if (simpleScrollRef.current?.isExpanded) {
          simpleScrollRef.current?.animateUp();
        } else {
          simpleScrollRef.current?.animateDown();
        }
      },
      animateTaskCompleted: () => {
        simpleScrollRef.current?.animateTaskCompleted();
      },
    }),
    [],
  );

  return (
    <SimpleScrollCurtainMemo
      ref={simpleScrollRef}
      enableGesture={enableGesture}>
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={handleTap}
        style={styles.contentContainer}>
        <TextApp
          preset="text_md_semibold"
          text={'TASK'}
          textColor="#7C3603"
          style={styles.headerText}
        />
        <View style={styles.missionWrapper}>
          {missions.map((mission, index) => (
            <View key={index} style={styles.missionItem}>
              <View
                style={[
                  styles.missionStatusIndicator,
                  {
                    backgroundColor: mission.status ? '#4CA30D' : '#fff',
                    borderColor: mission.status ? '#4CA30D' : '#D5D7DA',
                  },
                ]}>
                {mission.status && (
                  <SvgIcons.Check width={15} height={15} stroke={'#fff'} />
                )}
              </View>
              <View style={{flex: 1}}>
                <TextApp
                  preset="text_md_regular"
                  text={mission.value}
                  style={styles.missionText}
                  textColor="#181D27"
                />
              </View>
            </View>
          ))}
        </View>
      </TouchableOpacity>
    </SimpleScrollCurtainMemo>
  );
});

export const OptimizedScrollCurtainMemo = memo(OptimizedScrollCurtain);

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
    width: '100%',
  },
  headerText: {
    lineHeight: 24,
    textAlign: 'center',
    marginTop: isIOS ? 45 : 30,
  },
  missionWrapper: {
    gap: 16,
    marginHorizontal: 20,
    marginTop: 10,
  },
  missionItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  missionStatusIndicator: {
    width: 20,
    height: 20,
    borderRadius: 20,
    borderWidth: 1,
    // borderColor: '#4CA30D',
    justifyContent: 'center',
    alignItems: 'center',
    // backgroundColor: '#4CA30D',
  },
  missionText: {
    lineHeight: 24,
    marginLeft: 8,
  },
});
