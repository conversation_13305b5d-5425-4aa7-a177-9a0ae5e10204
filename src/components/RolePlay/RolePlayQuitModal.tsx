import React from 'react';
import {StyleSheet, TouchableOpacity, View, ViewBase} from 'react-native';
import {moderateVerticalScale} from 'react-native-size-matters';
import IconClosed from '../../../assets/svgIcons/IconClosed';
import {CustomBottomSheet} from '../BottomSheet';
import TextApp from '../TextApp';
import {Theme} from '../../themes';
import {SvgIcons} from '../../../assets/svg';
import Spacer from '../Spacer';

interface RolePlayQuitModalProps {
  visible: boolean;
  onClose: () => void;
  onQuit: () => void;
}

export const RolePlayQuitModal: React.FC<RolePlayQuitModalProps> = ({
  visible,
  onClose,
  onQuit,
}) => {
  return (
    <CustomBottomSheet
      visible={visible}
      isOverlay
      containerStyle={styles.container}>
      <View style={styles.content}>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <IconClosed />
        </TouchableOpacity>
        <View style={{position: 'absolute', top: -150}}>
          <SvgIcons.BoyConfused />
        </View>
        <Spacer size={20} />
        <TextApp
          preset="display_xs_medium"
          text={'Are you sure you want to quit??'}
          style={styles.title}
        />

        <View style={{flexDirection: 'row', gap: 10}}>
          {/* <TouchableOpacity style={styles.confirmButton} onPress={onQuit}>
            <TextApp
              preset="text_sm_semibold"
              text={`Yes`}
              style={styles.quitButtonText}
              textColor="#414651"
            />
          </TouchableOpacity> */}
          <TouchableOpacity style={styles.quitButton} onPress={onQuit}>
            <TextApp
              preset="text_sm_semibold"
              text={`Ok, I'll try it later`}
              style={styles.quitButtonText}
              textColor="#fff"
            />
          </TouchableOpacity>
        </View>
      </View>
    </CustomBottomSheet>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    bottom: 60,
    left: 50,
    right: 50,
    borderRadius: 16,
    paddingBottom: Theme.spacing.spacing_2xl,
    zIndex: 99,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    alignSelf: 'flex-end',
  },
  title: {
    lineHeight: 28,
    textAlign: 'center',
    marginBottom: 16,
  },
  quitButton: {
    flex: 1,
    height: moderateVerticalScale(44),
    backgroundColor: '#ff9800',
    paddingVertical: 12,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quitButtonText: {
    color: '#fff',
    lineHeight: 18,
  },
  confirmButton: {
    flex: 1,
    height: moderateVerticalScale(44),
    borderWidth: 1,
    borderColor: '#D5D7DA',
    paddingVertical: 12,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
