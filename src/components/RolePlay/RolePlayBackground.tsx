import React, {memo, useMemo} from 'react';
import {SharedValue, useDerivedValue} from 'react-native-reanimated';
import {
  Canvas,
  Image,
  useImage,
  Group,
  Skottie,
  Skia,
} from '@shopify/react-native-skia';
import {Theme} from '../../themes';
import {heightScreen, widthScreen} from '../../utils/Scale';
import FastImage from 'react-native-fast-image';
import {StyleSheet, View} from 'react-native';

interface RolePlayBackgroundProps {
  modalContent: RolePlayCard | null;
  trasnY: SharedValue<number>;
  opacity: SharedValue<number>;
}

export const RolePlayBackground: React.FC<RolePlayBackgroundProps> = memo(
  ({modalContent, trasnY, opacity}) => {
    const nameImage = useImage(Theme.images.rolePlayName);

    // Safely create Skottie animation with proper error handling
    const animation = useMemo(() => {
      try {
        // Load the Lottie file properly - you need to require/import the JSON file
        const lottieData = require('../../../assets/lotties/cheppy_blinked.json');
        return Skia.Skottie.Make(JSON.stringify(lottieData));
      } catch (error) {
        console.warn('Failed to load Lottie animation:', error);
        return null;
      }
    }, []);

    const characterImageSource = useMemo(
      () =>
        (Theme.images as any)[modalContent?.rlFigureCode || ''] ??
        Theme.images.rolePlayCharactorDefault,
      [modalContent?.rlFigureCode],
    );
    const characterImage = useImage(characterImageSource);

    const nameTransform = useDerivedValue(
      () => [{translateY: trasnY.value}],
      [trasnY],
    );

    const characterTransform = useDerivedValue(
      () => [
        {translateX: (115.51 / 375) * widthScreen},
        {translateY: (310.3 / 812) * heightScreen},
      ],
      [],
    );

    if (!nameImage || !characterImage) {
      return null;
    }

    return (
      <View style={styles.container}>
        <FastImage
          source={Theme.images.rolePlayBlur}
          style={styles.blur}
          resizeMode="stretch"
        />

        <FastImage
          source={Theme.images.bgRolePlay}
          style={styles.bg}
          resizeMode="stretch"
        />

        <Canvas style={styles.canvasName}>
          <Group transform={nameTransform}>
            <Image
              image={nameImage}
              x={0}
              y={0}
              width={widthScreen}
              height={(248.16 / 812) * heightScreen}
              fit="fill"
            />
          </Group>
        </Canvas>

        <Canvas style={styles.canvasCharactor}>
          <Group transform={characterTransform} opacity={opacity}>
            {/* Temporarily disabled Skottie to prevent crashes */}
            {/* {animation && (
              <Skottie
                animation={animation}
                frame={Math.min(41, animation.duration * animation.fps - 1)}
              />
            )} */}
            <Image
              image={characterImage}
              x={0}
              y={0}
              width={(144.98 / 375) * widthScreen}
              height={(191.4 / 812) * heightScreen}
              fit="contain"
            />
          </Group>
        </Canvas>
      </View>
    );
  },
);
const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: '100%',
  },
  blur: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: -2,
  },
  bg: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  canvasName: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: -1,
  },
  canvasCharactor: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
});
