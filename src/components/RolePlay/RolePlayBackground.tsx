import React, {memo, useMemo, useEffect} from 'react';
import {SharedValue, useDerivedValue, useSharedValue, withRepeat, withTiming} from 'react-native-reanimated';
import {
  Canvas,
  Image,
  useImage,
  Group,
  Skottie,
  Skia,
  rect,
  Rect,
} from '@shopify/react-native-skia';
import {Theme} from '../../themes';
import {heightScreen, widthScreen} from '../../utils/Scale';
import FastImage from 'react-native-fast-image';
import {StyleSheet, View} from 'react-native';

interface RolePlayBackgroundProps {
  modalContent: RolePlayCard | null;
  trasnY: SharedValue<number>;
  opacity: SharedValue<number>;
}

export const RolePlayBackground: React.FC<RolePlayBackgroundProps> = memo(
  ({modalContent, trasnY, opacity}) => {
    const nameImage = useImage(Theme.images.rolePlayName);

    // Load Skottie animation properly
    const animation = useMemo(() => {
      try {
        // Try with a simpler animation first
        const lottieData = require('../../../assets/lotties/.json');
        const skottieAnimation = Skia.Skottie.Make(JSON.stringify(lottieData));
        if (skottieAnimation) {
          console.log('Skottie animation loaded:', {
            duration: skottieAnimation.duration,
            fps: skottieAnimation.fps,
            size: skottieAnimation.size,
          });
        }
        return skottieAnimation;
      } catch (error) {
        console.warn('Failed to load Skottie animation:', error);
        return null;
      }
    }, []);

    // Simple frame counter for testing
    const animatedFrame = useSharedValue(0);

    useEffect(() => {
      if (animation) {
        // Simple loop from 0 to 30 frames
        animatedFrame.value = withRepeat(
          withTiming(30, { duration: 2000 }),
          -1,
          false
        );
      }
    }, [animation]);

    const characterImageSource = useMemo(
      () =>
        (Theme.images as any)[modalContent?.rlFigureCode || ''] ??
        Theme.images.rolePlayCharactorDefault,
      [modalContent?.rlFigureCode],
    );
    const characterImage = useImage(characterImageSource);

    const nameTransform = useDerivedValue(
      () => [{translateY: trasnY.value}],
      [trasnY],
    );

    const characterTransform = useDerivedValue(
      () => [
        {translateX: (115.51 / 375) * widthScreen},
        {translateY: (310.3 / 812) * heightScreen},
      ],
      [],
    );

    if (!nameImage || !characterImage) {
      return null;
    }

    return (
      <View style={styles.container}>
        <FastImage
          source={Theme.images.rolePlayBlur}
          style={styles.blur}
          resizeMode="stretch"
        />

        <FastImage
          source={Theme.images.bgRolePlay}
          style={styles.bg}
          resizeMode="stretch"
        />

        <Canvas style={styles.canvasName}>
          <Group transform={nameTransform}>
            <Image
              image={nameImage}
              x={0}
              y={0}
              width={widthScreen}
              height={(248.16 / 812) * heightScreen}
              fit="fill"
            />
          </Group>
        </Canvas>

        <Canvas style={styles.canvasCharactor}>
          <Group transform={characterTransform} opacity={opacity}>
            {animation ? (
              <Group
                transform={[
                  {translateX: 0},
                  {translateY: 0},
                  {scale: Math.min(
                    ((144.98 / 375) * widthScreen) / 200, // Assuming animation is ~200px wide
                    ((191.4 / 812) * heightScreen) / 200  // Assuming animation is ~200px tall
                  )},
                ]}
              >
                <Skottie
                  animation={animation}
                  frame={animatedFrame}
                />
              </Group>
            ) : (
              <Image
                image={characterImage}
                x={0}
                y={0}
                width={(144.98 / 375) * widthScreen}
                height={(191.4 / 812) * heightScreen}
                fit="contain"
              />
            )}
          </Group>
        </Canvas>
      </View>
    );
  },
);
const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: '100%',
  },
  blur: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: -2,
  },
  bg: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  canvasName: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: -1,
  },
  canvasCharactor: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
});
