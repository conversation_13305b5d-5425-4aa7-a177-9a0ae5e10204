import React from 'react';
import {View} from 'react-native';
import {scale, verticalScale} from 'react-native-size-matters';

type SpacerProps = {
  size?: number;
  direction?: 'vertical' | 'horizontal';
};

const Spacer: React.FC<SpacerProps> = ({size = 8, direction = 'vertical'}) => {
  return (
    <View
      style={
        direction === 'vertical'
          ? {height: verticalScale(size)}
          : {width: scale(size)}
      }
    />
  );
};

export default Spacer;
