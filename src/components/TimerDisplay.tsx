import React from 'react';
import {View} from 'react-native';
import TextApp from './TextApp';
import IconClock from '../../assets/svgIcons/IconClock';
import Spacer from './Spacer';
import {useTheme} from '../hooks/useTheme';
import {moderateVerticalScale, scale} from 'react-native-size-matters';
import {isIOS} from '../utils/Scale';

interface TimerDisplayProps {
  formattedTime: string;
}

const TimerDisplay: React.FC<TimerDisplayProps> = React.memo(
  ({formattedTime}) => {
    const theme = useTheme();

    return (
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#FFE2C7',
          borderRadius: 20,
          paddingVertical: 4,
          width: scale(60),
        }}>
        <View style={{position: 'absolute', left: -12}}>
          <IconClock />
        </View>
        <View style={{marginLeft: scale(6), width: '75%'}}>
          <TextApp
            text={formattedTime}
            preset={'text_md_medium'}
            textColor={theme.text_tertiary}
            style={{lineHeight: 20}}
          />
        </View>
      </View>
    );
  },
);

TimerDisplay.displayName = 'TimerDisplay';

export default TimerDisplay;
