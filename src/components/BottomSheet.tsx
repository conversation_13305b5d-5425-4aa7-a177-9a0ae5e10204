import React from 'react';
import {
  StyleSheet,
  TouchableWithoutFeedback,
  View,
  ViewStyle,
} from 'react-native';
import Animated, {
  FadeIn,
  FadeOut,
  SlideInDown,
  SlideOutDown,
} from 'react-native-reanimated';
import {Theme} from '../themes';
import {isIOS} from '../utils/Scale';

type CustomBottomSheet = {
  visible: boolean;
  containerStyle?: ViewStyle | ViewStyle[];
  onClose?: () => void;
  children: React.ReactElement;
  isOverlay?: boolean;
  overlayColor?: ViewStyle;
};

export const CustomBottomSheet = ({
  visible,
  containerStyle,
  onClose,
  children,
  isOverlay = false,
  overlayColor,
}: CustomBottomSheet) => {
  if (!visible) return null;
  return (
    <View style={styles.modalOverlay} pointerEvents="auto">
      {isOverlay && (
        <TouchableWithoutFeedback onPress={onClose}>
          <Animated.View
            style={[styles.overlay, overlayColor]}
            entering={FadeIn.duration(500)}
            exiting={FadeOut.duration(500)}
          />
        </TouchableWithoutFeedback>
      )}
      <Animated.View
        style={[styles.container, containerStyle]}
        entering={SlideInDown.duration(400)}
        exiting={SlideOutDown.duration(400)}>
        {children}
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    paddingHorizontal: Theme.spacing.spacing_2xl,
    paddingTop: Theme.spacing.spacing_md,
    paddingBottom: isIOS
      ? Theme.spacing.spacing_5xl
      : Theme.spacing.spacing_2xl,
    position: 'absolute',
    bottom: 0,
  },
  modalOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'flex-end',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 99,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
});
