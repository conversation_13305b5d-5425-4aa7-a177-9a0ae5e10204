import React from 'react';
import {Modal, View, Text, StyleSheet, TouchableOpacity} from 'react-native';

interface MissionCompleteModalProps {
  onSeeFullTalk: () => void;
  onContinue: () => void;
}

export const TopicCardFinishModal: React.FC<MissionCompleteModalProps> = ({
  onSeeFullTalk,
  onContinue,
}) => {
  return (
    <View style={styles.modalContainer}>
      {/* Character placeholder */}
      <View style={styles.characterBox} />

      {/* Confetti, Ribbon can be added as decoration if needed */}
      <Text style={styles.title}>MISSION COMPLETE!</Text>

      <View style={styles.statsContainer}>
        <View style={styles.statBox}>
          <Text style={styles.statLabel}>You talked</Text>
          <Text style={styles.statValueRed}>89%</Text>
        </View>
        <View style={styles.statBox}>
          <Text style={styles.statLabel}>You earned</Text>
          <Text style={styles.statValue}>50 EXP</Text>
        </View>
        <View style={styles.statBox}>
          <Text style={styles.statLabel}>You got</Text>
          <Text style={styles.statValue}>5</Text>
        </View>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={onSeeFullTalk}>
          <Text style={styles.buttonText}>See full talk</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.button} onPress={onContinue}>
          <Text style={styles.buttonText}>Continue</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    width: 300,
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  characterBox: {
    width: 100,
    height: 100,
    backgroundColor: '#ccc',
    borderRadius: 10,
    marginBottom: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#f58220',
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 20,
  },
  statBox: {
    alignItems: 'center',
    flex: 1,
  },
  statLabel: {
    fontSize: 14,
    color: '#555',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 4,
  },
  statValueRed: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 4,
    color: 'red',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 10,
  },
  button: {
    backgroundColor: '#ff9900',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 10,
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});
