import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import Button from '../Button';
import FastImage from 'react-native-fast-image';
import {Theme} from '../../themes';
import TextApp from '../TextApp';
import {moderateVerticalScale, scale} from 'react-native-size-matters';

interface TopicCardContentModalProps {
  modalContent: any | null;
  onClose: () => void;
  onStartNow: () => void;
}

export const TopicCardContentModal: React.FC<TopicCardContentModalProps> = ({
  modalContent,
  onClose,
  onStartNow,
}) => {
  return (
    <View style={styles.outerContainer}>
      <TouchableOpacity style={styles.closeButton} onPress={onClose}>
        <FastImage
          source={Theme.images.closeContentRolePlay}
          style={styles.closeIcon}
          resizeMode="contain"
        />
      </TouchableOpacity>

      <View style={styles.innerContainer}>
        <TextApp
          text={modalContent?.title}
          preset="text_xl_bold"
          textColor="#E06A00"
          style={styles.titleText}
        />

        <FastImage
          resizeMode="contain"
          source={Theme.images.mypet}
          style={{width: 150, height: 100, marginBottom: 20, marginTop: 10}}
        />

        <TextApp
          preset="text_lg_bold"
          text={modalContent?.question}
          style={styles.questionText}
        />

        <TextApp
          preset="text_xs_regular"
          text={modalContent?.mission}
          style={styles.missionTextModal}
          textColor="#181D27"
        />
        <View style={styles.rewardContainer}>
          <TextApp text={`Reward:`} style={styles.rewardText} />
          <TextApp
            preset="text_sm_bold"
            text={modalContent?.reward}
            style={styles.rewardText}
          />
          <FastImage source={Theme.icons.gem} style={styles.pearlIcon} />
        </View>
        <TouchableOpacity onPress={onStartNow}>
          <FastImage
            source={Theme.images.startNowRolePlay}
            style={styles.startNowButton}
            resizeMode="contain"
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  outerContainer: {
    backgroundColor: '#FFFFC8',
    borderRadius: 22,
    padding: 19,
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: -10,
    top: -10,
    width: 25,
    height: 25,
    backgroundColor: '#D9D9D9',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  innerContainer: {
    backgroundColor: '#FFFFC8',
    borderWidth: 1,
    borderColor: '#F99A3D',
    borderRadius: 19,
    paddingHorizontal: 15,
    paddingVertical: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleText: {
    textAlign: 'center',
    lineHeight: 32,
  },
  questionText: {
    lineHeight: 18,
    textAlign: 'center',
  },
  missionTextModal: {
    textAlign: 'center',
    marginVertical: moderateVerticalScale(10),
  },
  rewardContainer: {
    width: 160,
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#EDE3A9',
    paddingVertical: 10,
    marginVertical: 10,
    borderRadius: 8,
  },
  rewardText: {
    marginRight: 8,
    lineHeight: 18,
  },
  startNowButton: {
    width: 197.96,
    height: 57.22,
    alignSelf: 'center',
  },
  closeIcon: {
    width: 45,
    height: 45,
  },
  pearlIcon: {
    width: 21,
    height: 21,
    resizeMode: 'contain',
  },
});
