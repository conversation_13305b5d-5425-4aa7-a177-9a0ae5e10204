import React from 'react';
import {StyleSheet, View} from 'react-native';
import Svg, {G, Image} from 'react-native-svg';
import {Theme} from '../../themes';
import FastImage from 'react-native-fast-image';

export const TopicCardBackground = () => {
  return (
    <View style={styles.container}>
      <FastImage
        source={Theme.images.bgTopicCard}
        style={styles.bg}
        resizeMode="stretch"
      />
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: '100%',
  },
  bg: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
});
