import React from 'react';
import {
  ImageBackground,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Animated, {
  FadeIn,
  FadeOut,
  ZoomIn,
  ZoomOut,
} from 'react-native-reanimated';
import {RecordButtonWithRipple} from '../../../common/animation/RippleRecordButton';
import {
  moderateScale,
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import {heightScreen} from '../../utils/Scale';
import {SvgIcons} from '../../../assets/svg';
import TextApp from '../TextApp';
import {Theme} from '../../themes';

type TopicCardMissionViewProps = {
  ccActive: boolean;
  ideaActive: boolean;
  handleMission: () => void;
  onCCPress: () => void;
  onIdeaPress: () => void;
};

export const TopicCardMissionView = ({
  ccActive,
  ideaActive,
  handleMission,
  onCCPress,
  onIdeaPress,
}: TopicCardMissionViewProps) => {
  return (
    <View>
      {/* <Animated.View
        entering={ZoomIn.delay(500)}
        exiting={ZoomOut.delay(500)}
        style={styles.container}>
        <Text style={styles.missionTitle}>MISSIONS NEED TO COMPLETED</Text>
        <TouchableOpacity style={styles.missionItem} onPress={handleMission}>
          <View style={styles.missionStatusIncomplete} />
          <Text style={styles.missionText}>Tell 2 favorite foods 1/2</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.missionItem}>
          <View style={styles.missionStatusComplete} />
          <Text style={styles.missionText}>Know Cheppy's favorite food</Text>
        </TouchableOpacity>
        <RecordButtonWithRipple />
      </Animated.View> */}
      <Animated.View
        entering={ZoomIn.delay(500)}
        exiting={ZoomOut.delay(500)}
        style={styles.container}>
        <ImageBackground
          source={Theme.images.subFrameTopicCard}
          style={{width: 404.22, height: 190.86}}
          resizeMode="contain">
          <View style={styles.recordButtonContainer}>
            <RecordButtonWithRipple />
          </View>
        </ImageBackground>
      </Animated.View>

      <Animated.View
        entering={FadeIn.delay(500)}
        exiting={FadeOut.delay(500)}
        style={styles.bottomButtonsContainer}>
        <TouchableOpacity
          style={[
            styles.actionButton,
            {backgroundColor: ccActive ? '#FFF3AB' : '#597F00'},
          ]}
          onPress={onCCPress}>
          <TextApp
            preset="text_md_bold"
            text={'CC'}
            textColor={ccActive ? '#7F5500' : '#FEFF9B'}
            style={styles.actionButtonText}
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.actionButton,
            {backgroundColor: ideaActive ? '#FFF3AB' : '#597F00'},
          ]}
          onPress={onIdeaPress}>
          {ideaActive ? <SvgIcons.IdeaActive /> : <SvgIcons.Idea />}
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    alignSelf: 'center',
    bottom: (480 / 2436) * heightScreen,
  },
  missionTitle: {
    fontSize: moderateScale(20),
    fontWeight: 'bold',
    color: '#FFF',
    textAlign: 'center',
  },
  missionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: verticalScale(10),
  },
  missionStatusIncomplete: {
    width: scale(24),
    height: scale(24),
    borderRadius: scale(24),
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#FF7E00',
    marginRight: scale(10),
  },
  missionStatusComplete: {
    width: scale(24),
    height: scale(24),
    borderRadius: scale(24),
    backgroundColor: '#00FF00',
    marginRight: scale(10),
  },
  missionText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#fff',
  },
  bottomButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: moderateVerticalScale(5),
    position: 'absolute',
    alignSelf: 'center',
    width: '85%',
    bottom: (180 / 2436) * heightScreen,
  },
  actionButton: {
    width: scale(57),
    height: moderateVerticalScale(32),
    paddingVertical: 5,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtonText: {
    lineHeight: 20,
  },
  recordButtonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: -25,
  },
});
