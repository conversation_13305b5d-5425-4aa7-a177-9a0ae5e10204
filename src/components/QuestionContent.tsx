import React from 'react';
import Animated, {ZoomIn, ZoomOut} from 'react-native-reanimated';
import SelectTemplate from './templates/SelectTemplate';
import WellDone from './templates/WellDone';
import {goBack} from '../navigation/NavigationServices';

interface QuestionContentProps {
  currentPage: number;
  done: boolean;
  data: any[];
  passed: number;
  total: number;
  onPreview: () => void;
  styles: any;
}

const QuestionContent: React.FC<QuestionContentProps> = React.memo(
  ({currentPage, done, data, passed, total, onPreview, styles}) => {
    return (
      <Animated.View
        key={currentPage}
        entering={ZoomIn}
        exiting={ZoomOut}
        style={styles.centerBox}>
        {done ? (
          <WellDone
            onPress={() => goBack()}
            answer={`${passed}/${total}`}
            onPreview={onPreview}
          />
        ) : (
          <SelectTemplate data={data[currentPage]} />
        )}
      </Animated.View>
    );
  },
);

QuestionContent.displayName = 'QuestionContent';

export default QuestionContent;
