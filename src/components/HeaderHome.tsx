import React from 'react';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import {navigate} from '../navigation/NavigationServices.ts';
import {APP_SCREEN} from '../navigation/screenType.ts';
import {useTypedSelector} from '../redux/store.ts';
import {HIT_SLOP, initTop, isAndroid, SCREEN_WIDTH} from '../utils/Scale';
import {LevelBadge} from './LevelBadge.tsx';
import TextApp from './TextApp/index.tsx';
import {Icons} from '../themes/icons.ts';

export const HomeHeader = () => {
  const {data, countNotification} = useTypedSelector(state => state.profile);

  const goToProfile = () => {
    navigate(APP_SCREEN.PROFILE);
  };
  const handleNotification = () => {
    navigate(APP_SCREEN.NOTIFICATION);
  };
  return (
    <View style={styles.container}>
      <View style={styles.contentContainer}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            alignSelf: 'flex-start',
            width: '70%',
          }}>
          <TouchableOpacity onPress={goToProfile}>
            <FastImage
              source={{uri: data?.photoUrl}}
              style={styles.boxAvatar}
            />
          </TouchableOpacity>
          <TouchableOpacity style={styles.boxName} onPress={goToProfile}>
            <TextApp
              text={'Welcome back'}
              preset={'text_sm_regular'}
              style={{lineHeight: 20}}
            />
            <TextApp
              text={data?.name ?? ''}
              preset={'text_md_bold'}
              style={{
                lineHeight: 24,
                marginBottom: moderateVerticalScale(4),
              }}
            />
            <LevelBadge
              label={`Lv. ${data?.userLevelName.slice(0, 10)}`}
              size="small"
            />
          </TouchableOpacity>
        </View>

        <View style={styles.boxRight}>
          <View style={styles.total}>
            <Image
              source={Icons.gem}
              style={{
                width: 24,
                height: 24,
              }}
            />
            <TextApp
              preset="text_md_regular"
              text={data?.coins}
              style={{marginLeft: scale(4)}}
            />
          </View>
          <TouchableOpacity hitSlop={HIT_SLOP} onPress={handleNotification}>
            <Image
              source={Icons.noti}
              style={{
                width: 24,
                height: 24,
              }}
            />
            {countNotification > 0 && (
              <View style={styles.count}>
                <TextApp
                  text={countNotification}
                  preset="text_xs_medium"
                  style={{lineHeight: 16}}
                  textColor={'#fff'}
                />
              </View>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    position: 'absolute',
    zIndex: 1,
    top: isAndroid ? verticalScale(36) : initTop,
    width: '100%',
  },
  contentContainer: {
    width: SCREEN_WIDTH,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  boxAvatar: {
    width: 72,
    height: 72,
    borderRadius: 36,
    backgroundColor: '#FFEECD',
  },
  boxName: {
    flex: 1,
    marginHorizontal: 10,
  },
  total: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: scale(10),
  },
  notification: {
    width: scale(20),
    height: scale(20),
    borderRadius: 20,
    backgroundColor: '#D9D9D9',
    marginLeft: scale(20),
  },
  boxRight: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  count: {
    width: 16,
    height: 16,
    backgroundColor: '#D92D20',
    borderRadius: 16,
    position: 'absolute',
    right: -2,
    top: -5,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
