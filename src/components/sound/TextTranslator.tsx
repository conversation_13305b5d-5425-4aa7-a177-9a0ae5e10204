import React, { useMemo } from 'react';
import { StyleSheet, TouchableOpacity, View, ViewStyle } from 'react-native';
import IconVolume from '../../../assets/svgIcons/IconVolume.tsx';
import { useTheme } from '../../hooks/useTheme.ts';
import { useTTS } from '../../hooks/useTTS.tsx';
import soundService from '../../services/soundService.ts';
import TextApp from '../TextApp';
import WordTooltip from './WordTooltip.tsx';

interface TextTranslatorProps {
  text: string;
  styleText?: ViewStyle;
  children?: React.ReactNode;
  audio?: string;
  scores?: ScoreData[];
  disable: boolean;
}

interface ScoreData {
  word: string;
  score_grade: string;
}

const getColorByGrade = (grade?: string) => {
  switch (grade) {
    case 'A':
      return '#4CA30D';
    case 'B':
      return '#4CA30D';
    case 'C':
      return '#CA8504';
    case 'D':
      return '#D92D20';
    default:
      return '#181D27';
  }
};

const TextTranslator: React.FC<TextTranslatorProps> = ({
  text,
  styleText,
  children,
  audio,
  scores = [],
  disable,
}) => {
  const words = useMemo(() => text?.split(' '), [text]);
  const {speakWord} = useTTS();
  const theme = useTheme();
  const wordColorMap = useMemo(() => {
    return scores.reduce(
      (acc, {word, score_grade}) => {
        acc[word.toLowerCase()] = getColorByGrade(score_grade);
        return acc;
      },
      {} as Record<string, string>,
    );
  }, [scores]);

  return (
    <View style={styles.container}>
      {!children && (
        <TouchableOpacity
          disabled={disable}
          style={styles.speakBtn}
          onPress={() =>
            audio ? soundService?.changeSource(audio) : speakWord(text)
          }>
          <IconVolume width={45} height={44} />
        </TouchableOpacity>
      )}
      {scores?.length > 0
        ? scores.map((i, idx) => (
            <WordTooltip
              wordInfo={i}
              key={idx}
              word={i.word}
              textColor={getColorByGrade(i.score_grade)}
              styleText={[styleText]}
            />
          ))
        : words.map((word, index) => (
            <TextApp
              key={index}
              preset="text_xl_semibold"
              style={{
                marginLeft: 4,

                letterSpacing: 0.5,
                justifyContent: 'space-around',
                textAlign: 'center',
                lineHeight: 30,
              }}
              textColor={'#181D27'}
              text={word}
            />
          ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {flexDirection: 'row', flexWrap: 'wrap', alignItems: 'center'},
  speakerImg: {height: 15, width: 15},
  speakBtn: {
    width: 45,
    height: 45,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 5,
  },
});

export default React.memo(TextTranslator);
