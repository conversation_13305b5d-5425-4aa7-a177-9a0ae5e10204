import React, {useRef, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import Popover from 'react-native-popover-view';
import Animated, {FadeIn, FadeOut} from 'react-native-reanimated';

import {useTTS} from '../../hooks/useTTS.tsx';
import {Theme} from '../../themes';
import TextApp from '../TextApp';
import {removePunctuation} from '../../utils/index.ts';
import {processPhonemeScores} from '../../utils/processPhonemeScores.ts';

interface WordTooltipProps {
  word: string;
  styleText?: any;
  textColor: string;
  wordInfo?: any;
}

const WordTooltip: React.FC<WordTooltipProps> = ({
  word,
  styleText,
  textColor,
  wordInfo,
}) => {
  const [visible, setVisible] = useState(false);
  const popoverRef = useRef(null);
  const {speakWord} = useTTS();

  const result = processPhonemeScores(wordInfo?.phoneme_scores);
  const isHidden = textColor === '#4CA30D';

  const handlePress = () => {
    speakWord(removePunctuation(word));
    setVisible(true);
  };

  return (
    <Popover
      ref={popoverRef}
      isVisible={visible}
      popoverStyle={styles.container}
      onRequestClose={() => setVisible(false)}
      from={
        <TouchableOpacity onPress={handlePress}>
          <TextApp
            preset="text_xl_semibold"
            style={[styles.text, styleText]}
            textColor={textColor}
            text={word}
          />
        </TouchableOpacity>
      }>
      <Animated.View
        entering={FadeIn}
        exiting={FadeOut}
        style={styles.container}>
        {result?.textSegments && (
          <View style={styles.segmentRow}>
            <TextApp
              preset="text_md_semibold"
              text={`/${result.textSegments}/`}
              style={styles.word}
              textColor="#000000"
            />
          </View>
        )}

        <View style={styles.soundsLikeContainer}>
          {result?.textSoundsLike?.map((i, idx) => {
            const isLast = idx === result.textSoundsLike.length - 1;
            return (
              <View
                key={idx}
                style={[
                  styles.soundsLikeRow,
                  !isLast && styles.soundsLikeRowBorder,
                ]}>
                <TextApp
                  preset="text_md_medium"
                  text={i.text === '-' ? '' : `/${i.text}/`}
                  style={styles.soundText}
                  textColor={i.color}
                />
                <TextApp
                  preset="text_md_medium"
                  text={`/${i.ipa}/`}
                  style={styles.ipa}
                  textColor="#000000"
                />
              </View>
            );
          })}
        </View>

        {/* <TextApp
          preset="text_sm_semibold"
          text={wordInfo?.meaning}
          style={styles.meaning}
        /> */}
      </Animated.View>
    </Popover>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 12,
    backgroundColor: Theme.colors.background,
    borderRadius: 10,
    alignItems: 'center',
    borderColor: Theme.colors.border,
  },
  text: {
    marginLeft: 4,
    textDecorationLine: 'underline',
    letterSpacing: 0.5,
    textAlign: 'center',
    lineHeight: 30,
  },
  word: {
    marginLeft: 2,
    paddingVertical: 8,
    textAlign: 'left',
    color: Theme.colors.blue['700'],
  },
  segmentRow: {
    flexDirection: 'row',
    width: 180,
  },
  soundsLikeContainer: {
    alignItems: 'center',
  },
  soundsLikeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: 180,
    paddingHorizontal: 4,
  },
  soundsLikeRowBorder: {
    borderBottomWidth: 1,
    borderColor: '#D5D7DA',
  },
  soundText: {
    marginLeft: 2,
    width: 90,
    borderRightWidth: 1,
    borderColor: '#D5D7DA',
    textAlign: 'left',
    paddingVertical: 8,
  },
  ipa: {
    marginLeft: 2,
    textAlign: 'right',
    color: Theme.colors.gray['700'],
    paddingVertical: 8,
  },
  meaning: {
    color: Theme.colors.textPrimary,
  },
});

export default React.memo(WordTooltip);
