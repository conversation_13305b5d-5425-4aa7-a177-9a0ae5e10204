import React, {useState} from 'react';
import {
  Image,
  ImageBackground,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import ListViewProfile from './ListViewProfile.tsx';
import {useReduxDispatch, useTypedSelector} from '../redux/store.ts';
import {changeCharacter} from '../redux/reducer/ProfileSlice.ts';
import {moderateScale, moderateVerticalScale} from 'react-native-size-matters';
import {isAndroid, widthScreen} from '../utils/Scale.ts';

export type ClothingCategory = 'shirt' | 'pants' | 'mask' | 'pet' | 'glasses';

export interface ClothingItem {
  id: string;
  category: ClothingCategory;
  image: any;
  selected?: boolean;
}

const tabs: {label: string; key: ClothingCategory; icon: any}[] = [
  {
    label: 'Shirt',
    key: 'shirt',
    icon: require('../../assets/images/profiles/shirrt.webp'),
  },
  {
    label: 'Pants',
    key: 'pants',
    icon: require('../../assets/images/profiles/pants.webp'),
  },

  {
    label: 'glasses',
    key: 'glasses',
    icon: require('../../assets/images/profiles/glasses.webp'),
  },
  {
    label: 'Mask',
    key: 'mask',
    icon: require('../../assets/images/profiles/mask.webp'),
  },
  {
    label: 'Pet',
    key: 'pet',
    icon: require('../../assets/images/profiles/pet.webp'),
  },
];
const findAssets = (data: any, key: string) => {
  if (!data) {return;}
  return data.find((item: any) => item.typeCode === key)?.wardrobes;
};
const Wardrobe: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState<ClothingCategory>('shirt');
  const Assets = useTypedSelector(state => state.profile.assetsCharacter);
  const character = useTypedSelector(state => state.profile.character);
  const dispatch = useReduxDispatch();
  const handleChange = (type: ClothingCategory, id: number) => {
    if (type === 'shirt') {
      dispatch(changeCharacter({...character, shirtId: id}));
    }
    if (type === 'pants') {
      dispatch(changeCharacter({...character, pantsId: id}));
    }
    if (type === 'glasses') {
      dispatch(changeCharacter({...character, accessoryId: id}));
    }
    if (type === 'mask') {
      dispatch(changeCharacter({...character, faceId: id}));
    }
    if (type === 'pet') {
      dispatch(changeCharacter({...character, petsId: id}));
    }
  };
  return (
    <View style={styles.container}>
      <ImageBackground
        resizeMode={'stretch'}
        source={require('../../assets/images/profiles/wardrobeBar.webp')}
        style={styles.tabs}>
        {tabs.map(tab => (
          <TouchableOpacity
            key={tab.key}
            style={[
              styles.tabButton,
              selectedTab === tab.key && styles.tabButtonActive,
            ]}
            onPress={() => setSelectedTab(tab.key)}>
            <Image source={tab.icon} style={styles.tabIcon} />
          </TouchableOpacity>
        ))}
      </ImageBackground>
      <ImageBackground
        source={require('../../assets/images/profiles/bgListWardobeBar.webp')}
        resizeMode={'stretch'}
        style={{
          flex: 1,
          zIndex: 5,
          marginTop: -10,
        }}>
        {selectedTab === 'shirt' && (
          <ListViewProfile
            data={findAssets(Assets, 'SHIRT')}
            onSelect={item => handleChange(selectedTab, item.id)}
            id={character.shirtId}
          />
        )}
        {selectedTab === 'pants' && (
          <ListViewProfile
            data={findAssets(Assets, 'PANTS')}
            onSelect={item => handleChange(selectedTab, item.id)}
            id={character.pantsId}
          />
        )}
        {selectedTab === 'glasses' && (
          <ListViewProfile
            data={findAssets(Assets, 'GLASSES')}
            onSelect={item => handleChange(selectedTab, item.id)}
            id={character.accessoryId}
          />
        )}
        {selectedTab === 'mask' && (
          <ListViewProfile
            data={findAssets(Assets, 'FACE')}
            onSelect={item => handleChange(selectedTab, item.id)}
            id={character.faceId}
          />
        )}
        {selectedTab === 'pet' && (
          <ListViewProfile
            data={findAssets(Assets, 'PET')}
            onSelect={item => handleChange(selectedTab, item.id)}
            id={character.petsId}
          />
        )}
      </ImageBackground>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E5AE00',
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 12,
    height: 78,
    width: widthScreen + 10,
    zIndex: 10,
    alignSelf: 'center',
  },
  tabButton: {
    padding: 5,
    borderRadius: 8,
    marginTop: moderateVerticalScale(-13),
  },
  tabButtonActive: {
    // backgroundColor: '#FFD580',
    borderWidth: 3,
    borderColor: '#FFE576',
  },
  tabIcon: {
    width: moderateScale(isAndroid ? 32 : 32),
    height: moderateVerticalScale(isAndroid ? 32 : 32),
    resizeMode: 'stretch',
  },
  grid: {
    padding: 10,
  },
  itemBox: {
    width: '30%',
    margin: '1.6%',
    backgroundColor: '#532B00',
    padding: 10,
    borderRadius: 10,
    alignItems: 'center',
  },
  itemSelected: {
    backgroundColor: '#FFDEAD',
    borderWidth: 2,
    borderColor: '#FF8612',
  },
  itemImage: {
    width: 60,
    height: 60,
    resizeMode: 'contain',
  },
});

export default Wardrobe;
