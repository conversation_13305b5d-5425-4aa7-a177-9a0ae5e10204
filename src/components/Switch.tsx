import React, {useState} from 'react';
import {Switch as RNSwitch} from 'react-native';

interface SwitchProps {
    value?: boolean;
    onChange?: (value: boolean) => void;
}

const Switch: React.FC<SwitchProps> = ({value = false, onChange}) => {
    const [isOn, setIsOn] = useState(value);

    const toggleSwitch = () => {
        setIsOn(!isOn);
        onChange?.(!isOn);
    };

    return <RNSwitch value={isOn} onValueChange={toggleSwitch}/>;
};

export default Switch;
