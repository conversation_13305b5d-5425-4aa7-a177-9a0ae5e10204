import React from 'react';
import {StyleSheet, View} from 'react-native';
import Animated, {
  Easing,
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import {scale} from 'react-native-size-matters';
import {Theme} from '../themes';
import {useTheme} from '../hooks/useTheme.ts';

const DOTS = [0, 1, 2];
const DURATION = 1200;
const ACTIVE_COLOR = '#FF8612';
const INACTIVE_COLOR = '#ffccaa';

function Dot({index}: {index: number}) {
  const progress = useSharedValue(0);

  React.useEffect(() => {
    const delay = (DURATION / DOTS.length) * index;

    progress.value = withDelay(
      delay,
      withRepeat(
        withTiming(1, {
          duration: DURATION,
          easing: Easing.linear,
        }),
        -1,
        false,
      ),
    );
  }, []);

  const style = useAnimatedStyle(() => {
    const color = interpolateColor(
      progress.value,
      [0, 0.2, 0.333, 1],
      [INACTIVE_COLOR, ACTIVE_COLOR, ACTIVE_COLOR, INACTIVE_COLOR],
    );
    return {
      backgroundColor: color,
    };
  }, []);

  return <Animated.View style={[styles.dot, style]} />;
}

export default function LoadingDots() {
  const theme = useTheme();
  return (
    <View
      style={[styles.container, {backgroundColor: theme.bg_brand_secondary}]}>
      {DOTS.map((_, i) => (
        <Dot key={i} index={i} />
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: scale(72),
    height: scale(72),
    borderRadius: Theme.radius.radius_full,
  },
  dot: {
    width: 14,
    height: 14,
    borderRadius: 7,
    marginHorizontal: 4,
    backgroundColor: INACTIVE_COLOR,
  },
});
