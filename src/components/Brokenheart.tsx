import React, {useCallback, useState} from 'react';
import {Dimensions, StyleSheet, View} from 'react-native';
import Animated, {Easing, runOnJS, useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import {Images} from '../themes/images.ts';

const SCREEN_WIDTH = Dimensions.get('window').width;

const AnimatedImage = Animated.createAnimatedComponent(Animated.Image);

export const useBrokenHeart = () => {
  const [visible, setVisible] = useState(false);

  const leftTranslateX = useSharedValue(0);
  const rightTranslateX = useSharedValue(0);
  const leftRotate = useSharedValue(0);
  const rightRotate = useSharedValue(0);
  const opacity = useSharedValue(0);

  const showBrokenHeart = useCallback(() => {
    setVisible(true);

    opacity.value = withTiming(1, {duration: 200});

    leftTranslateX.value = withTiming(-100, {
      duration: 1000,
      easing: Easing.out(Easing.exp),
    });
    rightTranslateX.value = withTiming(100, {
      duration: 1000,
      easing: Easing.out(Easing.exp),
    });
    leftRotate.value = withTiming(-15, {duration: 1000});
    rightRotate.value = withTiming(15, {duration: 1000});

    setTimeout(() => {
      opacity.value = withTiming(0, {duration: 1000}, finished => {
        if (finished) {
          runOnJS(setVisible)(false);
        }
      });

      leftTranslateX.value = 0;
      rightTranslateX.value = 0;
      leftRotate.value = 0;
      rightRotate.value = 0;
    }, 800);
  }, []);

  const animatedStyleLeft = useAnimatedStyle(() => ({
    transform: [
      {translateX: leftTranslateX.value},
      {rotate: `${leftRotate.value}deg`},
    ],
    opacity: opacity.value,
  }));

  const animatedStyleRight = useAnimatedStyle(() => ({
    transform: [
      {translateX: rightTranslateX.value},
      {rotate: `${rightRotate.value}deg`},
    ],
    opacity: opacity.value,
  }));

  const BrokenHeartView = visible ? (
    <View style={styles.heartPiece}>
      <AnimatedImage
        source={Images.brokenHeartLeft}
        style={[styles.heartImage, {width: 124}, animatedStyleLeft]}
        resizeMode="contain"
      />
      <AnimatedImage
        source={Images.brokenHeartRight}
        style={[styles.heartImage, {width: 160}, animatedStyleRight]}
        resizeMode="contain"
      />
    </View>
  ) : null;

  return {
    BrokenHeartView,
    showBrokenHeart,
  };
};

const styles = StyleSheet.create({
  heartPiece: {
    position: 'absolute',

    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
    flexDirection: 'row',
    top: 100,
    alignSelf: 'center',
  },
  heartImage: {
    height: 228,
  },
});
