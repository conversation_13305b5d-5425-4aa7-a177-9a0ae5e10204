import React from 'react';
import {StyleSheet, View} from 'react-native';
import {SvgIcons} from '../../assets/svg';
import {scale} from 'react-native-size-matters';
import TextApp from './TextApp';
import {useTheme} from '../hooks/useTheme';
import Spacer from './Spacer';
import {useTranslate} from '../hooks/useTranslate';

type PasswordRuleItem = {
  validateLength?: boolean;
  hasNumber?: boolean;
  isMatch?: boolean;
};

export const PasswordRule = ({
  validateLength,
  hasNumber,
  isMatch,
}: PasswordRuleItem) => {
  const theme = useTheme();
  const {t} = useTranslate();

  const getColor = (isValid?: boolean) => {
    if (isValid === true) return theme.text_success_primary;
    if (isValid === false) return theme.text_error_primary;
    return theme.text_pleaceholder_subtle;
  };

  const rules = [
    {
      label: t('signup.passwordRequirements.length'),
      isValid: validateLength,
    },
    {
      label: t('signup.passwordRequirements.specialCharacter'),
      isValid: hasNumber,
    },
    {
      label: t('signup.passwordRequirements.match'),
      isValid: isMatch,
    },
  ];

  return (
    <View style={styles.container}>
      {rules.map((rule, index) => {
        const color = getColor(rule.isValid);
        return (
          <View key={index}>
            <View style={styles.rules}>
              <View style={[styles.check, {backgroundColor: color}]}>
                <SvgIcons.Check width={10} height={10} stroke={'#fff'} />
              </View>
              <TextApp
                preset="text_sm_regular"
                text={rule.label}
                textColor={color}
              />
            </View>
            {index < rules.length - 1 && <Spacer size={10} />}
          </View>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: scale(15),
  },
  rules: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  check: {
    width: 16,
    height: 16,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: scale(8),
  },
});
