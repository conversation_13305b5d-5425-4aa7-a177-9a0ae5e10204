import React from 'react';
import {
  ActivityIndicator,
  Animated,
  Dimensions,
  FlatList,
  Image,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {navigate} from '../navigation/NavigationServices.ts';
import {APP_SCREEN} from '../navigation/screenType.ts';
import {useReduxDispatch} from '../redux/store.ts';
import {resetDataQuestion} from '../redux/reducer/QuestionSlice.ts';
import {Theme} from '../themes';

const {width} = Dimensions.get('window');

const DATA = Array.from({length: 20}, (_, i) => i); // 10 hình tròn
const CIRCLE_RADIUS = width * 0.2;
const ITEM_SIZE = 100;

interface RoadMapProps {
  data: any[];
  loading: boolean;
}

const RoadMap: React.FC<RoadMapProps> = ({data, loading}) => {
  const scrollY = React.useRef(new Animated.Value(0)).current;
  const dispatch = useReduxDispatch();
  const handleNextScreen = (item: any) => {
    dispatch(resetDataQuestion());
    navigate(APP_SCREEN.QUESTIONS, item);
  };
  let lastDoneIndex = -1;

  for (let i = data.length - 1; i >= 0; i--) {
    if (data[i].isDone) {
      lastDoneIndex = i;
      break;
    }
  }
  const CircularItem = ({
    item,
    index,
  }: {
    item: any;
    index: number;
    scrollY: Animated.Value;
  }) => {
    const angle = (index - 7) * (Math.PI / 5);

    const translateX = CIRCLE_RADIUS * Math.cos(angle);

    return (
      <TouchableOpacity
        // disabled={index == lastDoneIndex ? false : true}
        onPress={() => handleNextScreen(item)}>
        <Animated.View
          style={{
            width: ITEM_SIZE,
            height: ITEM_SIZE,
            borderRadius: ITEM_SIZE / 2,
            backgroundColor: '#DE773D',
            marginVertical: 20,
            transform: [{translateX}],
            alignSelf: 'center',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          {item?.isDone ? (
            <Image
              source={Theme.icons.done}
              style={{width: 50, height: 50}}
              resizeMode={'contain'}
            />
          ) : (
            <Text>{item?.name?.toUpperCase()}</Text>
          )}
        </Animated.View>
      </TouchableOpacity>
    );
  };
  if (loading) {
    return <ActivityIndicator size={'small'} />;
  }
  return (
    <View style={{flex: 1}}>
      <FlatList
        data={data}
        keyExtractor={item => item.id}
        renderItem={({item, index}) => (
          <CircularItem item={item} index={index} scrollY={scrollY} />
        )}
        contentContainerStyle={{
          alignItems: 'center',
          paddingBottom: 100,
        }}
      />
    </View>
  );
};

export default RoadMap;
