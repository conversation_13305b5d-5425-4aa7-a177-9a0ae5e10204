import React from 'react';
import {
  Pressable,
  StyleSheet,
  View,
  ViewStyle
} from 'react-native';
import { scale } from 'react-native-size-matters';
import { SvgIcons } from '../../assets/svg';
import { useTheme } from '../hooks/useTheme';
import { useTranslate } from '../hooks/useTranslate';
import { Theme } from '../themes';
import TextApp from './TextApp';

type Props = {
  style?: ViewStyle;
};
export const ChangeLanguageButton = ({style}: Props) => {
  const theme = useTheme();
  const {language, changeLanguage} = useTranslate();

  const handleChangeLanguage = () => {
    const newLanguage = language === 'vi' ? 'en' : 'vi';
    changeLanguage(newLanguage);
  };

  return (
    <View style={[styles.container, style]}>
      <Pressable
        style={[
          styles.btn,
          {
            backgroundColor: theme.bg_tertiary,
          },
        ]}
        onPress={handleChangeLanguage}>
        {language === 'vi' ? <SvgIcons.FlagVN /> : <SvgIcons.FlagEN />}
        <TextApp
          preset="text_xs_regular"
          text={language === 'vi' ? 'VIE' : 'ENG'}
          style={{marginHorizontal: scale(6)}}
          textColor={theme.text_primary}
        />
      </Pressable>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {},
  btn: {
    alignSelf: 'flex-end',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: Theme.spacing.spacing_sm,
    paddingHorizontal: Theme.spacing.spacing_sm,
    borderRadius: Theme.radius.radius_full,
  },
});
