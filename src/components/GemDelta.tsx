// GemDelta.tsx
import React, {useEffect} from 'react';
import {StyleSheet, Text} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withSequence,
  withTiming,
} from 'react-native-reanimated';

type Props = {
  value: number; // +50 hoặc -50
  color?: string;
  delay?: number;
};

const GemDelta: React.FC<Props> = ({value, color = '#fff', delay = 0}) => {
  const translateY = useSharedValue(0);
  const opacity = useSharedValue(0);

  useEffect(() => {
    opacity.value = withDelay(
      delay,
      withSequence(
        withTiming(1, {duration: 200}),
        withTiming(0, {duration: 600}),
      ),
    );
    translateY.value = withDelay(delay, withTiming(-20, {duration: 800}));
  }, []);

  const rStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{translateY: translateY.value}],
  }));

  return (
    <Animated.View style={[styles.container, rStyle]}>
      <Text style={[styles.text, {color}]}>
        {value > 0 ? `+${value}` : `${value}`}
      </Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: -10,
    alignSelf: 'center',
  },
  text: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default GemDelta;
