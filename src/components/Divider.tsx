import React from 'react';
import {StyleSheet, View} from 'react-native';

interface DividerProps {
    color?: string;
    height?: number;
}

const Divider: React.FC<DividerProps> = ({color = '#ddd', height = 1}) => {
    return <View style={[styles.divider, {backgroundColor: color, height}]}/>;
};

const styles = StyleSheet.create({
    divider: {
        width: '100%',
        marginVertical: 8,
    },
});

export default Divider;
