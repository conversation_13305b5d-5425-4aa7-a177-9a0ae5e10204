import React, {useState} from 'react';
import {
  FlatList,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import Svg, {Ellipse} from 'react-native-svg';
import FastImage from 'react-native-fast-image';
import {getImages} from '../utils/getImages.ts';
import {widthScreen} from '../utils/Scale.ts';

const ITEM_MARGIN = 8;
const NUM_COLUMNS = 3;
const ITEM_SIZE =
  (widthScreen * 0.9 - ITEM_MARGIN * (NUM_COLUMNS + 1)) / NUM_COLUMNS;

export interface GridItem {
  id: number;
  path: string;
  type: 'shirt' | 'pants' | 'accessory' | 'face' | 'pets';
  isVisible: 0 | 1;
}

interface Props {
  data: GridItem[];
  onSelect?: (item: GridItem, index: number) => void;
  selectedIndex?: number;
  containerStyle?: ViewStyle;
  id?: number | string;
}

const ListViewProfile: React.FC<Props> = ({
  data,
  onSelect,
  selectedIndex: externalIndex,
  containerStyle,
  id,
}) => {
  const [internalIndex, setInternalIndex] = useState<number | null>(
    externalIndex ?? null,
  );
  const selectedIndex = externalIndex ?? internalIndex;

  const renderItem = ({item, index}: {item: GridItem; index: number}) => {
    const isSelected = index === selectedIndex || item.id === id;

    return (
      <TouchableOpacity
        style={[
          styles.itemContainer,
          {
            borderWidth: isSelected ? 4 : 0,
            borderColor: isSelected ? '#FBE7A1' : 'transparent',
            backgroundColor: isSelected ? '#FFEAB0' : 'transparent',
          },
        ]}
        onPress={() => {
          if (externalIndex === undefined) {
            setInternalIndex(index);
          }
          onSelect?.(item, index);
        }}
        activeOpacity={0.8}>
        <FastImage
          source={getImages(item?.path, true)}
          style={styles.image}
          resizeMode="contain"
        />
        {isSelected ? (
          <View
            style={{
              height: 10,
            }}
          />
        ) : (
          <Svg width="60" height="10">
            <Ellipse cx="30" cy="5" rx="30" ry="5" fill="#B26E00" />
          </Svg>
        )}
      </TouchableOpacity>
    );
  };
  if (!data) {
    return <></>;
  }
  return (
    <FlatList
      data={data}
      keyExtractor={item => item.id.toString()}
      renderItem={renderItem}
      numColumns={NUM_COLUMNS}
      contentContainerStyle={[styles.list, containerStyle]}
      columnWrapperStyle={styles.row}
      scrollEnabled={false}
    />
  );
};

const styles = StyleSheet.create({
  list: {
    padding: ITEM_MARGIN,
    paddingHorizontal: 30,
    paddingTop: 30,
  },
  row: {
    // justifyContent: 'space-between',
    marginBottom: ITEM_MARGIN,
  },
  itemContainer: {
    width: ITEM_SIZE,
    height: ITEM_SIZE * 0.7,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  image: {
    width: '70%',
    height: '70%',
  },
});

export default ListViewProfile;
