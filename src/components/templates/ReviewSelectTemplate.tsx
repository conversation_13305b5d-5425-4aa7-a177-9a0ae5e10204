import React, {useMemo} from 'react';
import {View} from 'react-native';
import {
  ReviewPronunciationTemplate,
  ReviewFillInTheBlankTemplate,
  ReviewMultipleChoice,
  ReviewMatchingPairsTemplate,
  ReviewArrangeWordsTemplate,
  ReviewDragDropTemplate,
  ReviewDropDownTemplate,
  ReviewArrangeDialogueTemplate,
} from './review';

export interface ReviewSelectTemplateProps {
  data: LessonData;
}

const templateMap: Record<ExerciseType, React.FC<any>> = {
  pronunciation: ReviewPronunciationTemplate,
  fill_in_the_blank: ReviewFillInTheBlankTemplate,
  multiple_choice: ReviewMultipleChoice,
  matching_pairs: ReviewMatchingPairsTemplate,
  drag_and_drop: ReviewDragDropTemplate,
  arrangement: ReviewArrangeWordsTemplate,
  dropdown: ReviewDropDownTemplate,
  arrarrangement_dialogue: ReviewArrangeDialogueTemplate,
};

const ReviewSelectTemplate: React.FC<ReviewSelectTemplateProps> = React.memo(({data}) => {
  const TemplateComponent = useMemo(() => {
    if (data.exerciseType === 'arrangement') {
      switch (data.subType) {
        case 'CHARACTER':
        case 'WORD':
          return ReviewArrangeWordsTemplate;
        case 'SENTENCE':
          return ReviewArrangeDialogueTemplate;
        default:
          return View;
      }
    }
    return templateMap[data.exerciseType] || View;
  }, [data.exerciseType, data.subType]);

  return <TemplateComponent {...data} />;
});

export default ReviewSelectTemplate;
