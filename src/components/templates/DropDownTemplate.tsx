import React, {useEffect, useMemo, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {moderateVerticalScale, scale} from 'react-native-size-matters';
import IconSpeak from '../../../assets/svgIcons/IconSpeak.tsx';
import useQuestion from '../../hooks/auth/useQuestion.ts';
import {useTheme} from '../../hooks/useTheme.ts';
import {Theme} from '../../themes';
import {getImages} from '../../utils/getImages.ts';
import {isAndroid, SCREEN_WIDTH} from '../../utils/Scale.ts';
import ButtonFooter from '../ButtonFooter.tsx';
import {SelectPicker} from '../SelectPicker.tsx';
import TextApp from '../TextApp/index.tsx';
import FastImage from 'react-native-fast-image';
import {useTypedSelector} from '../../redux/store.ts';
import {parseStringToParts} from '../../utils/parseStringToParts.ts';
import soundService from '../../services/soundService.ts';

interface DropDownTemplateProps {
  optionsLst: any[];
  title: string;
  question: string;
  answers: string[];
  audio?: string;
  id: string;
  index: string | number;
  answerByStudent?: any;
  image: string;
  mediaType: 'AUDIO' | 'IMAGE';
  exerciseType: ExerciseType;
}

const DropDownTemplate: React.FC<DropDownTemplateProps> = ({
  optionsLst,
  title,
  question,
  id,
  index,
  answerByStudent,
  answers,
  image,
  audio,
  mediaType,
  exerciseType,
}) => {
  const isDone: boolean = useTypedSelector(state => state.question.isDone);
  const options = optionsLst?.map((item, index) => ({...item, index: index}));
  const theme = useTheme();
  const [selectedAnswers, setSelectedAnswers] = useState<string[]>(
    Array(options?.length || answers?.length).fill(''),
  );
  const [hasLoadedAnswer, setHasLoadedAnswer] = useState<boolean>(false);
  const [isSubmit, setIsSubmit] = useState<boolean>(false);
  const {handleCheckAnswer, handleShowAnswer} = useQuestion();

  const parsedTextWithBlanks = useMemo(
    () => parseStringToParts(question),
    [question],
  );

  useEffect(() => {
    if (answerByStudent?.length > 0) {
      setSelectedAnswers(answerByStudent);
      setIsSubmit(true);
      setHasLoadedAnswer(true);
    }
  }, [answerByStudent]);

  const handleSelect = (index: number, value: string) => {
    const updatedAnswers = [...selectedAnswers];
    updatedAnswers[index] = value;
    setSelectedAnswers(updatedAnswers);
  };

  const isCorrectAnswer = () =>
    answers?.every((item, i) => item === selectedAnswers[i]);

  const handleSubmit = () => {
    setIsSubmit(true);
    handleCheckAnswer(
      selectedAnswers,
      isCorrectAnswer(),
      id,
      index,
      exerciseType,
      answers,
      question,
    );
  };

  const isAllAnswered = options?.every((_, index) => !!selectedAnswers[index]);

  return (
    <View
      style={[
        styles.container,
        {
          paddingTop: isDone
            ? moderateVerticalScale(isAndroid ? 130 : 150)
            : moderateVerticalScale(130),
        },
      ]}>
      <TextApp
        text={title}
        preset="text_md_semibold"
        textColor={theme.text_secondary}
        style={{marginTop: isDone ? moderateVerticalScale(10) : 0}}
      />
      <View style={styles.shadowWrapper}>
        <TouchableOpacity
          activeOpacity={1}
          disabled={mediaType !== 'AUDIO'}
          style={styles.boxMedia}
          onPress={() => soundService?.changeSource(audio || '')}>
          {mediaType === 'AUDIO' ? (
            <IconSpeak />
          ) : (
            <FastImage
              resizeMode={'cover'}
              source={getImages(image, true)}
              defaultSource={Theme.images.boyAvt}
              style={styles.image}
            />
          )}
        </TouchableOpacity>
      </View>
      <View style={styles.sentenceContainer}>
        {parsedTextWithBlanks?.map((part: any, i) => {
          const blankIndex = part.index ?? 0;
          if (part.type === 'text') {
            return (
              <TextApp
                key={i}
                preset="text_sm_medium"
                text={part.value}
                textColor={theme.text_secondary}
              />
            );
          }
          return (
            <SelectPicker
              key={`blank-${i}`}
              isSubmit={isSubmit}
              selectedAnswers={selectedAnswers}
              blankIndex={blankIndex}
              options={options}
              selectedValue={selectedAnswers[blankIndex] || ''}
              callbackSelectOption={handleSelect}
              disabled={isSubmit}
            />
          );
        })}
      </View>

      {hasLoadedAnswer && answerByStudent?.length > 0 && !isCorrectAnswer() && (
        <ButtonFooter
          btnCheck={handleShowAnswer?.bind(
            null,
            answerByStudent,
            exerciseType,
            answers,
            question,
          )}
          title="Show the answer"
        />
      )}

      {!isDone && isAllAnswered && (
        <ButtonFooter
          disabled={!isAllAnswered}
          btnCheck={handleSubmit}
          title="Submit"
        />
      )}
      {isDone && (
        <View
          style={{
            height: 400,
            width: '100%',
            position: 'absolute',
            top: 360,
            zIndex: 30,
          }}
        />
      )}
    </View>
  );
};

export default DropDownTemplate;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: SCREEN_WIDTH,
    backgroundColor: '#fff',
    alignItems: 'center',
    paddingTop: moderateVerticalScale(135),
  },
  shadowWrapper: {
    shadowColor: '#0A0D121A',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 1,
    shadowRadius: 3,
    elevation: 5,
  },
  boxMedia: {
    width: scale(300),
    height: scale(200),
    backgroundColor: '#FFFFFF',
    borderRadius: Theme.radius.radius_2xl,
    marginBottom: moderateVerticalScale(25),
    marginTop: moderateVerticalScale(20),
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  sentenceContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginHorizontal: Theme.spacing.spacing_3xl,
    gap: Theme.spacing.spacing_lg,
  },
});
