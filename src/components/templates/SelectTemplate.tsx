import React, {useMemo} from 'react';
import {View} from 'react-native';
import PronunciationTemplate from './PronunciationTemplate';
import FillInTheBlankTemplate from './FillInTheBlankTemplate';
import MultipleChoice from './MultipleChoice.tsx';
import MatchingPairsTemplate from './MatchingPairsTemplate';
import ArrangeWordsTemplate from './ArrangeWordsTemplate.tsx';
import DragDropTemplate from './DragDropTemplate.tsx';
import DropDownTemplate from './DropDownTemplate.tsx';
import ArrangeDialogueTemplate from './ArrangeDialogueTemplate.tsx';

export interface SelectTemplateProps {
  data: LessonData;
}

const templateMap: Record<ExerciseType, React.FC<any>> = {
  pronunciation: PronunciationTemplate,
  fill_in_the_blank: FillInTheBlankTemplate,
  multiple_choice: MultipleChoice,
  matching_pairs: MatchingPairsTemplate,
  drag_and_drop: DragDropTemplate,
  arrangement: ArrangeWordsTemplate,
  dropdown: DropDownTemplate,
  arrarrangement_dialogue: ArrangeDialogueTemplate,
};

const SelectTemplate: React.FC<SelectTemplateProps> = React.memo(({data}) => {
  const TemplateComponent = useMemo(() => {
    if (data.exerciseType === 'arrangement') {
      switch (data.subType) {
        case 'CHARACTER':
        case 'WORD':
          return ArrangeWordsTemplate;
        case 'SENTENCE':
          return ArrangeDialogueTemplate;
        default:
          return View;
      }
    }
    return templateMap[data.exerciseType] || View;
  }, [data.exerciseType, data.subType]);

  return <TemplateComponent {...data} />;
});

export default SelectTemplate;
