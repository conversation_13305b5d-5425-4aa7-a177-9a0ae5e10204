import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import Animated, {
  measure,
  runOnJS,
  runOnUI,
  useAnimatedRef,
} from 'react-native-reanimated';
import {Theme} from '../../themes';
import ButtonFooter from '../ButtonFooter.tsx';
import {getImages} from '../../utils/getImages.ts';
import TextApp from '../TextApp';
import {useTheme} from '../../hooks/useTheme.ts';
import FastImage from 'react-native-fast-image';
import useQuestion from '../../hooks/auth/useQuestion.ts';
import FlyingItem from '../item/FlyingItem.tsx';
import {isAndroid} from '../../utils/Scale.ts';
import IconSpeak from '../../../assets/svgIcons/IconSpeak.tsx';
import {useTypedSelector} from '../../redux/store.ts';
import {parseStringToParts} from '../../utils/parseStringToParts.ts';
import soundService from '../../services/soundService.ts';

interface DragDropTemplateProps {
  options: string[];
  question: string;
  title: string;
  answers: string[];
  id: string;
  index: string | number;
  answerByStudent?: any;
  image?: string;
  audio?: string;
  questions: any[];
  mediaType: 'AUDIO' | 'IMAGE';
  exerciseType: ExerciseType;
}

function isCorrectAnswers(data: any, correctAnswers: any) {
  if (data.length !== correctAnswers?.length) {
    return false;
  }

  return data.every(
    (item: any, index: any) => item?.answer === correctAnswers[index],
  );
}

function arraysEqual(a: string[], b: string[]) {
  if (a.length !== b.length) {
    return false;
  }

  for (let i = 0; i < a.length; i++) {
    if (a[i] !== b[i]) {
      return false;
    }
  }

  return true;
}

const DragDropTemplate: React.FC<DragDropTemplateProps> = ({
  question,
  title,
  id,
  image,
  answers,
  options,
  index,
  answerByStudent,
  exerciseType,
  mediaType,
  audio,
}) => {
  const isDone: boolean = useTypedSelector(state => state.question.isDone);
  const [questionsCtr, setQuestionsCtr] = useState<any[]>(
    parseStringToParts(question),
  );

  const [submitted, setSubmitted] = useState(false);
  const [target, setTarget] = useState<any[]>([]);
  const [start, setStart] = useState<any[]>([]);

  // eslint-disable-next-line react-hooks/rules-of-hooks
  const itemRefs = useRef(options.map(() => useAnimatedRef<Animated.View>()));
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const startRefs = useRef(options.map(() => useAnimatedRef<Animated.View>()));
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const checkDisable = questionsCtr?.some(
    item => item.type !== 'text' && item?.answer === null,
  );

  const {handleCheckAnswer, handleShowAnswer} = useQuestion();

  useEffect(() => {
    if (answerByStudent) {
      setSubmitted(true);
    }
  }, [answerByStudent]);
  useEffect(() => {
    const timeout = setTimeout(() => {
      runOnUI(() => {
        const targets = itemRefs.current.map(ref => {
          const m = measure(ref);
          return m ? {x: m.pageX, y: m.pageY, h: m.height, w: m.width} : null;
        });
        const starts = startRefs.current.map(ref => {
          const m = measure(ref);
          return m ? {x: m.pageX, y: m.pageY, h: m.height, w: m.width} : null;
        });

        if (targets.every(Boolean)) {
          runOnJS(setTarget)(targets);
        }
        if (starts.every(Boolean)) {
          runOnJS(setStart)(starts);
        }
      })();
    }, 500);

    return () => clearTimeout(timeout);
  }, []);

  const handleSelectOption = useCallback(
    (value: string, id: number | string) => {
      setQuestionsCtr(prev => {
        const updated = [...prev];
        const existIndex = updated.findIndex(q => q?.id === id);

        if (existIndex !== -1) {
          updated[existIndex] = {
            ...updated[existIndex],
            answer: null,
            id: null,
          };
          return updated;
        }

        const emptyIndex = updated.findIndex(q => q?.id === null);
        if (emptyIndex !== -1) {
          updated[emptyIndex] = {...updated[emptyIndex], answer: value, id};
        }

        return updated;
      });
    },
    [],
  );

  const isCorrectAnswer = isCorrectAnswers(
    questionsCtr.filter(m => m.answer),
    answers,
  );
  const handleSubmit = useCallback(() => {
    handleCheckAnswer(
      questionsCtr.filter(m => m.answer).map(m => m.answer),
      isCorrectAnswer,
      id,
      index,
      exerciseType,
      answers,
      question,
    );
    setSubmitted(true);
  }, [questionsCtr]);

  if (!questionsCtr) {
    return <></>;
  }
  return (
    <View
      key={id}
      style={[
        styles.container,
        {
          paddingTop: isDone
            ? moderateVerticalScale(isAndroid ? 130 : 150)
            : moderateVerticalScale(130),
        },
      ]}>
      <TextApp
        preset="text_md_semibold"
        textColor={theme.text_secondary}
        style={{
          textAlign: 'center',
          marginTop: isDone ? moderateVerticalScale(10) : 0,
        }}
        text={title}
      />
      <View style={styles.shadowWrapper}>
        <TouchableOpacity
          activeOpacity={1}
          disabled={mediaType !== 'AUDIO'}
          style={styles.boxMedia}
          onPress={() => soundService?.changeSource(audio || '')}>
          {mediaType === 'AUDIO' ? (
            <IconSpeak />
          ) : (
            <FastImage
              resizeMode={'cover'}
              source={getImages(image, true)}
              defaultSource={Theme.images.boyAvt}
              style={styles.image}
            />
          )}
        </TouchableOpacity>
      </View>
      <View style={styles.sentenceContainer}>
        {questionsCtr.map((part, index) => {
          if (part.type === 'text') {
            return (
              <TextApp
                key={`${part?.value}tex${index}`}
                text={part.value}
                textColor={theme.text_secondary}
                preset={'text_sm_medium'}
              />
            );
          }

          return (
            <Animated.View
              key={part.index}
              ref={itemRefs.current[part.index]}
              style={[
                styles.blankBox,
                part?.w && {width: part.w, borderWidth: 0},
                submitted && styles.blankBoxSubmitted,
              ]}
            />
          );
        })}
      </View>

      {!answerByStudent && (
        <View style={styles.optionsContainer}>
          {options.map((o, idx) => (
            <Animated.View
              key={`${o}+ ${idx}`}
              ref={startRefs.current[idx]}
              style={styles.optionShadow}>
              <Text style={styles.textShadow} numberOfLines={1}>
                {o}
              </Text>
            </Animated.View>
          ))}
        </View>
      )}

      {start?.length > 0 &&
        !answerByStudent &&
        options.map((op, idx) => {
          const isCorrect =
            questionsCtr.filter(q => q.type === 'blank')[idx]?.answer ===
            answers[idx];
          const isSelect = questionsCtr?.some(m => m.id === `${op}+ ${idx}`);
          return (
            <FlyingItem
              key={`${op}+ ${idx}`}
              start={start[idx]}
              isSelect={isSelect}
              isCorrect={isCorrect && submitted}
              isIncorrect={!isCorrect && submitted}
              target={
                target[
                  questionsCtr
                    .filter(q => q.type === 'blank')
                    .findIndex(q => q.answer == null)
                ] || target[idx]
              }
              text={op}
              id={`${op}+ ${idx}`}
              handleChange={handleSelectOption}
            />
          );
        })}
      {target?.length > 0 &&
        answerByStudent?.length > 0 &&
        answerByStudent.map((op: any, idx: any) => {
          const isCorrect = op === answers[idx];
          return (
            <FlyingItem
              key={`${op}+ ${idx}`}
              start={target[idx]}
              isSelect={false}
              isCorrect={isCorrect && submitted}
              isIncorrect={!isCorrect && submitted}
              target={target[idx]}
              text={op}
              id={`${op}+ ${idx}`}
              handleChange={handleSelectOption}
            />
          );
        })}
      {answerByStudent?.length > 0 &&
        !arraysEqual(answerByStudent, answers) && (
          <ButtonFooter
            btnCheck={handleShowAnswer?.bind(
              null,
              answerByStudent,
              exerciseType,
              answers,
              question,
            )}
            title="Show the answer"
          />
        )}

      {!isDone && !checkDisable && (
        <ButtonFooter
          disabled={checkDisable}
          btnCheck={handleSubmit}
          title="Submit"
        />
      )}
      {isDone && (
        <View
          style={{
            height: 400,
            width: '100%',
            position: 'absolute',
            top: 360,
            zIndex: 30,
          }}
        />
      )}
    </View>
  );
};

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: moderateVerticalScale(130),
      width: '100%',
      backgroundColor: '#fff',
    },
    shadowWrapper: {
      shadowColor: '#0A0D121A',
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 1,
      shadowRadius: 3,
      elevation: 5,
      alignItems: 'center',
    },
    boxMedia: {
      width: scale(300),
      height: scale(200),
      backgroundColor: '#FFFFFF',
      borderRadius: Theme.radius.radius_2xl,
      marginTop: moderateVerticalScale(20),
      alignItems: 'center',
      justifyContent: 'center',
      overflow: 'hidden',
    },
    image: {
      width: '100%',
      height: '100%',
    },
    questions: {
      marginVertical: verticalScale(20),
      marginHorizontal: scale(40),
    },
    questionContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
      marginBottom: 10,
    },

    blankBox: {
      borderWidth: 2,
      borderColor: '#A4A7AE',
      width: 120,
      height: 40,
      alignItems: 'center',
      maxHeight: 40,
      borderRadius: Theme.radius.radius_md,
    },
    blankBoxSubmitted: {
      backgroundColor: 'transparent',
      borderWidth: 0,
    },
    optionsContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      flexWrap: 'wrap',
      gap: 10,
      marginBottom: 20,
      marginTop: 16,
    },
    optionButton: {
      borderWidth: 2,
      borderColor: '#A4A7AE',

      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Theme.radius.radius_md,
      backgroundColor: '#FFFFFF',
    },
    optionShadow: {
      backgroundColor: '#D5D7DA',

      borderWidth: 2,
      borderColor: '#A4A7AE',

      borderRadius: Theme.radius.radius_md,
      margin: 5,

      alignItems: 'center',
      justifyContent: 'center',
      width: 120,
    },
    textShadow: {
      fontSize: 16,
      color: '#D5D7DA',

      borderRadius: Theme.radius.radius_md,
      paddingVertical: 5,
      paddingHorizontal: 2,
      width: 120,
    },

    isSelect: {
      backgroundColor: theme.bg_brand_secondary,
      borderColor: theme.bg_brand_solid,
    },
    textItem: {
      paddingVertical: 6.5,
      width: '100%',
      minWidth: 88,

      borderRadius: Theme.radius.radius_md,
      textAlign: 'center',
    },
    sentenceContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      paddingHorizontal: 16,
      alignItems: 'center',
      marginHorizontal: Theme.spacing.spacing_xl,
      gap: 6,
      marginTop: 16,
    },
  });
export default DragDropTemplate;
