import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {StyleSheet, TouchableOpacity, View, ViewStyle} from 'react-native';
import Animated, {
  Easing,
  measure,
  runOnJS,
  runOnUI,
  useAnimatedRef,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {moderateVerticalScale, scale} from 'react-native-size-matters';
import useQuestion from '../../hooks/auth/useQuestion';
import {useTheme} from '../../hooks/useTheme';
import {Theme} from '../../themes';

import {isAndroid, SCREEN_WIDTH} from '../../utils/Scale';
import ButtonFooter from '../ButtonFooter';
import TextApp from '../TextApp';
import {useTypedSelector} from '../../redux/store.ts';

interface ArrangeDialogueTemplateProps {
  options: string[];
  title: string;
  answers: string[];
  id: string;
  index: string | number;
  answerByStudent?: any;
  audio?: string;
  question: any[] | string;
  questions?: any[];
}

interface FlyingItemProps {
  start: any;
  target: any;
  text: string;
  handleChange?: (text: string, w: number) => void;
  styleItem?: ViewStyle;
  isSelect: boolean;
  isCorrect: boolean;
  isIncorrect: boolean;
}

export const FlyingItem = React.memo(
  ({
    start,
    target,
    text,
    handleChange,
    styleItem,
    isSelect,
    isCorrect,
    isIncorrect,
  }: FlyingItemProps) => {
    const theme = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);
    const x = useSharedValue(start.x);
    const y = useSharedValue(start.y);
    const width = useSharedValue(start.w);
    const height = useSharedValue(start.h);
    const atStart = useSharedValue(true);

    const animStyle = useAnimatedStyle(() => ({
      position: 'absolute',
      transform: [{translateX: x.value}, {translateY: y.value}],
      width: width.value,
      height: height.value,
    }));

    const fly = () => {
      x.value = withTiming(atStart.value ? target.x : start.x, {
        duration: 400,
        easing: Easing.inOut(Easing.ease),
      });
      y.value = withTiming(atStart.value ? target.y : start.y, {
        duration: 400,
        easing: Easing.inOut(Easing.ease),
      });
      width.value = withTiming(atStart.value ? target.w : start.w, {
        duration: 400,
        easing: Easing.inOut(Easing.ease),
      });
      height.value = withTiming(atStart.value ? target.h : start.h, {
        duration: 400,
        easing: Easing.inOut(Easing.ease),
      });
      atStart.value = !atStart.value;
    };

    return (
      <Animated.View style={animStyle}>
        <TouchableOpacity
          disabled={isCorrect || isIncorrect}
          activeOpacity={1}
          onPress={() => {
            handleChange?.(text, start.w);
            fly();
          }}
          style={[
            styles.optionButton,
            {height: start.h},
            isSelect &&
              (!isCorrect || !isIncorrect) && {
                borderColor: theme.bg_brand_solid,
                borderWidth: 2,
              },
            (isCorrect || isIncorrect) && {
              borderWidth: 0,
            },
            isIncorrect && {
              backgroundColor: theme.fg_error_primary,
            },
            isCorrect && {
              backgroundColor: theme.fg_success_primary,
            },
            styleItem,
          ]}>
          <View
            style={[
              styles.contentView,
              isSelect &&
                (!isCorrect || !isIncorrect) && {
                  backgroundColor: theme.bg_brand_secondary,
                },
              isIncorrect && {
                backgroundColor: theme.fg_error_primary,
              },
              isCorrect && {
                backgroundColor: theme.fg_success_primary,
              },
            ]}>
            <TextApp
              text={text}
              preset={'text_md_semibold'}
              textColor={
                isCorrect || isIncorrect
                  ? theme.text_white
                  : isSelect
                    ? theme.text_brand_secondary
                    : theme.text_secondary
              }
              style={{
                textAlign: 'center',
                marginHorizontal: scale(10),
                lineHeight: 20,
              }}
            />
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  },
);

const ArrangeDialogueTemplate: React.FC<ArrangeDialogueTemplateProps> = ({
  question,
  questions,
  title,
  id,
  answers,
  options,
  index,
  answerByStudent,
}) => {
  const isDone: boolean = useTypedSelector(state => state.question.isDone);
  const sourceQuestions = questions || question;
  const [selectedAnswers, setSelectedAnswers] = useState<(string | null)[]>(
    new Array(sourceQuestions?.length || 0).fill(null),
  );
  const [submitted, setSubmitted] = useState(false);
  const [target, setTarget] = useState<any[]>([]);
  const [start, setStart] = useState<any[]>([]);

  const itemRefs = useRef(options.map(() => useAnimatedRef<Animated.View>()));
  const startRefs = useRef(options.map(() => useAnimatedRef<Animated.View>()));
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const isCompleted = selectedAnswers.every(answer => answer !== null);
  const checkDisable = selectedAnswers.some(answer => answer === null);
  const {handleCheckAnswer, handleShowAnswer} = useQuestion();

  useEffect(() => {
    if (answerByStudent) {
      const answers = answerByStudent.map((item: any) => item);
      setSelectedAnswers(answers);
      setSubmitted(true);
    }
  }, [answerByStudent]);

  useEffect(() => {
    if (!answerByStudent && sourceQuestions) {
      setSelectedAnswers(new Array(sourceQuestions.length).fill(null));
    }
  }, [sourceQuestions, answerByStudent]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      runOnUI(() => {
        const targets = itemRefs.current.map(ref => {
          const m = measure(ref);
          return m ? {x: m.pageX, y: m.pageY, h: m.height, w: m.width} : null;
        });
        const starts = startRefs.current.map(ref => {
          const m = measure(ref);
          return m ? {x: m.pageX, y: m.pageY, h: m.height, w: m.width} : null;
        });

        if (targets.every(Boolean)) {runOnJS(setTarget)(targets);}
        if (starts.every(Boolean)) {runOnJS(setStart)(starts);}
      })();
    }, 500);

    return () => clearTimeout(timeout);
  }, []);

  const handleSelectOption = useCallback((value: string, w: number) => {
    setSelectedAnswers(prev => {
      const updated = [...prev];
      const existIndex = updated.findIndex(answer => answer === value);

      if (existIndex !== -1) {
        updated[existIndex] = null;
        return updated;
      }

      const emptyIndex = updated.findIndex(answer => answer === null);
      if (emptyIndex !== -1) {
        updated[emptyIndex] = value;
      }

      return updated;
    });
  }, []);

  const isCorrectAnswersNew = useCallback(() => {
    if (selectedAnswers.length !== answers.length) {return false;}
    return selectedAnswers.every(
      (selected, index) => selected === answers[index],
    );
  }, [selectedAnswers, answers]);

  const handleSubmit = useCallback(() => {
    handleCheckAnswer(
      selectedAnswers,
      isCorrectAnswersNew(),
      id,
      index,
      'arrarrangement_dialogue',
      answers,
      answerByStudent,
    );
    setSubmitted(true);
  }, [selectedAnswers, isCorrectAnswersNew, id, index, handleCheckAnswer]);

  return (
    <View
      style={[
        styles.container,
        {
          paddingTop: isDone
            ? moderateVerticalScale(isAndroid ? 130 : 150)
            : moderateVerticalScale(130),
        },
      ]}>
      <TextApp
        preset="text_md_semibold"
        textColor={theme.text_secondary}
        style={{
          textAlign: 'center',
          marginTop: isDone ? moderateVerticalScale(10) : 0,
        }}
        text={title}
      />
      <View style={styles.questions}>
        {Array.isArray(sourceQuestions) &&
          sourceQuestions.map((q: any, idx: number) => {
            const dialogueLabel = idx % 2 === 0 ? 'A:' : 'B:';
            return (
              <View key={q.id} style={styles.questionContainer}>
                <TextApp
                  text={dialogueLabel}
                  textColor={theme.text_secondary}
                  preset={'text_md_semibold'}
                  style={{textAlign: 'center', marginHorizontal: scale(16)}}
                />
                <Animated.View
                  ref={itemRefs.current[idx]}
                  style={[
                    styles.blankBox,
                    submitted && styles.blankBoxSubmitted,
                  ]}
                />
              </View>
            );
          })}
      </View>

      {!answerByStudent && (
        <View style={styles.optionsContainer}>
          {options.map((o, idx) => (
            <Animated.View
              key={o}
              ref={startRefs.current[idx]}
              style={styles.optionShadow}
            />
          ))}
        </View>
      )}

      {start?.length > 0 &&
        !answerByStudent &&
        options.map((op, idx) => {
          const currentPosition = selectedAnswers.findIndex(
            answer => answer === op,
          );
          const isCorrect =
            currentPosition !== -1 && answers[currentPosition] === op;
          const isSelect = selectedAnswers.includes(op);
          return (
            <FlyingItem
              key={idx}
              start={start[idx]}
              isSelect={isSelect}
              isCorrect={isCorrect && submitted}
              isIncorrect={isSelect && !isCorrect && submitted}
              target={
                target[selectedAnswers.findIndex(answer => answer === null)] ||
                target[idx]
              }
              text={op}
              handleChange={handleSelectOption}
            />
          );
        })}
      {target?.length > 0 &&
        answerByStudent &&
        answerByStudent?.map((op: any, idx: any) => {
          const isCorrect = selectedAnswers[idx] === answers[idx];
          const isSelect = true;
          return (
            <FlyingItem
              key={idx}
              start={target[idx]}
              isSelect={isSelect}
              isCorrect={isCorrect}
              isIncorrect={!isCorrect}
              target={target[idx]}
              text={op}
            />
          );
        })}

      {answerByStudent?.length > 0 && !isCorrectAnswersNew() && (
        <ButtonFooter
          btnCheck={handleShowAnswer?.bind(
            null,
            answerByStudent,
            'arrarrangement_dialogue',
            answers,
            answerByStudent,
          )}
          title="Show the answer"
        />
      )}
      {isDone && (
        <View
          style={{
            height: 500,
            width: '100%',
            position: 'absolute',
            top: 400,
            zIndex: 30,
          }}
        />
      )}
      {!isDone && isCompleted && (
        <ButtonFooter
          disabled={checkDisable}
          btnCheck={handleSubmit}
          title="Submit"
        />
      )}
    </View>
  );
};

export default ArrangeDialogueTemplate;

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: moderateVerticalScale(130),
      width: SCREEN_WIDTH,
      backgroundColor: '#fff',
    },
    questions: {
      marginHorizontal: scale(16),
      marginTop: moderateVerticalScale(12),
    },
    questionContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: moderateVerticalScale(16),
    },

    blankBox: {
      flex: 1,
      borderWidth: 1,
      borderColor: '#A4A7AE',
      width: '100%',
      height: moderateVerticalScale(50),
      alignItems: 'center',
      borderRadius: Theme.radius.radius_xl,
      marginLeft: scale(16),
    },
    blankBoxSubmitted: {
      backgroundColor: 'transparent',
    },
    optionsContainer: {
      justifyContent: 'center',
      marginBottom: 20,
      shadowColor: '#0A0D121A',
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 1,
      shadowRadius: 3,
      elevation: 5,
    },

    optionShadow: {
      borderWidth: 1,
      borderColor: theme.border_disabled_subtle,
      borderRadius: Theme.radius.radius_xl,
      alignItems: 'center',
      justifyContent: 'center',
      alignSelf: 'center',
      marginBottom: moderateVerticalScale(12),
      width: SCREEN_WIDTH - 40,
      height: moderateVerticalScale(50),
      backgroundColor: '#F5F5F5',
    },

    textShadow: {
      fontSize: 16,
      color: '#D5D7DA',
      borderWidth: 1,
      borderRadius: Theme.radius.radius_xl,
      paddingVertical: 6,
    },

    isSelect: {
      backgroundColor: theme.bg_brand_secondary,
      borderColor: theme.bg_brand_solid,
    },

    optionButton: {
      borderWidth: 1,
      padding: 2,
      borderColor: theme.border_primary,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Theme.radius.radius_xl,
      backgroundColor: '#FFFFFF',
      overflow: 'hidden',
    },

    contentView: {
      width: '100%',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: Theme.radius.radius_md,
    },
  });
