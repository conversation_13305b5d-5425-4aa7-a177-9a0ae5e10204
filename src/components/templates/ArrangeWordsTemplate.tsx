import DuoDragDrop, {
  DuoDragDropRef,
  Word,
} from '@jamsch/react-native-duo-drag-drop';
import {DropEvent} from '@jamsch/react-native-duo-drag-drop/lib/typescript/types';
import React, {useRef, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {
  moderateScale,
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import useQuestion from '../../hooks/auth/useQuestion.ts';
import {useTheme} from '../../hooks/useTheme.ts';
import {Theme} from '../../themes';
import {FontFamily} from '../../themes/typography.ts';
import {getImages} from '../../utils/getImages.ts';
import {
  calculateWordBankLines,
  isSingleWordQuestion,
} from '../../utils/index.ts';
import {isAndroid, SCREEN_WIDTH} from '../../utils/Scale';
import ButtonFooter from '../ButtonFooter';
import {DashedLine} from '../DashedLine.tsx';
import TextApp from '../TextApp/index.tsx';
import {useTypedSelector} from '../../redux/store.ts';

type ArrangeWordsTemplateProps = {
  answer: string;
  question: string;
  id: string;
  index: string | number;
  answerByStudent?: any;
  title: string;
  image: string;
  exerciseType: ExerciseType;
};

const ArrangeWordsTemplate: React.FC<ArrangeWordsTemplateProps> = ({
  answer,
  question,
  id,
  index,
  answerByStudent,
  title,
  image,
  exerciseType,
}) => {
  const isDone: boolean = useTypedSelector(state => state.question.isDone);
  const theme = useTheme();
  const duoDragDropRef = useRef<DuoDragDropRef>(null);
  const [gradeWords, setGradeWords] = useState<boolean[]>([]);
  const [isCompleted, setIsCompleted] = useState<boolean>(false);
  const [highlightedIndexes, setHighlightedIndexes] = useState<number[]>([]);

  const wordRenderCount: Record<string, number> = {};
  const answeredWords = duoDragDropRef.current?.getAnsweredWords() || [];

  const correctWords = isSingleWordQuestion(answer)
    ? answer.trim().split('')
    : answer.trim().split(/\s+/);

  const shuffledWords = isSingleWordQuestion(question)
    ? question.trim().split('')
    : question.trim().split(/\s+/);

  const {handleCheckAnswer, handleShowAnswer} = useQuestion();

  const isCorrectAnswer = isSingleWordQuestion(answer)
    ? correctWords?.every((item, i) => item === answeredWords[i])
    : answeredWords.join(' ') == answer;

  const handleCheck = () => {
    const grade = correctWords.map(
      (correctWord, i) => answeredWords[i] === correctWord,
    );

    setGradeWords(grade);
    setHighlightedIndexes([]);

    handleCheckAnswer(
      answeredWords,
      isCorrectAnswer,
      id,
      index,
      exerciseType,
      answer,
      question,
    );
  };

  const handleWordDrop = (event: DropEvent) => {
    const {destination, index, position} = event;

    const answeredWords = duoDragDropRef.current?.getAnsweredWords() || [];

    setIsCompleted(answeredWords?.length === correctWords?.length);

    if (destination === 'answered' && position >= 0) {
      setHighlightedIndexes(prev => [...new Set([...prev, index])]);
    } else if (destination === 'bank') {
      setHighlightedIndexes(prev => prev.filter(i => i !== index));
    }
  };

  const getWordStyles = (isCorrect: boolean | undefined, index: number) => {
    if (highlightedIndexes.includes(index)) {
      return {
        containerStyle: {
          backgroundColor: theme.bg_brand_secondary,
          borderColor: theme.bg_brand_secondary,
        },
        textStyle: [styles.textDefault, {color: theme.text_brand_secondary}],
      };
    }

    const containerStyle =
      isCorrect === true
        ? {
            backgroundColor: theme.bg_success_solid,
            borderColor: theme.bg_success_solid,
          }
        : isCorrect === false
          ? {
              backgroundColor: theme.bg_error_solid,
              borderColor: theme.bg_error_solid,
            }
          : {
              backgroundColor: theme.bg_primary,
              borderColor: theme.fg_quaternary,
            };

    const textStyle =
      isCorrect === true || isCorrect === false
        ? [styles.textWhite, {color: theme.text_primary_on_brand}]
        : [styles.textDefault, {color: theme.text_secondary}];

    return {containerStyle, textStyle};
  };

  const renderWords = (word: string, index: number) => {
    wordRenderCount[word] = (wordRenderCount[word] || 0) + 1;
    const occurrence = wordRenderCount[word];

    const matchedIndex = answeredWords.reduce((foundIndex, w, i) => {
      if (
        w === word &&
        occurrence ===
          answeredWords.slice(0, i + 1).filter(x => x === word).length
      ) {
        return i;
      }
      return foundIndex;
    }, -1);

    const isCorrect =
      matchedIndex !== -1 ? gradeWords[matchedIndex] : undefined;
    const {containerStyle, textStyle} = getWordStyles(isCorrect, index);

    return (
      <Word
        key={`${word}-${index}`}
        containerStyle={[styles.wordBase, containerStyle]}
        textStyle={textStyle}
      />
    );
  };

  const wordBankLines = calculateWordBankLines(
    shuffledWords || answerByStudent,
    SCREEN_WIDTH,
  );

  let wordBankOffsetY;

  if (wordBankLines === 1) {
    wordBankOffsetY = scale(-10);
  } else if (wordBankLines === 2) {
    wordBankOffsetY = scale(-5);
  } else if (wordBankLines === 3) {
    wordBankOffsetY = scale(20);
  } else {
    wordBankOffsetY = scale((4 - wordBankLines) * 10);
  }

  const renderAnswerByStudent = () => {
    return (
      <View
        style={{
          flexDirection: 'row',
          flexWrap: 'wrap',
          marginTop: 5,
        }}>
        {answerByStudent?.map((word: string, index: number) => (
          <View
            key={`${word}-${index}`}
            style={[
              styles.wordBase,
              {
                backgroundColor: theme.bg_primary,
                borderColor: theme.fg_quaternary,
                marginHorizontal: scale(4),
                marginBottom: verticalScale(12),
              },
              correctWords[index] === word
                ? {
                    backgroundColor: theme.bg_success_solid,
                    borderColor: theme.bg_success_solid,
                  }
                : {
                    backgroundColor: theme.bg_error_solid,
                    borderColor: theme.bg_error_solid,
                  },
            ]}>
            <TextApp
              text={word}
              preset="text_md_medium"
              textColor={theme.text_primary_on_brand}
            />
          </View>
        ))}
      </View>
    );
  };

  return (
    <View
      style={[
        styles.container,
        {
          paddingTop: isDone
            ? moderateVerticalScale(isAndroid ? 130 : 150)
            : moderateVerticalScale(130),
        },
      ]}>
      <TextApp
        text={title}
        preset="text_md_semibold"
        textColor={theme.text_secondary}
        style={{
          paddingHorizontal: 40,
          textAlign: 'center',
          marginTop: isDone ? moderateVerticalScale(10) : 0,
        }}
      />
      <View style={styles.shadowWrapper}>
        <View style={styles.boxMedia}>
          <FastImage
            resizeMode={'cover'}
            source={getImages(image, true)}
            defaultSource={Theme.images.boyAvt}
            style={styles.image}
          />
        </View>
      </View>
      <View style={styles.dragDropContainer}>
        <View style={[styles.line]}>
          <DashedLine />
          {wordBankLines > 1 && (
            <>
              {[...Array(wordBankLines - 1)].map((_, index) => (
                <React.Fragment key={index}>
                  <View style={{height: scale(45)}} />
                  <DashedLine />
                </React.Fragment>
              ))}
            </>
          )}
        </View>
        {answerByStudent?.length > 0 ? (
          renderAnswerByStudent()
        ) : (
          <DuoDragDrop
            ref={duoDragDropRef}
            words={shuffledWords}
            wordHeight={scale(35)}
            lineHeight={scale(45)}
            wordGap={4}
            wordBankOffsetY={wordBankOffsetY}
            wordBankAlignment="center"
            extraData={gradeWords}
            renderWord={renderWords}
            onDrop={handleWordDrop}
            renderPlaceholder={() => <></>}
            renderLines={() => <></>}
          />
        )}
      </View>

      {answerByStudent?.length > 0 &&
        answerByStudent.join('') !== answer.replace(/\s+/g, '') && (
          <ButtonFooter
            btnCheck={handleShowAnswer?.bind(
              null,
              answerByStudent,
              exerciseType,
              answer,
              question,
            )}
            title="Show the answer"
          />
        )}

      {!isDone && isCompleted && (
        <ButtonFooter
          btnCheck={() => {
            handleCheck();
          }}
          title="Submit"
        />
      )}
      {isDone && (
        <View
          style={{
            height: 400,
            width: '100%',
            position: 'absolute',
            top: 400,
            zIndex: 30,
          }}
        />
      )}
    </View>
  );
};

export default ArrangeWordsTemplate;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: SCREEN_WIDTH,
    backgroundColor: '#fff',
    alignItems: 'center',
    paddingTop: moderateVerticalScale(135),
  },
  centeredView: {
    alignSelf: 'center',
    marginTop: scale(20),
  },
  imageContainer: {
    width: 266,
    height: 241,
    borderRadius: 27,
    borderWidth: 2,
    borderColor: '#FF6905',
    marginTop: scale(20),
    padding: scale(20),
  },
  shadowWrapper: {
    shadowColor: '#0A0D121A',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 1,
    shadowRadius: 3,
    elevation: 5,
  },
  boxMedia: {
    width: scale(200),
    height: scale(200),
    backgroundColor: '#FFFFFF',
    borderRadius: Theme.radius.radius_2xl,
    marginTop: moderateVerticalScale(20),
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  dragDropContainer: {
    marginTop: scale(20),
    width: '90%',
  },
  wordBase: {
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: scale(10),
    borderRadius: Theme.radius.radius_xl,
    minHeight: scale(35),
  },

  textWhite: {
    fontSize: 14,
    fontFamily: FontFamily.medium,
  },
  textDefault: {
    fontSize: 14,
    fontFamily: FontFamily.medium,
  },
  line: {
    width: '100%',
    position: 'absolute',
    top: moderateScale(48),
  },
});
