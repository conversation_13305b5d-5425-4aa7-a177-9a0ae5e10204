import React from 'react';
import {StyleSheet, View} from 'react-native';
import {SvgIcons} from '../../assets/svg';

type StarDisplayProps = {
  rating: number;
};
export const StarDisplay: React.FC<StarDisplayProps> = ({rating = 0}) => {
  const stars = Array.from({length: 5}, (_, index) => {
    return index < rating ? (
      <SvgIcons.StarFill key={index} />
    ) : (
      <SvgIcons.Star key={index} />
    );
  });

  return <View style={styles.container}>{stars}</View>;
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
    marginTop: 8,
  },
});
