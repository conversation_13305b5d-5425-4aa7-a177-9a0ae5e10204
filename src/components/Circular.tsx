import React, { FC, useEffect, useMemo } from 'react';
import { PixelRatio, StyleSheet, View } from 'react-native';
import Animated, {
  useAnimatedProps,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { Circle, Svg } from 'react-native-svg';
import TextApp from './TextApp';

interface AnimatedCircularProgressProps {
  radius?: number;
  color?: string;
  trackColor?: string;
  percentage?: number;
  borderWidth?: number;
  duration?: number;
}

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

const AnimatedCircularProgress: FC<AnimatedCircularProgressProps> = ({
  radius = 24,
  color = '#5A4B8A',
  trackColor = '#E5DAF5',
  percentage = 45,
  borderWidth = 5,
  duration = 500,
}) => {
  const loaderRadius = PixelRatio.roundToNearestPixel(radius);
  const innerCircleRadii = useMemo(
    () => loaderRadius - borderWidth / 2,
    [loaderRadius, borderWidth],
  );

  const getCircumferenceData = useMemo(() => {
    const circumference = 2 * Math.PI * innerCircleRadii;
    const perc = Math.min(percentage, 100);
    const circumferencePercentage = circumference * perc * 0.01;
    return {
      circumference,
      circumferencePercentage,
      percentDiff: circumference - circumferencePercentage,
    };
  }, [percentage, innerCircleRadii]);

  const progress = useSharedValue(getCircumferenceData.circumference);

  useEffect(() => {
    progress.value = withTiming(getCircumferenceData.percentDiff, {
      duration,
    });
  }, [percentage, innerCircleRadii]);

  const animatedStrokeDashOffset = useAnimatedProps(() => ({
    strokeDashoffset: progress.value,
  }));

  return (
    <View style={styles(radius).container}>
      <Svg style={styles(radius).svg}>
        <Circle
          cx={radius}
          cy={radius}
          r={innerCircleRadii}
          stroke={trackColor}
          strokeWidth={borderWidth}
          fill="transparent"
        />
        <AnimatedCircle
          cx={radius}
          cy={radius}
          r={innerCircleRadii}
          stroke={color}
          strokeWidth={borderWidth}
          fill="transparent"
          animatedProps={animatedStrokeDashOffset}
          strokeDasharray={getCircumferenceData.circumference}
          strokeLinecap="round"
        />
      </Svg>
      <TextApp
        text={`${percentage}%`}
        preset="text_xs_regular"
        style={styles(radius).percentageText}
      />
    </View>
  );
};

const styles = (radius: number) =>
  StyleSheet.create({
    container: {
      width: radius * 2,
      height: radius * 2,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative',
    },
    svg: {
      width: radius * 2,
      height: radius * 2,
      position: 'absolute',
    },
    percentageText: {
      fontSize: radius * 0.4,
      fontWeight: 'bold',
      color: '#000',
      textAlign: 'center',
    },
  });

export default AnimatedCircularProgress;
