import React, {useCallback, useRef, useState} from 'react';
import {
  FlatList,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {Theme} from '../themes';
import {isAndroid} from '../utils/Scale';
import TextApp from './TextApp';

interface PaginationProps<T> {
  data: T[];
  onPress: (index: number, item: T) => void;
  initialIndex?: number;
  renderItemText?: (item: T) => string | number;
}

const Pagination = <T,>({
  data,
  onPress,
  initialIndex = 0,
  renderItemText = item => String(item),
}: PaginationProps<T>) => {
  const [selectedIndex, setSelectedIndex] = useState(initialIndex);
  const flatListRef = useRef<FlatList>(null);

  const itemWidth = 60;

  const handlePageChange = useCallback(
    (index: number) => {
      const clampedIndex = Math.max(0, Math.min(index, data.length - 1));
      setSelectedIndex(clampedIndex);
      onPress(clampedIndex, data[clampedIndex]);

      const offset = Math.max(0, clampedIndex * itemWidth - itemWidth * 2);
      flatListRef.current?.scrollToOffset({
        offset,
        animated: true,
      });
    },
    [data],
  );

  const scrollBy = (delta: number) => {
    handlePageChange(selectedIndex + delta);
  };

  const renderItem = useCallback(
    ({item, index}: {item: any; index: number}) => {
      const isActive = index === selectedIndex;

      return (
        <TouchableOpacity
          style={[
            styles.pageButton,
            {
              backgroundColor: isActive
                ? '#FFE2C7'
                : item.isPassExcercise
                  ? '#F3FEE7'
                  : '#FEF3F2',
              borderColor: isActive
                ? '#FF8612'
                : item.isPassExcercise
                  ? Theme.colors.success
                  : Theme.colors.error,
              borderWidth: isActive ? 2 : 0,
            },
          ]}
          onPress={() => handlePageChange(index)}>
          <TextApp
            text={item?.index + 1}
            preset={'text_sm_medium'}
            textColor={
              isActive
                ? '#B55F0D'
                : item.isPassExcercise
                  ? '#4CA30D'
                  : '#D92D20'
            }
          />
        </TouchableOpacity>
      );
    },
    [selectedIndex, handlePageChange],
  );

  const keyExtractor = useCallback(
    (_: T, index: number) => index.toString(),
    [],
  );

  return (
    <View style={styles.container}>
      {data.length > 5 && (
        <TouchableOpacity
          style={styles.arrowButton}
          onPress={() => scrollBy(-1)}>
          <Image source={Theme.icons.back} style={{width: 20, height: 20}} />
        </TouchableOpacity>
      )}

      <FlatList
        ref={flatListRef}
        horizontal
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        showsHorizontalScrollIndicator={false}
        getItemLayout={(_, index) => ({
          length: itemWidth,
          offset: itemWidth * index,
          index,
        })}
        initialScrollIndex={initialIndex}
        style={{flexGrow: 0}}
      />

      {data.length > 5 && (
        <TouchableOpacity
          style={styles.arrowButton}
          onPress={() => scrollBy(1)}>
          <Image
            source={Theme.icons.back}
            style={{
              width: 20,
              height: 20,
              transform: [{scaleX: -1}],
            }}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '80%',
    position: 'absolute',
    top: isAndroid ? 95 : 130,
    justifyContent: 'center',
    alignSelf: 'center',
  },
  pageButton: {
    width: 48,
    height: 36,
    borderRadius: Theme.radius.radius_xs,
    marginHorizontal: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  arrowButton: {
    width: 36,
    height: 36,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default Pagination;
