import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {verticalScale} from 'react-native-size-matters';
import {goBack} from '../navigation/NavigationServices.ts';
import {Theme} from '../themes';
import {HIT_SLOP, initTop, isAndroid, widthScreen} from '../utils/Scale.ts';
import Icon from './Icon';

interface HeaderProps {
  title?: string;
  rightIcon?: React.ReactNode;
  onBackPress?: () => void;
  close?: boolean;
  onPressRight?: () => void;
}

const Header: React.FC<HeaderProps> = ({
  title,
  rightIcon,
  onBackPress,
  close,
  onPressRight,
}) => {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        onPress={onBackPress || goBack}
        style={styles.icon}
        hitSlop={HIT_SLOP}>
        <Icon source={close ? Theme.icons.close : Theme.icons.back} size={20} />
      </TouchableOpacity>
      <View style={styles.boxTitle}>
        <Text style={styles.title}>{title}</Text>
      </View>
      <TouchableOpacity onPress={onPressRight}>{rightIcon}</TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    position: 'absolute',
    top: isAndroid ? verticalScale(36) : initTop,
    width: widthScreen,
    zIndex: 100,
  },
  icon: {
    marginRight: 10,
    backgroundColor: Theme.colors.background,
    padding: 8,
    borderRadius: Theme.radius.radius_md,
    borderWidth: 1,
    borderColor: '#D5D7DA',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  boxTitle: {
    position: 'absolute',
    alignItems: 'center',
    flex: 1,
    left: 0,
    right: 0,
  },
});

export default Header;
