import { API_INTEGRATE_URL } from '@env';
import React from 'react';
import { Image, StyleSheet, TouchableOpacity, View } from 'react-native';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { Theme } from '../themes';
import AnimatedCircularProgress from './Circular.tsx';
import TextApp from './TextApp';

interface CardUnitProps {
  onPress: () => void;
  title: string;
  subTitle: string;
  percentage: number;
  img: string;
}

const CardUnit: React.FC<CardUnitProps> = ({
  onPress,
  title,
  subTitle,
  percentage,
  img,
}) => {
  return (
    <TouchableOpacity style={styles.slide} onPress={onPress}>
      <Image
        resizeMode={'cover'}
        source={{uri: API_INTEGRATE_URL + 'files/get-by-path?path=' + img}}
        style={{
          width: scale(91),
          height: verticalScale(61),
          backgroundColor: Theme.colors.gray['500'],
          borderRadius: moderateScale(10),
        }}
      />
      <View
        style={{
          width: scale(140),

          height: verticalScale(61),
          justifyContent: 'center',
        }}>
        <TextApp text={title} preset="text_md_bold" />
        <TextApp text={subTitle} preset="text_sm_bold" />
      </View>
      <AnimatedCircularProgress percentage={percentage} />
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  slide: {
    width: '100%',
    backgroundColor: '#fff',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderColor: Theme.colors.orange,
    borderWidth: 2,
    alignSelf: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
});
export default CardUnit;
