import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {moderateVerticalScale} from 'react-native-size-matters';
import {Theme} from '../themes';
import {CustomBottomSheet} from './BottomSheet';
import TextApp from './TextApp';

const NotInClassModal = ({
  visible,
  onEnterClass,
  onClose,
}: {
  visible: boolean;
  onEnterClass: () => void;
  onClose: () => void;
}) => {
  return (
    <CustomBottomSheet
      visible={visible}
      isOverlay
      containerStyle={styles.container}>
      <View style={styles.background}>
        <View style={styles.characterContainer}>
          <FastImage
            source={Theme.images.charactorWaiting}
            style={styles.character}
            resizeMode="contain"
          />
        </View>

        <View style={styles.modalContent}>
          <TextApp
            preset="display_xs_medium"
            text={'You are not in any class!'}
            style={styles.title}
          />
          <TextApp
            preset="text_md_regular"
            text={'Join a class to start your mission'}
            style={styles.description}
            textColor="#535862"
          />
          <TouchableOpacity style={styles.enterButton} onPress={onEnterClass}>
            <TextApp
              preset="text_sm_semibold"
              text={'Enter a new class'}
              style={styles.enterButtonText}
              textColor="#fff"
            />
          </TouchableOpacity>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <TextApp
              preset="text_sm_semibold"
              text={'Close'}
              style={styles.closeButtonText}
              textColor="#414651"
            />
          </TouchableOpacity>
        </View>
      </View>
    </CustomBottomSheet>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    bottom: 50,
    left: 50,
    right: 50,
    borderRadius: 16,
    paddingBottom: Theme.spacing.spacing_2xl,
    zIndex: 99,
  },
  background: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  characterContainer: {
    position: 'absolute',
    top: -155,
    zIndex: 1,
  },
  character: {
    width: 200,
    height: 200,
  },
  modalContent: {
    width: '100%',
    alignItems: 'center',
    marginTop: moderateVerticalScale(40),
  },
  title: {
    marginBottom: moderateVerticalScale(12),
    lineHeight: 28,
  },
  description: {
    marginBottom: 20,
    lineHeight: 24,
  },
  enterButton: {
    height: moderateVerticalScale(44),
    backgroundColor: '#ff9800',
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 10,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  enterButtonText: {
    color: '#fff',
    lineHeight: 20,
  },
  closeButton: {
    height: moderateVerticalScale(44),
    borderRadius: 8,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D5D7DA',
  },
  closeButtonText: {
    lineHeight: 20,
  },
});

export default NotInClassModal;
