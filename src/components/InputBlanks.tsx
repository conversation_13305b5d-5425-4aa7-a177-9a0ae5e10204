import React, {useState} from 'react';
import {StyleSheet, TextInput, View} from 'react-native';
import {moderateScale, scale, verticalScale} from 'react-native-size-matters';
import {useTheme} from '../hooks/useTheme';
import {Theme} from '../themes';
import {FontFamily} from '../themes/typography';

type InputBlanksProps = {
  value: string;
  callbackInputBlank: (text: string, index: number) => void;
  disable: boolean;
  isSubmit: boolean;
  blankIndex: number;
  selectedAnswers: string[];
  answers: string[];
};

export const InputBlanks = ({
  value,
  callbackInputBlank,
  disable,
  isSubmit,
  blankIndex,
  selectedAnswers,
  answers,
}: InputBlanksProps) => {
  const theme = useTheme();
  const isCorrect =
    isSubmit &&
    selectedAnswers?.[blankIndex]?.trim()?.toLowerCase() ===
      answers?.[blankIndex]?.trim()?.toLowerCase();

  const [focusBlank, setFocusBlank] = useState(false);

  const getHighlightStyle = () => {
    if (isSubmit) {
      const color = isCorrect ? theme.bg_success_solid : theme.bg_error_solid;
      return {
        boxStyle: {
          borderColor: color,
          backgroundColor: color,
        },
        inputStyle: {
          backgroundColor: 'transparent',
          color: theme.text_primary_on_brand,
        },
      };
    }

    if (focusBlank) {
      return {
        boxStyle: {
          borderColor: theme.bg_brand_solid,
          backgroundColor: 'transparent',
        },
        inputStyle: {
          backgroundColor: 'transparent',
          color: theme.text_secondary,
        },
      };
    }

    if (value?.trim().length > 0) {
      return {
        boxStyle: {
          borderColor: theme.bg_brand_solid,
          backgroundColor: theme.bg_tertiary,
        },
        inputStyle: {
          backgroundColor: theme.bg_brand_secondary,
          color: theme.text_brand_secondary,
        },
      };
    }

    return {
      boxStyle: {
        borderColor: theme.fg_quaternary,
        backgroundColor: 'transparent',
      },
      inputStyle: {
        backgroundColor: 'transparent',
        color: theme.text_secondary,
      },
    };
  };

  const {boxStyle, inputStyle} = getHighlightStyle();

  const onChangeInputBlank = (text: string) => {
    callbackInputBlank?.(text?.trimEnd(), blankIndex);
  };

  const onFocusBlank = () => setFocusBlank(true);
  const onBlurBlank = () => setFocusBlank(false);

  return (
    <View style={[styles.boxInputBlank, boxStyle]}>
      <TextInput
        editable={disable}
        style={[styles.input, inputStyle]}
        value={value}
        onChangeText={onChangeInputBlank}
        textAlign="center"
        autoCapitalize="none"
        onFocus={onFocusBlank}
        onBlur={onBlurBlank}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  boxInputBlank: {
    width: scale(88),
    height: verticalScale(32),
    overflow: 'hidden',
    borderRadius: Theme.radius.radius_xl,
    borderWidth: 2,
    padding: moderateScale(Theme.spacing.spacing_xxs),
  },
  input: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderRadius: Theme.radius.radius_md,
    paddingHorizontal: moderateScale(Theme.spacing.spacing_xs),
    paddingVertical: 0,
    fontSize: 14,
    fontFamily: FontFamily.medium,
  },
});
