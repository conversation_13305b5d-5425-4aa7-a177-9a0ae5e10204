import React from 'react';
import {
  Image,
  ImageProps,
  StyleSheet,
  Switch,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {Icons} from '../themes/icons.ts';
import TextApp from './TextApp';

type Props = {
  label: string;
  icon?: ImageProps;
  onPress?: () => void;
  isSwitch?: boolean;
  value?: boolean;
  onValueChange?: (val: boolean) => void;
  style?: ViewStyle;
};

const SettingsItem: React.FC<Props> = ({
  label,
  icon,
  onPress,
  isSwitch = false,
  value = false,
  onValueChange,
  style,
}) => {
  return (
    <TouchableOpacity
      style={[styles.item, style]}
      onPress={onPress}
      activeOpacity={isSwitch ? 1 : 0.6}>
      <View style={styles.labelContainer}>
        {icon && <Image source={icon} style={styles.icon} />}
        <TextApp text={label} preset={'text_md_medium'} textColor={'#181D27'} />
      </View>
      {isSwitch ? (
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{false: '#ccc', true: '#FF8612'}}
          thumbColor={value ? '#ffffff' : '#ffffff'}
          ios_backgroundColor="#ccc" // cần thiết cho iOS
          style={styles.switch}
        />
      ) : (
        <Image source={Icons.settings.rightArrow} style={styles.icon} />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  item: {
    paddingVertical: 12,
    paddingHorizontal: 16,

    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    fontSize: 16,
  },
  icon: {
    marginRight: 8,
    width: 24,
    height: 24,
  },
  switch: {
    transform: [{scaleX: 1}, {scaleY: 1}],
  },
});

export default SettingsItem;
