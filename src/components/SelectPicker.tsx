import React, {useState} from 'react';
import {
  LayoutChangeEvent,
  Modal,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {scale, verticalScale} from 'react-native-size-matters';
import {SvgIcons} from '../../assets/svg';
import {useTheme} from '../hooks/useTheme';
import {Theme} from '../themes';
import {HIT_SLOP, isAndroid} from '../utils/Scale';
import TextApp from './TextApp';

interface SelectPickerProps {
  selectedValue: string;
  disabled: boolean;
  isSubmit: boolean;
  selectedAnswers: string[];
  blankIndex: number;
  options: {answer: string}[];
  callbackSelectOption: (index: number, value: string) => void;
}

type pickerType = {
  visible: boolean;
  option: any | null;
};

type positionType = {
  top: number;
  left: number;
};

const initPicker = {
  visible: false,
  option: null,
};

const initPosition = {
  top: 0,
  left: 0,
};

export const SelectPicker = ({
  selectedValue,
  disabled,
  isSubmit,
  selectedAnswers,
  blankIndex,
  options,
  callbackSelectOption,
}: SelectPickerProps) => {
  const theme = useTheme();
  const selected = selectedAnswers[blankIndex];
  const correct = options[blankIndex]?.answer;
  const isCorrect = selected === correct;

  const [pickerState, setPickerState] = useState<pickerType>(initPicker);
  const [position, setPosition] = useState<positionType>(initPosition);
  const [pressedIndex, setPressedIndex] = useState<number | null>(null);

  const getHighlightStyle = () => {
    if (pickerState.visible) return {borderColor: theme.bg_brand_solid};
    if (isSubmit) {
      const color = isCorrect ? theme.bg_success_solid : theme.bg_error_solid;
      return {backgroundColor: color, borderColor: color};
    }
    return {borderColor: theme.fg_quaternary};
  };

  const highlightStyle = getHighlightStyle();
  const textColor = isSubmit
    ? theme.text_primary_on_brand
    : theme.text_secondary;

  const handleOpen = () => {
    setPickerState({visible: true, option: options[blankIndex]});
  };

  const handleSelectOptions = (item: string) => {
    setPickerState({visible: false, option: null});
    callbackSelectOption?.(pickerState?.option?.index, item);
  };

  const handleClose = () => {
    setPickerState({visible: false, option: null});
  };

  const onLayoutButton = (event: LayoutChangeEvent) => {
    const target = event?.target;
    if (target && typeof target.measureInWindow === 'function') {
      target.measureInWindow((x, y, width, height) => {
        setPosition({
          top: y + height + verticalScale(3),
          left: x,
        });
      });
    }
  };

  return (
    <View onLayout={onLayoutButton} collapsable={false}>
      <View style={[styles.btnSelect, highlightStyle]}>
        <Pressable
          disabled={disabled}
          onPress={handleOpen}
          hitSlop={HIT_SLOP}
          style={[
            styles.pressableBase,
            pickerState.visible && {backgroundColor: theme.bg_brand_secondary},
            !isSubmit && styles.pdRight,
          ]}>
          <TextApp
            preset="text_sm_medium"
            text={selectedValue}
            textColor={textColor}
          />
          {!isSubmit && (
            <View style={styles.dropIcon}>
              <SvgIcons.ArrowDown />
            </View>
          )}
        </Pressable>
      </View>

      <Modal
        transparent
        visible={pickerState?.visible}
        animationType="fade"
        onRequestClose={handleClose}>
        <Pressable style={styles.modalOverlay} onPress={handleClose}>
          <View
            style={[
              styles.shadowWrapper,
              {
                top: position?.top,
                left: position?.left,
              },
            ]}>
            <View
              style={[styles.dropdown, {backgroundColor: theme.bg_primary}]}>
              {pickerState?.option?.options?.map(
                (item: string, index: number) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => handleSelectOptions(item)}
                    onPressIn={() => setPressedIndex(index)}
                    onPressOut={() => setPressedIndex(null)}
                    style={[
                      styles.optionItem,
                      {
                        borderColor:
                          pressedIndex === index
                            ? theme.border_brand
                            : theme.border_tertiary,
                        backgroundColor:
                          pressedIndex === index
                            ? theme.bg_brand_secondary
                            : theme.bg_primary,
                      },
                    ]}>
                    <TextApp
                      text={item}
                      preset="text_sm_medium"
                      textColor={theme.text_secondary}
                    />
                  </TouchableOpacity>
                ),
              )}
            </View>
          </View>
        </Pressable>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  btnSelect: {
    width: scale(88),
    height: verticalScale(32),
    overflow: 'hidden',
    borderRadius: Theme.radius.radius_xl,
    borderWidth: 2,
    padding: Theme.spacing.spacing_xxs,
  },
  pdRight: {
    paddingRight: scale(Theme.spacing.spacing_xl),
  },
  modalOverlay: {
    flex: 1,
  },
  shadowWrapper: {
    position: 'absolute',
    shadowColor: '#0A0D121A',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 1,
    shadowRadius: 3,
    elevation: 5,
    borderRadius: Theme.radius.radius_md,
  },

  dropdown: {
    width: scale(88),
    borderRadius: Theme.radius.radius_md,
    overflow: 'hidden',
  },
  optionItem: {
    paddingHorizontal: Theme.spacing.spacing_lg,
    paddingVertical: Theme.spacing.spacing_md,
    borderBottomWidth: 1,
  },
  dropIcon: {
    position: 'absolute',
    right: scale(Theme.spacing.spacing_sm),
  },
  pressableBase: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderRadius: Theme.radius.radius_md,
  },
});
