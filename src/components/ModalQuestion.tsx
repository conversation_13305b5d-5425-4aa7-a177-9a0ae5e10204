import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {moderateVerticalScale, scale} from 'react-native-size-matters';
import { SvgIcons } from '../../assets/svg/index.tsx';
import {useTheme} from '../hooks/useTheme.ts';
import {HIT_SLOP, SCREEN_WIDTH} from '../utils/Scale.ts';
import {CustomBottomSheet} from './BottomSheet.tsx';
import Button from './Button.tsx';
import TextApp from './TextApp/index.tsx';
import UniversalResultAnswer from './UniversalResultAnswer.tsx';
import {EnhancedDataFinish, ExerciseType} from '../types/answer.types';
import useQuestion from '../hooks/auth/useQuestion.ts';

interface BottomModalProps {
  isVisible: boolean;
  answer: EnhancedDataFinish | undefined;
}

export const ModalQuestion: React.FC<BottomModalProps> = ({
  isVisible,
  answer,
}) => {
  const theme = useTheme();
  const {handleCloseAnswer} = useQuestion();

  if (!isVisible) return null;

  const safeAnswers = (answers: any) => {
    if (!answers) return [];
    if (Array.isArray(answers)) return answers.filter(a => a !== undefined);
    return [answers].filter(a => a !== undefined);
  };

  const safeStudentAnswers = (studentAnswers: any) => {
    if (!studentAnswers) return [];
    if (Array.isArray(studentAnswers)) return studentAnswers;
    return [studentAnswers];
  };

  const safeQuestion = (question: any) => {
    if (!question) return undefined;
    if (Array.isArray(question)) return question.join(' ');
    return String(question);
  };

  const renderAnswerByExcercise = () => {
    const {excerciseType, answerByStudent, answers, question} = answer ?? {};

    switch (excerciseType) {
      case ExerciseType.DROPDOWN:
      case ExerciseType.FILL_IN_THE_BLANK:
        return (
          <>
            <UniversalResultAnswer
              question={safeQuestion(question)}
              answers={safeAnswers(answers)}
              studentAnswers={safeStudentAnswers(answerByStudent)}
              excerciseType={excerciseType}
            />
          </>
        );

      case ExerciseType.ARRANGEMENT:
        return (
          <>
            <UniversalResultAnswer
              question={safeQuestion(question)}
              answers={safeAnswers(answers)}
              studentAnswers={safeStudentAnswers(answerByStudent)}
              excerciseType={excerciseType}
              isArrangement={true}
            />
          </>
        );

      case ExerciseType.DRAG_AND_DROP:
        return (
          <>
            <UniversalResultAnswer
              question={safeQuestion(question)}
              answers={safeAnswers(answers)}
              studentAnswers={safeStudentAnswers(answerByStudent)}
              excerciseType={excerciseType}
            />
          </>
        );

      case ExerciseType.MULTIPLE_CHOICE:
        return (
          <>
            <TextApp
              preset="text_md_medium"
              text={answers || ''}
              textColor={theme.text_success_primary}
              style={{
                textDecorationLine: 'underline',
                marginTop: 5,
                lineHeight: 24,
              }}
            />
          </>
        );
      case ExerciseType.ARRANGEMENT_DIALOGUE:
        return (
          <>
            <UniversalResultAnswer
              answers={safeAnswers(answers)}
              studentAnswers={safeStudentAnswers(answerByStudent)}
              excerciseType={excerciseType}
              isArrangement={true}
            />
          </>
        );

      default:
        return <></>;
    }
  };

  return (
    <CustomBottomSheet
      visible={isVisible}
      onClose={handleCloseAnswer}
      containerStyle={{
        width: SCREEN_WIDTH,
        backgroundColor: theme.bg_brand_primary,
      }}>
      <View style={{marginTop: moderateVerticalScale(8)}}>
        <View style={styles.rowCenter}>
          <View style={styles.flex1}>
            <View style={styles.rowSpaceBetween}>
              <TextApp
                preset="text_lg_semibold"
                text={'Correct answer'}
                textColor={theme.text_success_primary}
              />
              <TouchableOpacity hitSlop={HIT_SLOP}>
                <SvgIcons.IconBookmark />
              </TouchableOpacity>
            </View>

            <View
              style={{
                marginBottom: moderateVerticalScale(3),
                flex: 1,
              }}>
              {renderAnswerByExcercise()}
            </View>
          </View>
        </View>

        <Button
          title={'Close'}
          onPress={handleCloseAnswer}
          style={{
            height: moderateVerticalScale(40),
            backgroundColor: theme.bg_success_solid,
          }}
          textColor={theme.text_white}
          textStyle={{lineHeight: 20}}
        />
      </View>
    </CustomBottomSheet>
  );
};

const styles = StyleSheet.create({
  rowCenter: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: moderateVerticalScale(8),
  },
  flex1: {
    flex: 1,
  },
  rowSpaceBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: moderateVerticalScale(16),
  },
});

export default ModalQuestion;
