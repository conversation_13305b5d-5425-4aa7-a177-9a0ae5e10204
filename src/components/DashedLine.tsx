import React from 'react';
import Svg, {Line} from 'react-native-svg';
import {Theme} from '../themes';
import {useTheme} from '../hooks/useTheme';
export const DashedLine = () => {
  const theme = useTheme();
  return (
    <Svg height="2" width="100%">
      <Line
        x1="0"
        y1="1"
        x2="100%"
        y2="1"
        stroke={theme.fg_quaternary}
        strokeWidth="2"
        strokeDasharray="6, 6"
        strokeLinecap="round"
      />
    </Svg>
  );
};
