import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {ms, scale} from 'react-native-size-matters';
import {SvgIcons} from '../../assets/svg';
import {useTheme} from '../hooks/useTheme';
import {Theme} from '../themes';
import {FontFamily} from '../themes/typography';
import {HIT_SLOP} from '../utils/Scale';

interface CheckboxProps {
  checked: boolean;
  onPress: () => void;
  title: string;
  size?: number;
  textStyle?: object;
}

const Checkbox: React.FC<CheckboxProps> = ({
  checked,
  onPress,
  title,
  size = 16,
  textStyle,
}) => {
  const theme = useTheme();

  const renderTitle = () => {
    if (typeof title === 'string' && title.includes('<link>')) {
      const [before, linkAndAfter] = title.split('<link>');
      const [linkText, after] = linkAndAfter.split('</link>');

      return (
        <Text style={[styles.text, textStyle, {color: theme.text_secondary}]}>
          {before}
          <Text style={{color: theme.text_infor_primary}}>{linkText}</Text>
          {after}
        </Text>
      );
    }

    return (
      <Text style={[styles.text, textStyle, {color: theme.text_secondary}]}>
        {title}
      </Text>
    );
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.checkbox,
          {
            width: scale(size),
            height: scale(size),
            backgroundColor: checked ? theme.bg_brand_solid : theme.bg_primary,
            borderColor: checked ? theme.bg_brand_solid : theme.border_primary,
          },
        ]}
        onPress={onPress}
        activeOpacity={1}
        hitSlop={HIT_SLOP}>
        {checked && <SvgIcons.Check stroke={'#fff'} width={15} height={15} />}
      </TouchableOpacity>
      <View style={{flex: 1}}>{renderTitle()}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    borderWidth: 1,
    borderRadius: Theme.spacing.spacing_xs,
    alignItems: 'center',
    justifyContent: 'center',
  },

  text: {
    marginLeft: scale(10),
    lineHeight: ms(18),
    fontSize: ms(14),
    fontFamily: FontFamily.medium,
  },
});

export default Checkbox;
