import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {useDispatch} from 'react-redux';

import {useTypedSelector} from '../redux/store.ts';
import {hideAlert} from '../redux/reducer/PopUpSlice.ts';
import Modal from 'react-native-modal';
import {Theme} from '../themes';

const AlertModal: React.FC = () => {
  const dispatch = useDispatch();
  const {alertMessage, showAlert} = useTypedSelector(state => state.popUp);
  const closeModal = () => dispatch(hideAlert());
  if (!alertMessage) return null;

  return (
    <Modal
      backdropColor={'transparent'}
      isVisible={showAlert}
      onBackdropPress={closeModal}>
      <View style={styles.overlay}>
        <View style={styles.container}>
          <Text style={styles.message}>{alertMessage}</Text>
          <TouchableOpacity style={styles.button} onPress={closeModal}>
            <Text style={styles.buttonText}>OK</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,

    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: 300,
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Theme.colors.border,
  },
  message: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#007BFF',
    padding: 10,
    borderRadius: 5,
    width: '100%',
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default AlertModal;
