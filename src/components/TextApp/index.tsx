import React from 'react';
import {Text} from 'react-native';
import {textPresets} from './preset';
import {TextProperties} from './type';

const TextApp = ({
  style: styleOverride = {},
  text,
  preset = 'text_sm_regular',
  onPress,
  textColor,
  ...props
}: TextProperties) => {
  let newStyle;
  if (Array.isArray(styleOverride)) {
    newStyle = [textPresets[preset], ...styleOverride, {color: '#181D27'}];
  } else {
    newStyle = [textPresets[preset], styleOverride, {color: '#181D27'}];
  }

  return (
    <Text
      {...props}
      style={[newStyle, {color: textColor}]}
      onPress={onPress}
      allowFontScaling={false}>
      {text}
    </Text>
  );
};

export default TextApp;
