import {StyleSheet} from 'react-native';
import {FontFamily} from '../../themes/typography';
import {ms} from 'react-native-size-matters';

export const textPresets = StyleSheet.create({
  text_s_regular: {
    fontSize: ms(10),
    fontFamily: FontFamily.regular,
  },
  text_xs_regular: {
    fontSize: ms(12),
    fontFamily: FontFamily.regular,
  },
  text_xs_medium: {
    fontSize: ms(12),
    fontFamily: FontFamily.medium,
  },
  text_xs_semibold: {
    fontSize: ms(12),
    fontFamily: FontFamily.semibold,
  },
  text_xs_bold: {
    fontSize: ms(12),
    fontFamily: FontFamily.bold,
  },
  text_sm_regular: {
    fontSize: ms(14),
    fontFamily: FontFamily.regular,
  },
  text_sm_medium: {
    fontSize: ms(14),
    fontFamily: FontFamily.medium,
  },
  text_sm_semibold: {
    fontSize: ms(14),
    fontFamily: FontFamily.semibold,
  },
  text_sm_bold: {
    fontSize: ms(14),
    fontFamily: FontFamily.bold,
  },
  text_md_regular: {
    fontSize: ms(16),
    fontFamily: FontFamily.regular,
  },
  text_md_medium: {
    fontSize: ms(16),
    fontFamily: FontFamily.medium,
  },
  text_md_semibold: {
    fontSize: ms(16),
    fontFamily: FontFamily.semibold,
  },
  text_md_bold: {
    fontSize: ms(16),
    fontFamily: FontFamily.bold,
  },
  text_lg_regular: {
    fontSize: ms(18),
    fontFamily: FontFamily.regular,
  },
  text_lg_medium: {
    fontSize: ms(18),
    fontFamily: FontFamily.medium,
  },
  text_lg_semibold: {
    fontSize: ms(18),
    fontFamily: FontFamily.semibold,
  },
  text_lg_bold: {
    fontSize: ms(18),
    fontFamily: FontFamily.bold,
  },
  text_xl_regular: {
    fontSize: ms(20),
    fontFamily: FontFamily.regular,
  },
  text_xl_medium: {
    fontSize: ms(20),
    fontFamily: FontFamily.medium,
  },
  text_xl_semibold: {
    fontSize: ms(20),
    fontFamily: FontFamily.semibold,
  },
  text_xl_bold: {
    fontSize: ms(20),
    fontFamily: FontFamily.bold,
  },
  display_xs_regular: {
    fontSize: ms(24),
    fontFamily: FontFamily.regular,
  },
  display_xs_medium: {
    fontSize: ms(24),
    fontFamily: FontFamily.medium,
  },
  display_xs_semibold: {
    fontSize: ms(24),
    fontFamily: FontFamily.semibold,
  },
  display_xs_bold: {
    fontSize: ms(24),
    fontFamily: FontFamily.bold,
  },
  display_sm_regular: {
    fontSize: ms(30),
    fontFamily: FontFamily.regular,
  },
  display_sm_medium: {
    fontSize: ms(30),
    fontFamily: FontFamily.medium,
  },
  display_sm_semibold: {
    fontSize: ms(30),
    fontFamily: FontFamily.semibold,
  },
  display_sm_bold: {
    fontSize: ms(30),
    fontFamily: FontFamily.bold,
  },
  display_md_regular: {
    fontSize: ms(36),
    fontFamily: FontFamily.regular,
  },
  display_md_medium: {
    fontSize: ms(36),
    fontFamily: FontFamily.medium,
  },
  display_md_semibold: {
    fontSize: ms(36),
    fontFamily: FontFamily.semibold,
  },
  display_md_bold: {
    fontSize: ms(36),
    fontFamily: FontFamily.bold,
  },
  display_lg_regular: {
    fontSize: ms(48),
    fontFamily: FontFamily.regular,
  },
  display_lg_medium: {
    fontSize: ms(48),
    fontFamily: FontFamily.medium,
  },
  display_lg_semibold: {
    fontSize: ms(48),
    fontFamily: FontFamily.semibold,
  },
  display_lg_bold: {
    fontSize: ms(48),
    fontFamily: FontFamily.bold,
  },
  display_xl_regular: {
    fontSize: ms(60),
    fontFamily: FontFamily.regular,
  },
  display_xl_medium: {
    fontSize: ms(60),
    fontFamily: FontFamily.medium,
  },
  display_xl_semibold: {
    fontSize: ms(60),
    fontFamily: FontFamily.semibold,
  },
  display_xl_bold: {
    fontSize: ms(60),
    fontFamily: FontFamily.bold,
  },
  display_xxl_regular: {
    fontSize: ms(72),
    fontFamily: FontFamily.regular,
  },
  display_xxl_medium: {
    fontSize: ms(72),
    fontFamily: FontFamily.medium,
  },
  display_xxl_semibold: {
    fontSize: ms(72),
    fontFamily: FontFamily.semibold,
  },
  display_xxl_bold: {
    fontSize: ms(72),
    fontFamily: FontFamily.bold,
  },
});

export type TextPresetNames = keyof typeof textPresets;
