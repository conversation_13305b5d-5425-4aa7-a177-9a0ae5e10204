import React from 'react';
import {Image, StyleSheet, TouchableOpacity} from 'react-native';
import {scale} from 'react-native-size-matters';
import TextApp from './TextApp';

type ImageButtonProps = {
  onPress: () => void;
  source: any;
  widthVal: number | string;
  title?: string;
  disabled?: boolean;
  contentColor?: string;
};

const ImageButton: React.FC<ImageButtonProps> = ({
  onPress,
  source,
  widthVal,
  title,
  disabled = false,
  contentColor,
}) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled}
      style={[styles.container, {width: scale(widthVal), height: scale(48)}]}>
      <Image
        source={source}
        resizeMode="contain"
        style={[styles.image, {width: scale(widthVal)}]}
      />
      {title && (
        <TextApp
          text={title}
          preset={'text_md_semibold'}
          style={styles.title}
          textColor={contentColor ?? '#FFF4C1'}
        />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',

    position: 'relative',
  },
  image: {
    ...StyleSheet.absoluteFillObject,
    width: '100%',
    height: '100%',
  },
  title: {
    textAlign: 'center',
  },
});

export default ImageButton;
