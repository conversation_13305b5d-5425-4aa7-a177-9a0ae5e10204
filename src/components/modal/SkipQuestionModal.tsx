import React, {useEffect, useRef} from 'react';
import {
  Animated,
  Image,
  Pressable,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {Theme} from '../../themes';
import {scale} from 'react-native-size-matters';
import {useTheme} from '../../hooks/useTheme.ts';
import TextApp from '../TextApp';
import {SCREEN_WIDTH} from '../../utils/Scale.ts';

interface PopupModalProps {
  isVisible: boolean;
  onClose: () => void;
  onConfirm: () => void;
  doNotShowAgain?: boolean;
  onToggleDoNotShowAgain?: (value: boolean) => void;
  title: string;
  content: string;
  btnText: string;
}

const SkipQuestionModal: React.FC<PopupModalProps> = ({
  isVisible,
  onClose,
  onConfirm,
  title,
  content,
  btnText,
}) => {
  const theme = useTheme();
  const scaleAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isVisible) {
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(scaleAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  }, [isVisible]);

  if (!isVisible) {return null;}

  return (
    <View style={styles.overlay}>
      <Pressable style={styles.backdrop} onPress={() => {}} />

      <Animated.View
        style={[
          styles.centered,
          {
            transform: [
              {
                scale: scaleAnim,
              },
            ],
          },
        ]}>
        <View style={styles.characterWrapper}>
          <Image
            source={Theme.images.characterSkip}
            style={styles.character}
            resizeMode="contain"
          />
        </View>

        <View style={styles.modalContent}>
          {/*<TouchableOpacity onPress={onClose} style={styles.closeBtn}>*/}
          {/*  <Text style={{fontSize: 20, color: theme.fg_quaternary}}>✕</Text>*/}
          {/*</TouchableOpacity>*/}

          <TextApp text={title} preset={'display_xs_medium'} />
          <TextApp
            text={content}
            preset={'text_md_regular'}
            textColor={theme.text_tertiary}
            style={{textAlign: 'center', paddingVertical: 12}}
          />

          <TouchableOpacity
            style={[styles.button, {backgroundColor: theme.bg_brand_solid}]}
            onPress={onConfirm}>
            <Text style={styles.buttonText}>{btnText}</Text>
          </TouchableOpacity>

          {/*<View style={{width: '100%'}}>*/}
          {/*  <TouchableOpacity*/}
          {/*    style={styles.checkboxRow}*/}
          {/*    onPress={() => onToggleDoNotShowAgain?.(!doNotShowAgain)}*/}
          {/*    activeOpacity={0.8}>*/}
          {/*    <View*/}
          {/*      style={[styles.checkbox, doNotShowAgain && styles.checkedBox]}>*/}
          {/*      {doNotShowAgain && <Text style={styles.checkmark}>✓</Text>}*/}
          {/*    </View>*/}
          {/*    <Text style={styles.checkboxLabel}>Không hiển thị lần sau</Text>*/}
          {/*  </TouchableOpacity>*/}
          {/*</View>*/}
        </View>
      </Animated.View>
    </View>
  );
};

export default SkipQuestionModal;

const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
    alignItems: 'center',
    zIndex: 1000,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
  },
  centered: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  characterWrapper: {
    zIndex: 2,
    position: 'absolute',
    top: -190,
  },
  character: {
    width: scale(215),
    height: scale(200),
  },
  modalContent: {
    width: SCREEN_WIDTH * 0.85,
    backgroundColor: 'white',
    borderRadius: 16,
    paddingTop: 60,
    paddingHorizontal: 30,
    paddingBottom: 24,
    marginBottom: 24,
    alignItems: 'center',
  },
  closeBtn: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 3,
  },
  button: {
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    width: '100%',
    marginBottom: 16,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 1.5,
    borderColor: '#999',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  checkedBox: {
    backgroundColor: '#FF6A00',
    borderColor: '#FF6A00',
  },
  checkmark: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  checkboxLabel: {
    fontSize: 14,
    color: '#414651',
    fontWeight: '500',
  },
});
