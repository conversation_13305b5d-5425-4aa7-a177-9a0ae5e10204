import React from 'react';
import {StyleSheet, View} from 'react-native';
import TextApp from './TextApp';
import {Theme} from '../themes';

type Props = {
  title: string;
  children: React.ReactNode;
};

const SettingsSection: React.FC<Props> = ({title, children}) => (
  <View style={styles.section}>
    <TextApp
      text={title}
      style={styles.title}
      preset={'text_lg_semibold'}
      textColor={'#395500'}
    />
    <View style={styles.content}>{children}</View>
  </View>
);

const styles = StyleSheet.create({
  section: {
    marginBottom: 6,
    paddingHorizontal: 40,
  },
  title: {
    textAlign: 'center',
    marginBottom: 6,
  },
  content: {
    backgroundColor: '#F6E5AF',
    borderRadius: Theme.radius.radius_lg,
    overflow: 'hidden',
  },
});

export default SettingsSection;
