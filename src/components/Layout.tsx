import React from 'react';
import { StyleSheet, View } from 'react-native';
import { initTop, isIOS } from '../utils/Scale.ts';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({children}) => {
  return (
    <View style={styles.container}>
      {/*<StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />*/}
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: isIOS ? initTop : 10,
    // backgroundColor: '',
  },
});

export default Layout;
