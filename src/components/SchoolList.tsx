import React, {createRef, useMemo, useState} from 'react';
import {
  Animated,
  LayoutRectangle,
  Modal,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  moderateScale,
  moderateVerticalScale,
  scale,
} from 'react-native-size-matters';
import {SvgIcons} from '../../assets/svg';
import {useTheme} from '../hooks/useTheme';
import {Theme} from '../themes';
import {HIT_SLOP} from '../utils/Scale';
import TextApp from './TextApp';

type Props = {
  options: any[];
  callbackSelectOption: (item: any) => void;
  callBackCreateNewSchool: () => void;
  callBackChangeModal: () => void;
};

export const SchoolList = ({
  options,
  callbackSelectOption,
  callBackCreateNewSchool,
  callBackChangeModal,
}: Props) => {
  const buttonRef = createRef<View>();
  const [isModalOpen, setModalOpen] = useState<boolean>(false);
  const [pressedIndex, setPressedIndex] = useState<number | null>(0);
  const [componentPosition, setComponentPosition] =
    useState<LayoutRectangle | null>(null);
  const opacity = useMemo(() => new Animated.Value(0), []);
  const theme = useTheme();

  const calculatePosition = () => {
    if (buttonRef.current) {
      buttonRef.current.measureInWindow((x, y, width, height) => {
        setComponentPosition({x, y, width, height});
      });
    }
  };

  const openModal = () => {
    calculatePosition();
    Animated.timing(opacity, {
      toValue: 1,
      duration: 100,
      useNativeDriver: true,
    }).start(() => setModalOpen(true));
  };

  const hideModal = () => {
    Animated.timing(opacity, {
      toValue: 0,
      duration: 100,
      useNativeDriver: true,
    }).start(() => setModalOpen(false));
  };

  const handleClick = () => {
    const method = isModalOpen ? hideModal : openModal;
    method();
    callBackChangeModal?.();
  };

  const handleSelectOptions = (item: any, index: number) => {
    setPressedIndex(index);
    callbackSelectOption?.(item);
    setModalOpen(false);
  };

  const createNewSchool = () => {
    setModalOpen(false);
    callBackCreateNewSchool?.();
  };

  return (
    <View ref={buttonRef} collapsable={false}>
      <TouchableOpacity
        style={styles.container}
        hitSlop={HIT_SLOP}
        onPress={handleClick}
        onLayout={calculatePosition}>
        <SvgIcons.List />
        <TextApp
          preset="text_sm_semibold"
          text={'School List'}
          style={styles.textAppMargin}
          textColor={theme.text_secondary}
        />
      </TouchableOpacity>

      <Modal visible={isModalOpen} transparent>
        <TouchableOpacity
          activeOpacity={1}
          style={{flex: 1}}
          onPress={hideModal}>
          <View style={styles.overlay} />
          {componentPosition && (
            <Animated.View
              style={[
                styles.tooltipContainer,
                {
                  top: componentPosition.y + componentPosition.height + 6,
                  left: Math.max(componentPosition.x - scale(90), 0),
                  opacity,
                },
              ]}>
              <View style={{marginBottom: scale(8)}}>
                {options.map((item: any, index: number) => {
                  return (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.optionButton,
                        pressedIndex === index && styles.optionButtonSelected,
                      ]}
                      onPress={handleSelectOptions.bind(null, item, index)}>
                      <TextApp
                        text={item?.schoolName}
                        style={styles.textAppStyle}
                        textColor="#414651"
                        numberOfLines={1}
                      />
                    </TouchableOpacity>
                  );
                })}
              </View>
              <TouchableOpacity
                style={styles.newSchoolButton}
                onPress={createNewSchool}>
                <SvgIcons.Plus />
                <TextApp
                  preset="text_sm_semibold"
                  text={'Enter a new school'}
                  style={styles.textAppStyle}
                  textColor="#fff"
                />
              </TouchableOpacity>
            </Animated.View>
          )}
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject,
  },
  tooltipMargin: {
    borderBottomWidth: 1,
    borderBottomColor: '#E1E1E1',
  },
  component: {
    position: 'absolute',
  },
  tooltipContainer: {
    width: scale(200),
    position: 'absolute',
    borderRadius: Theme.radius.radius_md,
    backgroundColor: '#fff',
    padding: scale(8),
  },
  optionButton: {
    padding: 8,
    borderRadius: 8,
  },
  textAppStyle: {
    lineHeight: 20,
  },
  container: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#D5D7DA',
    borderRadius: Theme.radius.radius_md,
    paddingHorizontal: scale(12),
    height: moderateScale(34),
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  textAppMargin: {
    lineHeight: 18,
    marginLeft: scale(5),
  },
  newSchoolButton: {
    height: moderateVerticalScale(36),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FF8612',
    borderRadius: 8,
    flexDirection: 'row',
  },
  optionButtonSelected: {
    backgroundColor: '#FCF2E8',
  },
});
