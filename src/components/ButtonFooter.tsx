import React, {useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import TextApp from '../components/TextApp';
import {moderateScale, moderateVerticalScale} from 'react-native-size-matters';
import {Theme} from '../themes';
import {useTheme} from '../hooks/useTheme.ts';
import {isAndroid} from '../utils/Scale.ts';

type Props = {
  btnCheck: () => void;
  disabled?: boolean;
  title: string;
};

const ButtonFooter = ({btnCheck, disabled, title}: Props) => {
  const [isOnPress, setIsOnPress] = useState<boolean>(false);
  const theme = useTheme();
  return (
    <View style={[styles.container, {backgroundColor: theme.bg_primary}]}>
      <TouchableOpacity
        disabled={disabled}
        style={[
          styles.button,
          {
            backgroundColor: disabled
              ? theme.bg_disabled
              : isOnPress
                ? theme.bg_brand_solid_hover
                : theme.bg_brand_solid,
          },
        ]}
        onPressIn={() => setIsOnPress(true)}
        onPressOut={() => setIsOnPress(false)}
        onPress={btnCheck}>
        <TextApp
          preset="text_md_semibold"
          text={title}
          textColor={disabled ? theme.text_disabled : theme.text_white}
          style={styles.label}
        />
      </TouchableOpacity>
    </View>
  );
};

export default ButtonFooter;

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    alignSelf: 'center',
    width: '100%',
    paddingHorizontal: moderateScale(32),
    paddingBottom: isAndroid
      ? moderateVerticalScale(16)
      : moderateVerticalScale(58),
    paddingTop: moderateVerticalScale(16),
    zIndex: 99999,
  },
  button: {
    backgroundColor: '#FF8612',
    height: 48,
    borderRadius: Theme.radius.radius_md,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Theme.colors.border,
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  label: {
    textAlign: 'center',
    fontWeight: '600',
  },
});
