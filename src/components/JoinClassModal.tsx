import React, {useState} from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {moderateVerticalScale, ms, scale} from 'react-native-size-matters';
import IconClosed from '../../assets/svgIcons/IconClosed';
import {useTheme} from '../hooks/useTheme';
import {FontFamily} from '../themes/typography';
import {CustomBottomSheet} from './BottomSheet';
import TextApp from './TextApp';
import {KeyboardAwareScrollView} from 'react-native-keyboard-controller';
import {SCREEN_WIDTH} from '../utils/Scale';
import {useReduxDispatch} from '../redux/store';
import {
  fetchJoinClassByCode,
  fetchSchoolByStudent,
} from '../redux/reducer/fetchData';
import Toast from 'react-native-toast-message';
import Button from './Button';

type Props = {
  visibleJoinClass: boolean;
  closeJoinClass: () => void;
  callbackWhenJoinClassSuccess: (classId: string) => void;
};

const JoinClassModal = ({
  visibleJoinClass,
  closeJoinClass,
  callbackWhenJoinClassSuccess,
}: Props) => {
  const theme = useTheme();
  const [loading, setLoading] = useState<boolean>(false);
  const [classCode, setClassCode] = useState<string>('');
  const dispatch = useReduxDispatch();

  const handleJoinClass = async () => {
    setLoading(true);

    const result = await dispatch(fetchJoinClassByCode({code: classCode}));

    if (fetchJoinClassByCode.fulfilled.match(result)) {
      const {classId} = result?.payload?.data || {};
      callbackWhenJoinClassSuccess?.(classId);
      dispatch(fetchSchoolByStudent());
    }

    setClassCode('');
    setLoading(false);
  };

  const isDisabled = !classCode?.trim();

  return (
    <CustomBottomSheet
      visible={visibleJoinClass}
      isOverlay
      containerStyle={styles.container}>
      <KeyboardAwareScrollView
        testID="aware_scroll_view_container"
        showsVerticalScrollIndicator={false}>
        <View style={styles.modalContent}>
          <TouchableOpacity
            style={styles.close}
            onPress={closeJoinClass}
            disabled={loading}>
            <IconClosed />
          </TouchableOpacity>
          <TextApp
            preset="text_md_regular"
            text={'Ask your teacher for a class code'}
            style={styles.title}
            textColor={theme.text_tertiary}
          />
          <View style={styles.boxInput}>
            <TextInput
              placeholder="Class Code"
              defaultValue={classCode}
              onChangeText={value => setClassCode(value)}
              style={styles.input}
              editable={!loading}
            />
          </View>
          <Button
            title={'Join Class'}
            onPress={handleJoinClass}
            disabled={isDisabled}
            style={[
              styles.enterButton,
              {
                backgroundColor: isDisabled
                  ? theme.bg_disabled
                  : theme.bg_brand_solid,
                borderColor: isDisabled
                  ? theme.border_disabled_subtle
                  : theme.bg_brand_solid,
              },
            ]}
            textColor={isDisabled ? theme.fg_disabled : theme.text_white}
            loading={loading}
          />
        </View>
      </KeyboardAwareScrollView>
    </CustomBottomSheet>
  );
};

const styles = StyleSheet.create({
  container: {
    width: SCREEN_WIDTH - 32,
    backgroundColor: 'transparent',
    alignSelf: 'center',
    zIndex: 99,
  },

  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: scale(12),
  },
  title: {
    marginBottom: moderateVerticalScale(24),
    lineHeight: 24,
    textAlign: 'center',
  },

  enterButton: {
    height: moderateVerticalScale(44),
    borderWidth: 1,
  },
  enterButtonText: {
    lineHeight: 18,
  },
  boxInput: {
    width: '100%',
    marginBottom: moderateVerticalScale(12),
    paddingVertical: moderateVerticalScale(10),
    paddingLeft: scale(12),
    paddingRight: scale(31),
    borderWidth: 1,
    borderColor: '#D5D7DA',
    borderRadius: 8,
  },
  input: {
    paddingVertical: 0,
    fontSize: ms(16),
    fontFamily: FontFamily.regular,
  },
  close: {
    alignSelf: 'flex-end',
    marginBottom: moderateVerticalScale(24),
  },
});

export default JoinClassModal;
