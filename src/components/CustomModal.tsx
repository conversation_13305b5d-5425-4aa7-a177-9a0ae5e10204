import React, {memo, useCallback, useEffect, useMemo} from 'react';
import {
  StyleSheet,
  View,
  ViewStyle,
  TouchableWithoutFeedback,
  Dimensions,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  FadeIn,
  FadeOut,
} from 'react-native-reanimated';
import {SCREEN_HEIGHT} from '../utils/Scale';

interface CustomModalProps {
  visible: boolean;
  onClose?: () => void;
  children?: React.ReactNode;
  modalStyle?: ViewStyle;
  containerStyle?: ViewStyle;
  backdropOpacity?: number;
}

const CustomModal: React.FC<CustomModalProps> = memo(
  ({visible, onClose, children, modalStyle, containerStyle}) => {
    const translateY = useSharedValue(-SCREEN_HEIGHT);

    useEffect(() => {
      if (visible) {
        translateY.value = withSpring(0, {damping: 18, stiffness: 120});
      } else {
        translateY.value = withTiming(-SCREEN_HEIGHT, {duration: 400});
      }
    }, [visible, translateY]);

    const animatedContainer = useAnimatedStyle(
      () => ({
        transform: [{translateY: translateY.value}],
      }),
      [],
    );

    const handleBackdropPress = useCallback(() => {
      onClose?.();
    }, [onClose]);

    const backdrop = useMemo(() => {
      if (!visible) return null;
      return (
        <TouchableWithoutFeedback onPress={handleBackdropPress}>
          <Animated.View
            style={styles.backdrop}
            entering={FadeIn.duration(500)}
            exiting={FadeOut.duration(500)}
          />
        </TouchableWithoutFeedback>
      );
    }, [visible, handleBackdropPress]);

    const modalContent = useMemo(
      () => <View style={containerStyle}>{children}</View>,
      [containerStyle, children],
    );

    if (!visible) return null;

    return (
      <View
        style={StyleSheet.absoluteFill}
        pointerEvents={visible ? 'auto' : 'none'}>
        {backdrop}
        <Animated.View
          style={[styles.modalContainer, modalStyle, animatedContainer]}>
          {modalContent}
        </Animated.View>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 99,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContainer: {
    ...StyleSheet.absoluteFillObject,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 99,
  },
});

export default CustomModal;
