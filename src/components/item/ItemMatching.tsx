import React, {forwardRef, memo} from 'react';
import {StyleSheet, View, ViewStyle} from 'react-native';
import Animated from 'react-native-reanimated';
import {scale} from 'react-native-size-matters';
import ItemQuestion from './ItemQuestion.tsx';

interface ItemBoxProps {
  onPress: (key: string, audio?: string) => void;
  text: string;
  url?: string;
  style?: ViewStyle;
  keyItem: string;
  position: 'left' | 'right';
  disable: boolean;
  audio?: string;
  isSelected: boolean;
  isWrong: boolean;
  isCorrect: boolean;
  isReview: boolean;
}

const ItemMatching = forwardRef<Animated.View, ItemBoxProps>((props, ref) => {
  const {
    onPress,
    text,
    style,
    keyItem,
    position,
    url,
    disable,
    audio,
    isSelected,
    isWrong,
    isCorrect,
    isReview,
  } = props;

  return (
    <View style={styles.container}>
      {position === 'right' && <Animated.View style={styles.point} ref={ref} />}

      <ItemQuestion
        disable={disable}
        text={text}
        onPress={() => onPress(keyItem, audio)}
        image={url}
        audio={audio}
        isSelected={isSelected}
        isWrong={isWrong}
        isCorrect={isCorrect}
        style={styles.itemContainer}
        isReview={isReview}
      />

      {position === 'left' && <Animated.View style={styles.point} ref={ref} />}
    </View>
  );
});

const styles = StyleSheet.create({
  point: {
    width: 3,
    height: 3,
  },
  container: {flexDirection: 'row', alignItems: 'center'},
  itemContainer: {height: scale(70), width: scale(125), marginBottom: 0},
});

export default memo(ItemMatching);
