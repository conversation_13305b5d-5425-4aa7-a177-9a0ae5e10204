import {useTheme} from '../../hooks/useTheme.ts';
import React, {useMemo} from 'react';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {StyleSheet, TouchableOpacity, ViewStyle} from 'react-native';
import TextApp from '../TextApp';
import {
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import {Theme} from '../../themes';

interface FlyingItemProps {
  start: any;
  target: any;
  text: string;
  handleChange: (text: string, id: number | string) => void;
  styleItem?: ViewStyle;
  isSelect: boolean;
  isCorrect: boolean;
  isIncorrect: boolean;
  id: string | number;
}

const FlyingItem = ({
  start,
  target,
  text,
  handleChange,
  styleItem,
  isSelect,
  isCorrect,
  isIncorrect,
  id,
}: FlyingItemProps) => {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const x = useSharedValue(start.x);
  const y = useSharedValue(start.y);
  const atStart = useSharedValue(true);

  const animStyle = useAnimatedStyle(() => ({
    position: 'absolute',
    transform: [{translateX: x.value}, {translateY: y.value}],
  }));

  const fly = () => {
    x.value = withTiming(atStart.value ? target.x : start.x, {duration: 500});
    y.value = withTiming(atStart.value ? target.y : start.y, {duration: 500});
    atStart.value = !atStart.value;
  };

  return (
    <Animated.View style={animStyle}>
      <TouchableOpacity
        disabled={isCorrect || isIncorrect}
        activeOpacity={1}
        onPress={() => {
          handleChange(text, id);
          fly();
        }}
        style={[
          styles.optionButton,
          isSelect &&
            (!isCorrect || !isCorrect) && {
              borderColor: theme.bg_brand_solid,
            },
          isIncorrect && {
            backgroundColor: theme.fg_error_primary,
            borderWidth: 0,
          },
          isCorrect && {
            backgroundColor: theme.fg_success_primary,
            borderWidth: 0,
          },
          styleItem,
        ]}>
        <TextApp
          numberOfLines={1}
          text={text}
          preset={'text_sm_medium'}
          style={[
            isSelect &&
              (!isCorrect || !isCorrect) && {
                backgroundColor: theme.bg_brand_secondary,
                borderColor: theme.fg_white,
                borderWidth: 2,
              },
            isIncorrect && {
              backgroundColor: theme.fg_error_primary,
              borderWidth: 0,
            },
            isCorrect && {
              backgroundColor: theme.fg_success_primary,
              borderWidth: 0,
            },
            styles.textItem,
          ]}
          textColor={
            isCorrect || isIncorrect
              ? theme.text_white
              : isSelect
                ? theme.text_brand_secondary
                : theme.text_secondary
          }
        />
      </TouchableOpacity>
    </Animated.View>
  );
};
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: moderateVerticalScale(130),
      width: '100%',
    },
    shadowWrapper: {
      shadowColor: '#0A0D121A',
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 1,
      shadowRadius: 3,
      elevation: 5,
      alignItems: 'center',
    },
    boxMedia: {
      width: scale(300),
      height: scale(200),
      backgroundColor: '#FFFFFF',
      borderRadius: Theme.radius.radius_2xl,
      marginTop: moderateVerticalScale(20),
      alignItems: 'center',
      justifyContent: 'center',
      overflow: 'hidden',
    },
    image: {
      width: '100%',
      height: '100%',
    },
    questions: {
      marginVertical: verticalScale(20),
      marginHorizontal: scale(40),
    },
    questionContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
      marginBottom: 10,
    },

    blankBox: {
      borderWidth: 2,
      borderColor: '#A4A7AE',
      width: 88,
      height: 40,
      alignItems: 'center',

      borderRadius: Theme.radius.radius_md,
    },
    blankBoxSubmitted: {
      backgroundColor: 'transparent',
      borderWidth: 0,
    },
    optionsContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      flexWrap: 'wrap',
      gap: 10,
      marginBottom: 20,
      marginTop: 40,
    },
    optionButton: {
      borderWidth: 2,
      borderColor: '#A4A7AE',

      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Theme.radius.radius_md,
      backgroundColor: '#FFFFFF',
      width: 120,
    },
    optionShadow: {
      backgroundColor: '#D5D7DA',

      borderWidth: 2,
      borderColor: '#A4A7AE',

      borderRadius: Theme.radius.radius_md,
      margin: 5,

      alignItems: 'center',
      justifyContent: 'center',
    },
    textShadow: {
      fontSize: 16,
      color: '#D5D7DA',
      borderWidth: 2,
      borderColor: '#FFFFFF',
      borderRadius: Theme.radius.radius_md,
      paddingVertical: 6,
      minWidth: 88,
    },

    isSelect: {
      backgroundColor: theme.bg_brand_secondary,
      borderColor: theme.bg_brand_solid,
    },
    textItem: {
      paddingVertical: 6.5,
      width: '100%',
      maxWidth: 116,
      minWidth: 116,
      borderRadius: Theme.radius.radius_md,
      textAlign: 'center',
      lineHeight: 18,
    },
    sentenceContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      paddingHorizontal: 16,
      alignItems: 'center',
      marginHorizontal: Theme.spacing.spacing_xl,
      gap: 6,
      marginTop: 16,
    },
  });
export default FlyingItem;
