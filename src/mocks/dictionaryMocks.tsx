export const dictionaryMocks = {
  hello: {
    meaning: 'Xin chào',
    ipa: '/həˈloʊ/',

  },
  how: {
    meaning: '<PERSON>h<PERSON> thế nào',
    ipa: '/haʊ/',
  },
  are: {
    meaning: 'L<PERSON>, ở',
    ipa: '/ɑːr/',
  },
  you: {
    meaning: 'Bạ<PERSON>',
    ipa: '/juː/',
  },
  is: {meaning: 'Là', ipa: '/ɪz/'},
  emma: {meaning: 'Tên riêng (<PERSON>)', ipa: '/ˈɛmə/'},
  the: {meaning: 'Mạo từ xác định', ipa: '/ðə/'},
  most: {meaning: 'Nhất (so sánh bậc nhất)', ipa: '/moʊst/'},
  important: {meaning: 'Quan trọng', ipa: '/ɪmˈpɔːrtnt/'},
  player: {meaning: 'C<PERSON>u thủ, người chơi', ipa: '/ˈpleɪər/'},
  on: {meaning: 'Trên, thuộc về', ipa: '/ɑːn/'},
  that: {meaning: '<PERSON><PERSON>, kia (đại từ chỉ định)', ipa: '/ðæt/'},
  team: {meaning: 'Đội, nhóm', ipa: '/tiːm/'},
  '?': {meaning: 'Dấu chấm hỏi (câu hỏi)', ipa: ''},
};
