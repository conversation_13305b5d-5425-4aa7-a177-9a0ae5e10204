export const Images = {
  bg_onboarding: require('../../assets/images/bg_onboarding.webp'),
  bgProfile: require('../../assets/images/bgProfile.webp'),
  completed: require('../../assets/images/completed.webp'),
  boyAvt: require('../../assets/images/boy.webp'),
  girlAvt: require('../../assets/images/girl.webp'),
  characterSkip: require('../../assets/images/characterskip.webp'),
  thought: require('../../assets/images/thought.webp'),
  bgLight: require('../../assets/images/bgLight.webp'),
  bgPopupLesson: require('../../assets/images/bgLessonModal.webp'),
  btnActiveLesson: require('../../assets/images/btnActiveLesson.webp'),
  bgPopupMission: require('../../assets/images/bgMission.webp'),
  btnPopupMission: require('../../assets/images/btnActiveMission.webp'),
  btnLocked: require('../../assets/images/btnLocked.webp'),
  helloman: require('../../assets/images/hellomen.webp'),
  btnClockLesson: require('../../assets/images/btnClockedLesson.webp'),
  brokenHeart: require('../../assets/images/BrokenHeart.webp'),
  brokenHeartLeft: require('../../assets/images/brokenHeartLeft.webp'),
  brokenHeartRight: require('../../assets/images/brokenHeartRight.webp'),
  charactorWaiting: require('../../assets/images/charactor-waiting.webp'),
  charactorHappy: require('../../assets/images/charactor-happy.webp'),
  chatBubble: require('../../assets/images/chat_bubble.webp'),
  avatarProfile: require('../../assets/images/avatar.webp'),
  frameStreak: require('../../assets/images/frameStreak.webp'),
  boardProfile: require('../../assets/images/boardProfile.webp'),
  tabActive: require('../../assets/images/tabActive.webp'),
  tabInactive: require('../../assets/images/tabInactive.webp'),
  bgPlayGround: require('../../assets/images/bg-play-ground.webp'),
  bgRolePlay: require('../../assets/images/role-play-bg.webp'),
  bgMissionRolePlay: require('../../assets/images/bg-mission-role-play.webp'),
  rolePlayName: require('../../assets/images/role-play-name.webp'),
  rolePlayBlur: require('../../assets/images/role-play-bg-blur.webp'),
  rolePlayCharactorDefault: require('../../assets/images/role-play-charactor-default.webp'),
  rolePlayCardCharactor1: require('../../assets/images/card-chacractor-1.webp'),
  rolePlayCardCharactor2: require('../../assets/images/card-chacractor-2.webp'),
  rolePlayCardCharactor3: require('../../assets/images/card-chacractor-3.webp'),
  rolePlayCardCharactor4: require('../../assets/images/card-chacractor-4.webp'),
  rolePlayCardCharactor5: require('../../assets/images/card-chacractor-5.webp'),
  bgConfirmChooseCard: require('../../assets/images/bg-confirm-choose-card.webp'),
  bgTopicCard: require('../../assets/images/bg-topic-card.webp'),
  bgComplete: require('../../assets/images/completedQuest.webp'),
  frameText: require('../../assets/images/frameText.webp'),
  doAgain: require('../../assets/images/doAgain.webp'),
  btnReview: require('../../assets/images/btnpreview.webp'),
  completeQ: require('../../assets/images/complete.webp'),
  missionRolePlayFrame: require('../../assets/images/mission-roleplay.webp'),
  subFrameRolePlay: require('../../assets/images/sub-frame-role-play.webp'),
  startNowRolePlay: require('../../assets/images/start-now-role-play.webp'),
  closeContentRolePlay: require('../../assets/images/close-content-role-play.webp'),
  boardName: require('../../assets/images/boardName.webp'),
  bgPractice: require('../../assets/images/bg-practice.webp'),
  bgRolePlayCompleted: require('../../assets/images/bgRolePlayComplete.webp'),
  practiceHeaderBanner: require('../../assets/images/practice_header_banner.webp'),
  practiceMistakeCard: require('../../assets/images/practice_mistake_card.webp'),
  practiceSkillCard: require('../../assets/images/practice_skill_card.webp'),
  topicCard1: require('../../assets/images/topic-card-1.webp'),
  topicCard2: require('../../assets/images/topic-card-2.webp'),
  topicCard3: require('../../assets/images/topic-card-3.webp'),
  topicCard4: require('../../assets/images/topic-card-4.webp'),
  topicCard5: require('../../assets/images/topic-card-5.webp'),
  topicCard6: require('../../assets/images/topic-card-6.webp'),
  topicCard7: require('../../assets/images/topic-card-7.webp'),
  randomTopCard: require('../../assets/images/randomTopCard.webp'),
  mypet: require('../../assets/images/mypet.webp'),
  subFrameTopicCard: require('../../assets/images/sub-frame-topic-card.webp'),
};
