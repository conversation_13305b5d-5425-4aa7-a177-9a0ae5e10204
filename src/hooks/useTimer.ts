import {useCallback, useEffect, useMemo, useState} from 'react';
import {useReduxDispatch} from '../redux/store.ts';
import {setTimeOutQuest} from '../redux/reducer/QuestionSlice.ts';

interface UseTimerProps {
  initialSeconds: number;
  isActive: boolean;
  onTimeUp?: () => void;
}

export const useTimer = ({
  initialSeconds,
  isActive,
  onTimeUp,
}: UseTimerProps) => {
  const dispatch = useReduxDispatch();
  const [secondsLeft, setSecondsLeft] = useState(initialSeconds);

  useEffect(() => {
    setSecondsLeft(initialSeconds);
  }, [initialSeconds]);

  useEffect(() => {
    if (!isActive || secondsLeft <= 0) {
      dispatch(setTimeOutQuest(true));
      return;
    }

    const interval = setInterval(() => {
      setSecondsLeft(prev => {
        if (prev <= 1) {
          clearInterval(interval);
          onTimeUp?.();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [secondsLeft, isActive, onTimeUp]);

  const formattedTime = useMemo(() => {
    const minutes = Math.floor(secondsLeft / 60);
    const seconds = secondsLeft % 60;
    const paddedMinutes = String(minutes).padStart(2, '0');
    const paddedSeconds = String(seconds).padStart(2, '0');
    return `${paddedMinutes}:${paddedSeconds}`;
  }, [secondsLeft]);

  const resetTimer = useCallback(() => {
    setSecondsLeft(initialSeconds);
  }, [initialSeconds]);

  return {
    secondsLeft,
    formattedTime,
    resetTimer,
    isTimeUp: secondsLeft <= 0,
  };
};
