import {useCallback} from 'react';
import useAudioRecorder from '../useAudioRecorder.ts';
import {useReduxDispatch} from '../../redux/store.ts';
import {isAndroid} from '../../utils/Scale.ts';
import {fetchCheckingPhonemes} from '../../redux/reducer/fetchData.ts';
import {
  cleanDataPhonemes,
  completeQuestion,
  hiddenAnswer,
  showAnswer,
  wrongQuestion,
} from '../../redux/reducer/QuestionSlice.ts';

const useQuestion = () => {
  const {recording, audioPath, startRecording, stopRecording} =
    useAudioRecorder();
  const dispatch = useReduxDispatch();

  const clearPhonemes = useCallback(() => {
    dispatch(cleanDataPhonemes());
  }, [dispatch]);

  const handleToggleRecording = useCallback(
    async (question: string) => {
      if (!recording) {
        startRecording();
        return;
      }

      await stopRecording();

      if (!audioPath) {
        console.warn('Không có file để upload!');
        return;
      }

      const fileName = `audio_${Date.now()}.${isAndroid ? 'mp3' : 'm4a'}`;
      const formData = new FormData();
      formData.append('audio', {
        uri: audioPath,
        name: fileName,
        type: isAndroid ? 'audio/mp3' : 'audio/m4a',
      });
      formData.append('text', question);

      await dispatch(fetchCheckingPhonemes(formData));
    },
    [recording, audioPath, startRecording, stopRecording, dispatch],
  );

  const handleCheckAnswer = (
    answerByStudent: any,
    isPass: boolean,
    id: string,
    index: number | string,
    excerciseType?: string,
    answers?: string[] | string,
    question?: string,
  ) => {
    const payload = {
      id,
      index,
      answerByStudent,
      excerciseType,
      answers,
      question,
      isPassExcercise: isPass,
    };

    dispatch(isPass ? completeQuestion(payload) : wrongQuestion(payload));
  };

  const handleShowAnswer = (
    answerByStudent: any,
    excerciseType?: string,
    answers?: string[] | string,
    question?: string,
  ) => {
    dispatch(
      showAnswer({
        answerByStudent,
        excerciseType,
        answers,
        question,
      }),
    );
  };

  const handleCloseAnswer = () => {
    dispatch(hiddenAnswer());
  };

  return {
    recording,
    clearPhonemes,
    handleToggleRecording,
    handleCheckAnswer,
    handleShowAnswer,
    handleCloseAnswer,
  };
};

export default useQuestion;
