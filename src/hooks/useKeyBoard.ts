import {useEffect, useState} from 'react';
import {Keyboard, Platform} from 'react-native';

type UseKeyboardHookReturnType = {
  isKeyboardVisible: boolean;
  keyboardOffset: number;
};

type UseKeyboardHookType = {
  android: boolean;
};

const useKeyboard = ({
  android,
}: UseKeyboardHookType): UseKeyboardHookReturnType => {
  const [keyboardVisible, setKeyboardVisible] = useState<boolean>(false);
  const [keyboardOffset, setKeyboardOffset] = useState<number>(0);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      event => {
        setKeyboardVisible(true);
        setKeyboardOffset(event.endCoordinates.height);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
        setKeyboardOffset(0);
      },
    );

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  const keyboardHeight =
    Platform.OS === 'android' ? (android ? keyboardOffset : 0) : keyboardOffset;
  return {isKeyboardVisible: keyboardVisible, keyboardOffset: keyboardHeight};
};

export default useKeyboard;
