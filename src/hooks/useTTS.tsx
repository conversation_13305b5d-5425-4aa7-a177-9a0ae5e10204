import {useCallback, useEffect} from 'react';
import Speech from '@mhpdev/react-native-speech';
import {isIOS} from '../utils/Scale';

export const useTTS = () => {
  useEffect(() => {
    return () => {
      Speech.stop();
    };
  }, []);

  const speakWord = useCallback((word: string) => {
    if (!word) {
      return;
    }
    Speech.speakWithOptions(word, {
      language: 'en-GB',
      voice: isIOS ? 'com.apple.voice.compact.en-GB.Daniel' : 'en-GB',
      rate: isIOS ? 0.55 : 1,
      volume: 1,
      pitch: 1,
    });
  }, []);

  const stopSpeak = () => {
    Speech.stop();
  };

  return {
    speakWord,
    stopSpeak,
  };
};
