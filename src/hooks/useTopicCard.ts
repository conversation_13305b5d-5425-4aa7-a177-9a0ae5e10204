import {useCallback, useMemo, useRef, useState} from 'react';
import {navigate} from '../navigation/NavigationServices';
import {APP_SCREEN} from '../navigation/screenType';
import {useTypedSelector} from '../redux/store';

const modalData = [
  {
    title: 'Favorite food',
    image: 'topicCard1',
    question: 'What do you like to eat?',
    mission: `🎯 Mission: Tell Cheppy your 3 \nfavorite foods and ask him!`,
    reward: '20',
  },
  {
    title: 'Favorite color',
    image: 'topicCard2',
    question: 'What is your favorite color?',
    mission: '🎯 Mission: Share your top 3 \nfavorite colors with <PERSON>eppy!',
    reward: '15',
  },
  {
    title: 'Your pet',
    image: 'topicCard3',
    question: 'Let’s talk about pets!',
    mission:
      '🎯 Mission: Tell Cheppy what pet you \nhave or want and what it can do.',
    reward: '25',
  },
  {
    title: 'Favorite place',
    image: 'topicCard4',
    question: 'Where do you like to go?',
    mission: '🎯 Mission: Share your top 3 \nfavorite places with <PERSON><PERSON><PERSON>!',
    reward: '30',
  },
  {
    title: 'Favorite place',
    image: 'topicCard5',
    question: 'Where do you like to go?',
    mission: '🎯 Mission: Share your top 3 \nfavorite places with Cheppy!',
    reward: '30',
  },
  {
    title: 'Favorite place',
    image: 'topicCard6',
    question: 'Where do you like to go?',
    mission: '🎯 Mission: Share your top 3 \nfavorite places with Cheppy!',
    reward: '30',
  },
  {
    title: 'Favorite place',
    image: 'topicCard7',
    question: 'Where do you like to go?',
    mission: '🎯 Mission: Share your top 3 \nfavorite places with Cheppy!',
    reward: '30',
  },
];

export const useTopicCard = () => {
  const [topicCardData, setTopicCardData] = useState<any[]>(modalData);
  const [modalState, setModalState] = useState({
    visible: false,
    content: null as RolePlayCard | null,
  });
  const [uiState, setUIState] = useState({
    showMission: false,
    quitModal: false,
    ccActive: false,
    ideaActive: false,
    finishModal: false,
  });
  const speechBubbleRef = useRef<any>(null);
  const idleTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const {data} = useTypedSelector(state => state.profile);

  const handleShowFinishModal = useCallback(() => {
    setUIState(prev => ({
      ...prev,
      finishModal: true,
    }));
  }, []);

  const handleCloseFinishModal = useCallback(() => {
    setUIState(prev => ({
      ...prev,
      finishModal: false,
    }));
  }, []);

  const handleSeeFunTalk = () => {
    setUIState(prev => ({
      ...prev,
      finishModal: false,
    }));
    setTimeout(() => {
      navigate(APP_SCREEN.FULL_CONVERSATION);
    }, 300);
  };

  const handleCloseModal = useCallback(() => {
    setModalState(prev => ({
      ...prev,
      visible: false,
    }));
  }, []);

  const handleStartNow = useCallback(async () => {
    handleCloseModal();
    setUIState(prev => ({
      ...prev,
      showMission: true,
    }));
  }, [handleCloseModal]);

  const handleChooseCard = useCallback((item: any) => {
    setModalState(prev => ({
      ...prev,
      content: item,
    }));
    setTimeout(() => {
      setModalState(prev => ({
        ...prev,
        visible: true,
      }));
      speechBubbleRef?.current?.hide();
    }, 200);
    cancelIdleTimeout();
  }, []);

  const cancelIdleTimeout = () => {
    if (idleTimeoutRef.current) {
      clearTimeout(idleTimeoutRef.current);
      idleTimeoutRef.current = null;
    }
  };

  const startIdleTimeout = () => {
    cancelIdleTimeout();
    idleTimeoutRef.current = setTimeout(() => {
      speechBubbleRef?.current?.show?.(
        'Open a card and shine on stage?',
        false,
      );
    }, 3000);
  };

  const handleCCPress = () => {
    setUIState(prev => {
      const nextState = !prev.ccActive;

      if (nextState) {
        speechBubbleRef?.current?.show?.(
          `Hello there! I can help you expand your answer to increase your fluency. Let's start with the main question: Why do you want to learn English?`,
          false,
        );
      } else {
        speechBubbleRef?.current?.hide?.();
      }

      return {
        ...prev,
        ccActive: nextState,
        ideaActive: nextState ? false : prev.ideaActive,
      };
    });
  };

  const handleIdeaPress = useCallback(() => {
    setUIState(prev => {
      const nextState = !prev.ideaActive;

      if (nextState) {
        speechBubbleRef?.current?.show?.(
          'You can ask me:\n"What is your name?"',
          true,
        );
      } else {
        speechBubbleRef?.current?.hide?.();
      }

      return {
        ...prev,
        ideaActive: nextState,
        ccActive: nextState ? false : prev.ccActive,
      };
    });
  }, []);

  return useMemo(
    () => ({
      modalVisible: modalState.visible,
      modalContent: modalState.content,
      showMission: uiState.showMission,
      quitModal: uiState.quitModal,
      ccActive: uiState.ccActive,
      ideaActive: uiState.ideaActive,
      finishModal: uiState.finishModal,
      coins: data?.coins,
      topicCardData,
      handleCloseModal,
      handleStartNow,
      handleChooseCard,
      handleShowFinishModal,
      handleCloseFinishModal,
      speechBubbleRef,
      handleSeeFunTalk,
      cancelIdleTimeout,
      startIdleTimeout,
      handleCCPress,
      handleIdeaPress,
    }),
    [
      modalState.visible,
      modalState.content,
      uiState.showMission,
      uiState.quitModal,
      uiState.ccActive,
      uiState.ideaActive,
      uiState.finishModal,
      topicCardData,
      handleCloseModal,
      handleStartNow,
      handleChooseCard,
      handleShowFinishModal,
      handleCloseFinishModal,
      speechBubbleRef,
      handleSeeFunTalk,
      startIdleTimeout,
      cancelIdleTimeout,
      handleCCPress,
      handleIdeaPress,
    ],
  );
};
