import {useEffect, useRef, useState} from 'react';
import {Keyboard, TextInput} from 'react-native';

const TIMER_DURATION = 60;

export const useOtpInput = () => {
  const numberOfDigits = 6;
  const inputRef = useRef<TextInput>(null);
  const [otp, setOTP] = useState<string>('');
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [isFullOTP, setIsFullOTP] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [timer, setTimer] = useState<number>(TIMER_DURATION);
  const focusedInputIndex = otp.length;

  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => setTimer(prev => prev - 1), 1000);
      return () => clearInterval(interval);
    }
  }, [timer]);

  const handlePress = () => {
    if (!Keyboard.isVisible()) {
      Keyboard.dismiss();
    }
    inputRef.current?.focus();
  };

  const handleTextChange = (value: string) => {
    const sanitized = value.replace(/\D/g, '');
    setOTP(sanitized);
    setIsFullOTP(sanitized.length === numberOfDigits);
    setError('');
  };

  const updateErorr = (error: string) => {
    setError(error);
  };

  const resendOTP = () => {
    setTimer(TIMER_DURATION);
    setError('');
    setOTP('');
  };

  const clear = () => setOTP('');
  const focus = () => inputRef.current?.focus();
  const blur = () => inputRef.current?.blur();
  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  return {
    otp,
    timer,
    inputRef,
    focusedInputIndex,
    isFocused,
    handleTextChange,
    handlePress,
    clear,
    focus,
    blur,
    handleFocus,
    handleBlur,
    isFullOTP,
    error,
    updateErorr,
    resendOTP,
  };
};
