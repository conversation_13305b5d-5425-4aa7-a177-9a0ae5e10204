import {useCallback, useState} from 'react';
import {resetAndNavigate} from '../../navigation/NavigationServices';
import {APP_SCREEN} from '../../navigation/screenType';
import {fetchRegister} from '../../redux/reducer/fetchData';
import {useReduxDispatch, useTypedSelector} from '../../redux/store';
import {isValidEmail} from '../../utils';
import Toast from 'react-native-toast-message';
import {useTranslate} from '../useTranslate';

type FormDataType = {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
};

const defaultFormData: FormDataType = {
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
};

export const useRegister = () => {
  const [formData, setFormData] = useState<FormDataType>(defaultFormData);
  const [focused, setFocused] = useState({
    username: false,
    email: false,
    password: false,
    confirmPassword: false,
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [termsAccepted, setTermsAccepted] = useState<boolean>(false);
  const loading = useTypedSelector(state => state.auth.loading);
  const dispatch = useReduxDispatch();
  const {t} = useTranslate();

  const rules = {
    validateLength:
      formData?.password?.length > 7
        ? true
        : formData?.password?.length > 0
          ? false
          : undefined,
    hasNumber: /\d/.test(formData?.password)
      ? true
      : formData?.password?.length > 0
        ? false
        : undefined,
    isMatch:
      formData?.confirmPassword?.length > 0
        ? formData?.password === formData?.confirmPassword
        : undefined,
  };

  const onChangeTextInput = useCallback(
    (key: keyof typeof formData, value: string) => {
      setFormData(prev => ({...prev, [key]: value}));
    },
    [],
  );

  const onFocusInput = (key: keyof typeof focused) => {
    setFocused(prev => ({...prev, [key]: true}));
  };

  const onBlurInput = (key: keyof typeof focused) => {
    setFocused(prev => ({...prev, [key]: false}));
    const value = formData[key]?.trim();

    if (!value) {
      const message =
        key === 'email'
          ? 'signup.pleaseEnterEmail'
          : key === 'username'
            ? 'signup.pleaseEnterUsername'
            : 'signup.pleaseEnterPassword';
      setErrors(prev => ({...prev, [key]: message}));
      return;
    }

    if (key === 'email' && !isValidEmail(value)) {
      setErrors(prev => ({...prev, [key]: 'signup.invalidMail'}));
      return;
    }

    setErrors(prev => ({...prev, [key]: ''}));
  };

  const handleRegister = async () => {
    const params = {
      username: formData?.username,
      email: formData?.email,
      password: formData?.password,
    };

    const result = await dispatch(fetchRegister(params));
    if (!fetchRegister.fulfilled.match(result)) return;
    Toast.show({
      text1: t('signup.register_success'),
      type: 'customSuccess',
    });
    resetAndNavigate(APP_SCREEN.LOGIN);
  };

  const handleChangeTerms = () => {
    setTermsAccepted(!termsAccepted);
  };

  const handleLogin = () => {
    resetAndNavigate(APP_SCREEN.LOGIN);
  };
  return {
    formData,
    termsAccepted,
    errors,
    onChangeTextInput,
    onFocusInput,
    onBlurInput,
    handleChangeTerms,
    handleRegister,
    handleLogin,
    focused,
    loading,
    rules,
  };
};
