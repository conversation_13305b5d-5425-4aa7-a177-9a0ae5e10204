import {useEffect} from 'react';
import NetInfo from '@react-native-community/netinfo';
import {useReduxDispatch} from '../redux/store';
import {setConnectionStatus} from '../redux/reducer/NetInfoSlice';

export const useNetInfo = () => {
  const dispatch = useReduxDispatch();

  useEffect(() => {
    NetInfo.fetch().then(state => {
      const isConnected = state.isConnected ?? true;
      dispatch(setConnectionStatus(isConnected));
    });

    const unsubscribe = NetInfo.addEventListener(state => {
      const isConnected = state.isConnected ?? true;
      dispatch(setConnectionStatus(isConnected));
    });

    return () => unsubscribe();
  }, [dispatch]);
};
