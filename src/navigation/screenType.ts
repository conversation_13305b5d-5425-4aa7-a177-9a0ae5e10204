import {NativeStackScreenProps as RNStackScreenProps} from '@react-navigation/native-stack';

export enum APP_SCREEN {
  AUTH = 'AUTH',
  UN_AUTH = 'UN_AUTH',
  LOGIN = '<PERSON><PERSON><PERSON><PERSON>_SCREEN',
  REGISTER = 'R<PERSON>IS<PERSON><PERSON>_SCREEN',
  FORGOT_PASSWORD = 'FORGOT_PASSWORD_SCREEN',
  CHOOSE_CHARACTER = 'CHOOSE_CHARACTER',
  QUESTIONS = 'QUESTIONS_SCREEN',
  REVIEW_QUESTIONS = 'REVIEW_QUESTIONS_SCREEN',
  LESSON = 'LESSON_SCREEN',
  WELLCOME = 'WELLCOME',
  HOME = 'HOME',
  PROFILE = 'PROFILE',
  UNIT = 'UNIT',
  MY_SCHOOL = 'MY_SCHOOL',
  LEADERBOARD = 'LEADERBOARD',
  PRACTICE = 'PRACTICE',
  YOUR_CLASS = 'YOUR_CLASS',
  NOTIFICATION = 'NOTIFICATION',
  CLASS_LEADERBOARD = 'CLASS_LEADERBOARD',
  PLAY_GROUND = 'PLAY_GROUND',
  ROLE_PLAY = 'ROLE_PLAY',
  TOPIC_CARD = 'TOPIC_CARD',
  SETTINGS = 'SETTINGS',
  FULL_CONVERSATION = 'FULL_CONVERSATION',
  EDIT_PROFILE = 'EDIT_PROFILE',
  CHANGE_PASSWORD = 'CHANGE_PASSWORD',
  NOTIFICATION_SETTING = 'NOTIFICATION_SETTING',
  ROLE_PLAY_COMPLETED = 'ROLE_PLAY_COMPLETED',
}

export enum BOTTOM_TAB_ROUTE_NAME {
  HOME = 'HOME',
  CHAT_BOT = 'CHAT_BOT',
  PROFILE = 'PROFILE',
}

export type RootStackParamList = {
  [APP_SCREEN.AUTH]: undefined;
  [APP_SCREEN.UN_AUTH]: undefined;
  [APP_SCREEN.LOGIN]: undefined;
  [APP_SCREEN.REGISTER]: undefined;
  [APP_SCREEN.FORGOT_PASSWORD]: undefined;
  [APP_SCREEN.CHOOSE_CHARACTER]: undefined;
  [APP_SCREEN.QUESTIONS]: {item: Lesson; isDone: boolean};
  [APP_SCREEN.REVIEW_QUESTIONS]: {item: any; isDone: boolean};
  [APP_SCREEN.LESSON]: {unitId: string};
  [APP_SCREEN.WELLCOME]: undefined;
  [APP_SCREEN.HOME]: undefined;
  [APP_SCREEN.PROFILE]: undefined;
  [APP_SCREEN.UNIT]: {classId?: string};
  [APP_SCREEN.LEADERBOARD]: undefined;
  [APP_SCREEN.PRACTICE]: undefined;
  [APP_SCREEN.MY_SCHOOL]: undefined;
  [APP_SCREEN.YOUR_CLASS]: {classId: string; classStatus?: number; classData?: any};
  [APP_SCREEN.NOTIFICATION]: undefined;
  [APP_SCREEN.CLASS_LEADERBOARD]: {classId: string};
  [APP_SCREEN.PLAY_GROUND]: undefined;
  [APP_SCREEN.ROLE_PLAY]: undefined;
  [APP_SCREEN.TOPIC_CARD]: undefined;
  [APP_SCREEN.SETTINGS]: undefined;
  [APP_SCREEN.FULL_CONVERSATION]: undefined;
  [APP_SCREEN.EDIT_PROFILE]: undefined;
  [APP_SCREEN.CHANGE_PASSWORD]: undefined;
  [APP_SCREEN.NOTIFICATION_SETTING]: undefined;
  [APP_SCREEN.ROLE_PLAY_COMPLETED]: {coins: number};
};

export type StackScreenProps<T extends keyof RootStackParamList> =
  RNStackScreenProps<RootStackParamList, T>;
