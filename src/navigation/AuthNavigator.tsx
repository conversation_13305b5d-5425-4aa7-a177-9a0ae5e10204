import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {APP_SCREEN, RootStackParamList} from './screenType.ts';
import Register from '../screens/auth/Register.tsx';
import ForgotPassword from '../screens/auth/ForgotPassWord.tsx';
import LogIn from '../screens/auth/LogIn.tsx';
import WellCome from '../screens/WellCome.tsx';
import {useTypedSelector} from '../redux/store.ts';
import {Choose<PERSON>haracter} from '../screens/auth/ChooseGender.tsx';

const AuthStack = createStackNavigator<RootStackParamList>();

const AuthNavigator = () => {
  const {firstOpen} = useTypedSelector(state => state.auth);
  return (
    <AuthStack.Navigator
      screenOptions={{headerShown: false}}
      initialRouteName={firstOpen ? APP_SCREEN.WELLCOME : APP_SCREEN.LOGIN}>
      <AuthStack.Screen name={APP_SCREEN.WELLCOME} component={WellCome} />
      <AuthStack.Screen name={APP_SCREEN.LOGIN} component={LogIn} />
      <AuthStack.Screen name={APP_SCREEN.REGISTER} component={Register} />
      <AuthStack.Screen
        name={APP_SCREEN.FORGOT_PASSWORD}
        component={ForgotPassword}
      />
      <AuthStack.Screen
        name={APP_SCREEN.CHOOSE_CHARACTER}
        component={ChooseCharacter}
      />
    </AuthStack.Navigator>
  );
};

export default AuthNavigator;
