import axios, {AxiosInstance, AxiosRequestConfig, AxiosResponse} from 'axios';
import {API_BASE_URL} from '@env';
import {resetAndNavigate} from '../navigation/NavigationServices.ts';
import {APP_SCREEN} from '../navigation/screenType.ts';
import i18n from '../i18n';
import {fetchRefreshToken} from '../redux/reducer/fetchData.ts';
import NotificationService from '../services/NotificationService.ts';

let _store: any;
let _persistor: any;

export const injectStore = (store: any, persistor: any) => {
  _store = store;
  _persistor = persistor;
};

const createAxiosInstance = (baseURL: string = API_BASE_URL): AxiosInstance => {
  const instance = axios.create({
    baseURL,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  instance.interceptors.request.use(
    (config: AxiosRequestConfig | any) => {
      const state = _store?.getState?.();
      const token = state?.auth?.token;
      config.headers['accept-language'] = i18n.language || 'en';
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    error => Promise.reject(error),
  );

  instance.interceptors.response.use(
    (response: AxiosResponse) => response,
    async error => {
      const originalRequest = error.config;
      const {status} = error.response || {};

      if (status === 401 && !originalRequest._retry && _store) {
        originalRequest._retry = true;

        const state = _store.getState();
        const refreshToken = state?.auth?.refreshToken;

        if (refreshToken) {
          try {
            const refreshResult = await _store.dispatch(
              fetchRefreshToken({
                client_id: 'elp-web',
                refresh_token: refreshToken,
              }),
            );

            if (fetchRefreshToken.fulfilled.match(refreshResult)) {
              const newTokens = refreshResult.payload;

              _store.dispatch({
                type: 'Auth/logIn',
                payload: {
                  accessToken: newTokens.access_token,
                  refreshToken: newTokens.refresh_token,
                },
              });

              originalRequest.headers.Authorization = `Bearer ${newTokens.access_token}`;
              return instance(originalRequest);
            } else {
              console.log('123');
              NotificationService.clearNotificationState();
              _store.dispatch({type: 'RESET_STATE'});
              _persistor?.purge?.();
              resetAndNavigate(APP_SCREEN.AUTH);
            }
          } catch (refreshError) {
            console.log('234');
            NotificationService.clearNotificationState();
            _store.dispatch({type: 'RESET_STATE'});
            _persistor?.purge?.();
            resetAndNavigate(APP_SCREEN.AUTH);
          }
        } else {
          console.log('345');
          NotificationService.clearNotificationState();
          _store.dispatch({type: 'RESET_STATE'});
          _persistor?.purge?.();
          resetAndNavigate(APP_SCREEN.AUTH);
        }
      } else if (status === 403 && _store) {
        console.log('456');
        NotificationService.clearNotificationState();
        _store.dispatch({type: 'RESET_STATE'});
        _persistor?.purge?.();
        resetAndNavigate(APP_SCREEN.AUTH);
      }

      return Promise.reject(error);
    },
  );

  return instance;
};

const api = createAxiosInstance();

export {api, createAxiosInstance};
