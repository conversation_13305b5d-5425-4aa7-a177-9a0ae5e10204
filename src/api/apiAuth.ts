import axios, {AxiosInstance, AxiosResponse} from 'axios';
import {API_AUTH_BASE_URL} from '@env';
import i18n from '../i18n';

const axiosInstance: AxiosInstance = axios.create({
  baseURL: API_AUTH_BASE_URL,
  headers: {
    accept: 'application/json',
    'accept-language': i18n.language || 'en',
  },
  withCredentials: true,
});

axiosInstance.interceptors.request.use(
  async config => {
    config.headers['accept-language'] = i18n.language || 'vi';
    config.headers.Cookie =
      '97b312292554174fb523a827cf2d3bb8=79be7ec21777b2e2bec33a4f30ce98e4';
    return config;
  },
  error => {
    console.error('[API Request Error]', error);
    return Promise.reject(error);
  },
);

axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  error => {
    console.error('[API response Error]', error);
    if (error.response?.status === 403) {
    }

    return Promise.reject(error);
  },
);

export default axiosInstance;
