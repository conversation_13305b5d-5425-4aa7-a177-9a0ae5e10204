type Point = {
  x: number;
  y: number;
  key: string;
};

type Pair = {
  leftKey: string;
  rightKey: string;
  isCorrect: boolean;
};

type MatchedPair = {
  left: Point;
  right: Point;
  matched: boolean;
};

export function mergeByKeyString(
  left: Point[],
  right: Point[],
  pairs: Pair[],
): MatchedPair[] {
  const leftMap = new Map<string, Point>();
  const rightMap = new Map<string, Point>();

  left.forEach(item => leftMap.set(item.key, item));
  right.forEach(item => rightMap.set(item.key, item));

  const result: MatchedPair[] = pairs.map(pair => {
    const leftPoint = leftMap.get(pair.leftKey);
    const rightPoint = rightMap.get(pair.rightKey);

    if (!leftPoint || !rightPoint) {
      throw new Error(`oh oh${JSON.stringify(pair)}`);
    }

    return {
      left: leftPoint,
      right: rightPoint,
      matched: pair.isCorrect,
    };
  });

  return result;
}
