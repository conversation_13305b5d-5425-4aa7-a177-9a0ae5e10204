type ExerciseItem = {
  exerciseType: string;
  [key: string]: any;
};

export function rearrangeDragAndDrop(arr: ExerciseItem[]): ExerciseItem[] {
  const result: ExerciseItem[] = [];

  for (const item of arr) {
    const last = result[result.length - 1];

    const isDrag = item.exerciseType === 'drag_and_drop';
    const isLastDrag = last?.exerciseType === 'drag_and_drop';

    if (isDrag && isLastDrag) {
      const insertIndex = result.findIndex((curr, idx) => {
        const next = result[idx + 1];
        return (
          curr.exerciseType !== 'drag_and_drop' &&
          (!next || next.exerciseType !== 'drag_and_drop')
        );
      });

      if (insertIndex !== -1) {
        result.splice(insertIndex + 1, 0, item);
      } else {
        result.push(item);
      }
    } else {
      result.push(item);
    }
  }

  return result;
}
