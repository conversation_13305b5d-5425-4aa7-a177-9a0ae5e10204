import {EnhancedDataFinish} from '../types/answer.types';

export const calculateAccuracy = (answer: EnhancedDataFinish): number => {
  if (!answer) return 0;

  const {answers, answerByStudent, excerciseType} = answer;

  switch (excerciseType) {
    case 'multiple_choice':
      return JSON.stringify(answers) === JSON.stringify(answerByStudent)
        ? 100
        : 0;

    case 'pronunciation':
      return answerByStudent?.overall_score || 0;

    case 'matching_pairs':
      if (
        answerByStudent?.matchedKeys &&
        Array.isArray(answerByStudent.matchedKeys)
      ) {
        const total = answerByStudent.matchedKeys.length;
        const correct = answerByStudent.matchedKeys.filter(
          (m: any) => m.isCorrect,
        ).length;
        return total > 0 ? (correct / total) * 100 : 0;
      }
      return 0;

    case 'fill_in_the_blank':
    case 'drag_and_drop':
    case 'dropdown':
    case 'arrangement':
    case 'arrarrangement_dialogue': {
      const ans = answers;
      const stu = answerByStudent;

      if (Array.isArray(ans) && Array.isArray(stu)) {
        const total = ans.length;
        const correct = ans.filter(
          (a, i) =>
            String(a ?? '')
              .toLowerCase()
              .trim() ===
            String(stu[i] ?? '')
              .toLowerCase()
              .trim(),
        ).length;
        return total > 0 ? (correct / total) * 100 : 0;
      }

      if (typeof ans === 'string' && Array.isArray(stu)) {
        const ansArray = ans.split(' ');
        const total = ansArray.length;
        const correct = ansArray.filter(
          (a, i) =>
            String(a ?? '')
              .toLowerCase()
              .trim() ===
            String(stu[i] ?? '')
              .toLowerCase()
              .trim(),
        ).length;
        return total > 0 ? (correct / total) * 100 : 0;
      }

      return String(ans ?? '')
        .toLowerCase()
        .trim() ===
        String(stu ?? '')
          .toLowerCase()
          .trim()
        ? 100
        : 0;
    }

    default:
      return 0;
  }
};
