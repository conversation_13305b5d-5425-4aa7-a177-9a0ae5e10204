import {
  AnswerValue,
  StudentAnswers,
  StudentAnswer,
  NormalizedAnswers,
  AnswerComparisonResult,
  TextSegment,
  ExerciseType,
} from '../types/answer.types';

export const normalizeStudentAnswers = (
  studentAnswers: StudentAnswers,
): NormalizedAnswers => {
  if (!studentAnswers) return {};

  if (Array.isArray(studentAnswers) && typeof studentAnswers[0] === 'string') {
    return studentAnswers.reduce(
      (acc, answer, index) => ({...acc, [index]: answer}),
      {},
    );
  }

  if (Array.isArray(studentAnswers) && typeof studentAnswers[0] === 'object') {
    return (studentAnswers as StudentAnswer[]).reduce(
      (acc, item) => ({
        ...acc,
        [item.id ? item.id - 1 : item.index || 0]: item.answer,
      }),
      {},
    );
  }

  return {};
};

export const compareAnswers = (
  correctAnswers: AnswerValue[] | AnswerValue,
  studentAnswers: StudentAnswers,
): AnswerComparisonResult[] => {
  const normalizedStudent = normalizeStudentAnswers(studentAnswers);
  const correctArray = Array.isArray(correctAnswers)
    ? correctAnswers
    : [correctAnswers];

  return correctArray.map((correct, index) => ({
    isCorrect: normalizedStudent[index] === correct,
    correctAnswer: correct,
    studentAnswer: normalizedStudent[index] || '',
    index,
  }));
};

export const isSingleWordQuestion = (text: string): boolean => {
  if (!text) return false;
  const trimmed = text.trim();
  const wordCount = trimmed.split(/\s+/).length;
  return wordCount === 1;
};

export const parseTextWithBlanks = (
  text: string,
  answers: AnswerValue[],
  studentAnswers: StudentAnswers,
): TextSegment[] => {
  const normalizedStudent = normalizeStudentAnswers(studentAnswers);
  const parts = text.split(/({\d+})/g);

  return parts.map(part => {
    const match = part.match(/{(\d+)}/);
    if (match) {
      const answerIndex = Number(match[1]);
      const correctAnswer = answers[answerIndex] || '';
      const studentAnswer = normalizedStudent[answerIndex] || '';

      return {
        type: 'answer' as const,
        content: String(correctAnswer),
        isCorrect: studentAnswer === correctAnswer,
        index: answerIndex,
      };
    }

    return {
      type: 'text' as const,
      content: part,
    };
  });
};

export const processArrangementAnswers = (
  correctAnswers: AnswerValue[] | AnswerValue,
  studentAnswers: StudentAnswers,
  isSingleWord: boolean = false,
): AnswerComparisonResult[] => {
  if (Array.isArray(correctAnswers)) {
    return correctAnswers.map((sentence, index) => {
      const studentSentence = Array.isArray(studentAnswers)
        ? studentAnswers[index]
        : '';

      return {
        isCorrect: String(sentence) === String(studentSentence),
        correctAnswer: String(sentence),
        studentAnswer: String(studentSentence) || '',
        index,
      };
    });
  }

  const answerString = String(correctAnswers);
  const correctWords = isSingleWord
    ? answerString.trim().split('')
    : answerString.trim().split(/\s+/);

  const studentArray = Array.isArray(studentAnswers) ? studentAnswers : [];

  return correctWords.map((word, index) => ({
    isCorrect: String(studentArray[index]) === word,
    correctAnswer: word,
    studentAnswer: String(studentArray[index]) || '',
    index,
  }));
};

export const getExerciseProcessor = (exerciseType: ExerciseType) => {
  switch (exerciseType) {
    case ExerciseType.ARRANGEMENT:
    case ExerciseType.ARRANGEMENT_DIALOGUE:
      return processArrangementAnswers;

    case ExerciseType.FILL_IN_THE_BLANK:
    case ExerciseType.DROPDOWN:
      return parseTextWithBlanks;

    default:
      return compareAnswers;
  }
};

export const validateAnswerFormat = (
  answers: any,
  studentAnswers: any,
  exerciseType: ExerciseType,
): boolean => {
  if (!answers || !studentAnswers) return false;

  switch (exerciseType) {
    case ExerciseType.MULTIPLE_CHOICE:
      return typeof answers === 'string' && Array.isArray(studentAnswers);

    case ExerciseType.ARRANGEMENT:
      return Array.isArray(answers) || typeof answers === 'string';

    case ExerciseType.FILL_IN_THE_BLANK:
    case ExerciseType.DROPDOWN:
      return Array.isArray(answers) && Array.isArray(studentAnswers);

    default:
      return true;
  }
};
