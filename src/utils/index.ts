export const isNullOrEmpty = (data: any | any[]): boolean => {
  if (!data) {
    return true;
  }
  if (data instanceof Array) {
    return data.length === 0;
  }
  if (typeof data === 'number') {
    return data === 0;
  }
  if (typeof data === 'undefined') {
    return true;
  }
  if (typeof data === 'object') {
    return Object.keys(data).length === 0;
  }
  let output = data;
  if (typeof output !== 'string') {
    output = output.toString();
  }
  output = output.trim();

  return output.length <= 0;
};

export const parseTextWithBlanks = (template: string) => {
  const regex = /\{(\d+)\}/g;
  const result: {type: 'text' | 'blank'; value?: string; index?: number}[] = [];
  let lastIndex = 0;
  let match;

  while ((match = regex.exec(template)) !== null) {
    const index = parseInt(match[1], 10);
    if (match.index > lastIndex) {
      result.push({
        type: 'text',
        value: template.slice(lastIndex, match.index),
      });
    }
    result.push({type: 'blank', index});
    lastIndex = regex.lastIndex;
  }

  if (lastIndex < template.length) {
    result.push({type: 'text', value: template.slice(lastIndex)});
  }

  return result;
};

export const chunkArray = (arr: string[], size: number): string[][] => {
  const result = [];
  for (let i = 0; i < arr.length; i += size) {
    result.push(arr.slice(i, i + size));
  }
  return result;
};

export const isCorrectAnswers = (data: any, correctAnswers: any) => {
  if (data.length !== correctAnswers?.length) return false;

  return data.every(
    (item: any, index: any) => item?.answer === correctAnswers[index],
  );
};

const estimateWordWidth = (word: string) => {
  const avgCharWidth = 10;
  const basePadding = word.length === 1 ? 30 : 20;
  return word.length * avgCharWidth + basePadding;
};

export const calculateWordBankLines = (words: string[], width: number) => {
  if (!words || words?.length === 0) return 1;
  const containerWidth = width;
  let currentLineWidth = 0;
  let lineCount = 1;

  words.forEach((word: string, index: number) => {
    const wordWidth = estimateWordWidth(word);
    const wordGap = word.length === 1 ? 10 : 4;

    if (
      currentLineWidth + wordWidth + (index > 0 ? wordGap : 0) <=
      containerWidth
    ) {
      currentLineWidth += wordWidth + (index > 0 ? wordGap : 0);
    } else {
      lineCount++;
      currentLineWidth = wordWidth;
    }
  });

  return lineCount;
};

export const isSingleWordQuestion = (word: string): boolean => {
  const trimmed = word?.trim();
  const wordCount = trimmed?.split(/\s+/).length;
  return wordCount === 1;
};

export const shuffleArray = <T>(array: T[]): T[] => {
  const newArray = [...array];
  for (let i = newArray?.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
};

export const isValidEmail = (email: string) =>
  /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

export function removePunctuation(str: string) {
  return str.replace(/[.,\/#!$%\^&\*;:{}=\-_`~()?"'<>@[\]|]/g, '');
}

export const isEnglishOnly = (text: string) => /^[A-Za-z\s]*$/.test(text);
