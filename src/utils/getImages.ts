import {API_INTEGRATE_URL} from '@env';
import {Theme} from '../themes';
import FastImage from 'react-native-fast-image';

export const getImages = (uri: string = '', isFastImage: boolean = false) => {
  if (uri) {
    if (isFastImage) {
      return {
        uri: API_INTEGRATE_URL + 'files/get-by-path?path=' + uri,
        priority: FastImage.priority.high,
        cache: FastImage.cacheControl.immutable,
      };
    }
    return {
      uri: API_INTEGRATE_URL + 'files/get-by-path?path=' + uri,
    };
  }
  return Theme.icons.character;
};
