type TextPart = {
  type: 'text';
  value: string;
};

type BlankPart = {
  type: 'blank';
  index: number;
  answer: null;
  id: any;
};

type Part = TextPart | BlankPart;

export function parseStringToParts(input: string): Part[] {
  const result: Part[] = [];

  const regex = /\{(\d+)\}|[^\s{}]+|\s+/g;

  let match;
  while ((match = regex.exec(input)) !== null) {
    if (match[0].startsWith('{') && match[0].endsWith('}')) {
      const index = parseInt(match[1], 10);
      result.push({
        type: 'blank',
        index,
        answer: null,
        id: null,
      });
    } else {
      if (match[0].trim().length > 0) {
        result.push({
          type: 'text',
          value: match[0],
        });
      }
    }
  }

  return result;
}
