import {createSlice} from '@reduxjs/toolkit';

const offlineSlice = createSlice({
  name: 'offline',
  initialState: {
    visible: false,
  },
  reducers: {
    showOfflineBar: state => {
      state.visible = true;
    },
    hideOfflineBar: state => {
      state.visible = false;
    },
  },
});

export const {showOfflineBar, hideOfflineBar} = offlineSlice.actions;
export default offlineSlice.reducer;
