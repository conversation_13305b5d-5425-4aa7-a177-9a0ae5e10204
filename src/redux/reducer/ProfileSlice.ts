import {createSlice} from '@reduxjs/toolkit';
import {
  fetchAssetsWardrobe,
  fetchCountMessage,
  fetchDataInfo,
  fetchReadMessage,
  fetchSaveAssetsWardrobe,
  fetchUpdateAvatar,
  fetchUpdateProfile,
} from './fetchData';

interface ProfileSliceData {
  data?: ProfileData;
  loading: boolean;
  error: string;
  countNotification: number;
  isPlayBgSound: boolean;
  character: {
    shirtId?: number | string;
    pantsId?: number | string;
    accessoryId?: number | string;
    faceId?: number | string;
    petsId?: number | string;
  };
  assetsCharacter: any;
}

type WardrobeItem = {
  id: string;
  code: string;
  name: string;
  path: string;
  isVisible: number;
};

type WardrobeGroup = {
  typeId: string;
  typeName: string;
  typeCode: 'SHIRT' | 'PANTS' | 'FACE' | 'PET' | 'GLASSES';
  wardrobes: WardrobeItem[];
};

type Character = {
  shirtId?: string;
  pantsId?: string;
  accessoryId?: string;
  faceId?: string;
  petsId?: string;
};

const typeMap: Record<WardrobeGroup['typeCode'], keyof Character> = {
  SHIRT: 'shirtId',
  PANTS: 'pantsId',
  FACE: 'faceId',
  PET: 'petsId',
  GLASSES: 'accessoryId',
};

function buildCharacter(data: WardrobeGroup[]): Character {
  return data.reduce<Character>((acc, type) => {
    const visibleItem = type.wardrobes.find(item => item.isVisible === 1);
    if (visibleItem) {
      const key = typeMap[type.typeCode];
      acc[key] = visibleItem.id;
    }
    return acc;
  }, {});
}

const initialState: ProfileSliceData = {
  data: undefined,
  loading: false,
  error: '',
  countNotification: 0,
  isPlayBgSound: true,
  character: {
    shirtId: 1,
    pantsId: 1,
    accessoryId: 1,
    faceId: 1,
    petsId: 1,
  },
  assetsCharacter: null,
};

const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {
    resetDataProfile: () => initialState,
    controllerSoundBg: state => {
      state.isPlayBgSound = !state.isPlayBgSound;
    },
    changeCharacter: (state, action) => {
      state.character = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchDataInfo.pending, state => {
        state.loading = true;
        state.error = '';
      })
      .addCase(fetchDataInfo.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action?.payload;
      })
      .addCase(fetchDataInfo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchCountMessage.fulfilled, (state, action) => {
        state.countNotification = action.payload?.data;
      })
      .addCase(fetchReadMessage.fulfilled, state => {
        state.countNotification = state.countNotification - 1;
      })
      .addCase(fetchUpdateProfile.pending, state => {
        state.loading = true;
        state.error = '';
      })
      .addCase(fetchUpdateProfile.fulfilled, (state, action) => {
        state.loading = false;
        // state.data = action?.payload;
      })
      .addCase(fetchUpdateProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchUpdateAvatar.pending, state => {
        state.loading = true;
        state.error = '';
      })
      .addCase(fetchUpdateAvatar.fulfilled, (state, action) => {
        state.loading = false;
        // state.data = action?.payload;
      })
      .addCase(fetchUpdateAvatar.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchAssetsWardrobe.pending, state => {
        state.loading = true;
        state.error = '';
      })
      .addCase(fetchAssetsWardrobe.fulfilled, (state, action) => {
        state.loading = false;
        state.assetsCharacter = action?.payload?.data;
        state.character = buildCharacter(action?.payload?.data);
      })
      .addCase(fetchAssetsWardrobe.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchSaveAssetsWardrobe.pending, state => {
        state.loading = true;
        state.error = '';
      })
      .addCase(fetchSaveAssetsWardrobe.fulfilled, (state, action) => {
        state.loading = false;
        state.assetsCharacter = action?.payload?.data;
        state.character = buildCharacter(action?.payload?.data);
      })
      .addCase(fetchSaveAssetsWardrobe.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});
export const {controllerSoundBg, changeCharacter} = profileSlice.actions;
export default profileSlice.reducer;
