import {createSlice, PayloadAction} from '@reduxjs/toolkit';

type NetInfoState = {
  isConnected: boolean;
};

const initialState: NetInfoState = {
  isConnected: true,
};

const netInfoSlice = createSlice({
  name: 'netInfo',
  initialState,
  reducers: {
    setConnectionStatus: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
    },
  },
});

export const {setConnectionStatus} = netInfoSlice.actions;
export default netInfoSlice.reducer;
