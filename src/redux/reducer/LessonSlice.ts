import {createSlice} from '@reduxjs/toolkit';
import {
  fetchDoneLesson,
  fetchLessonAnswer,
  fetchLessonPracticeAnswer,
  fetchLessonWithUnitID,
} from './fetchData';

interface LessonState {
  data: Lesson[];
  loading: boolean;
  error: string;
  done: any;
  dataAnswer: any;
}

const initialState: LessonState = {
  data: [],
  loading: false,
  error: '',
  done: null,
  dataAnswer: null,
};

const LessonSlice = createSlice({
  name: 'lesson',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(fetchLessonWithUnitID.pending, state => {
        state.error = '';
      })
      .addCase(fetchLessonWithUnitID.fulfilled, (state, action) => {
        state.data = action.payload?.data;
      })
      .addCase(fetchLessonWithUnitID.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      .addCase(fetchDoneLesson.pending, state => {
        state.error = '';
      })
      .addCase(fetchDoneLesson.fulfilled, (state, action) => {
        state.done = action.payload?.data;
      })
      .addCase(fetchDoneLesson.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      .addCase(fetchLessonAnswer.pending, state => {
        state.loading = true;
        state.error = '';
      })
      .addCase(fetchLessonAnswer.fulfilled, (state, action) => {
        state.loading = false;
        state.dataAnswer = action.payload?.data;
      })
      .addCase(fetchLessonAnswer.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchLessonPracticeAnswer.pending, state => {
        state.loading = true;
        state.error = '';
      })
      .addCase(fetchLessonPracticeAnswer.fulfilled, (state, action) => {
        state.loading = false;
        state.dataAnswer = action.payload?.data;
      })
      .addCase(fetchLessonPracticeAnswer.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export default LessonSlice.reducer;
