import {createSlice} from '@reduxjs/toolkit';

interface UIState {
  showModal: boolean;
  showAlert: boolean;
  alertMessage: string | null;
}

const initialState: UIState = {
  showModal: false,
  alertMessage: null,
  showAlert: false,
};

const PopUpSlice = createSlice({
  name: 'popup',
  initialState,
  reducers: {
    showModal: state => {
      state.showModal = true;
    },
    hideModal: state => {
      state.showModal = false;
    },
    showAlert: (state, action) => {
      state.showAlert = true;
      state.alertMessage = action.payload;
    },
    hideAlert: state => {
      state.showAlert = false;
      state.alertMessage = null;
    },
  },
});

export const {showModal, hideModal, showAlert, hideAlert} = PopUpSlice.actions;
export default PopUpSlice.reducer;
