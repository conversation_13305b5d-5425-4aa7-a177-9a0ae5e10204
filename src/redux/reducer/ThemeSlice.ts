import {createSlice, PayloadAction} from '@reduxjs/toolkit';

type ThemeType = 'girl' | 'boy';

interface ThemeState {
  mode: ThemeType;
}

const initialState: ThemeState = {
  mode: 'girl',
};

const ThemeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<ThemeType>) => {
      state.mode = action.payload;
    },
    toggleTheme: state => {
      state.mode = state.mode === 'girl' ? 'boy' : 'girl';
    },
  },
});

export const {setTheme, toggleTheme} = ThemeSlice.actions;
export default ThemeSlice.reducer;
