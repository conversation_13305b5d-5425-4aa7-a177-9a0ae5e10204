import {createSlice} from '@reduxjs/toolkit';
import {fetchSchoolByStudent} from './fetchData';

interface SchoolState {
  listSchool: any[];
  loading: boolean;
  error: string;
}

const initialState: SchoolState = {
  listSchool: [],
  loading: false,
  error: '',
};

const SchoolSlice = createSlice({
  name: 'school',
  initialState,
  reducers: {
    resetData: () => initialState,
  },
  extraReducers: builder => {
    builder
      .addCase(fetchSchoolByStudent.pending, state => {
        state.loading = true;
        state.error = '';
      })
      .addCase(fetchSchoolByStudent.fulfilled, (state, action) => {
        state.loading = false;
        state.listSchool = action?.payload?.data;
      })
      .addCase(fetchSchoolByStudent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});
export default SchoolSlice.reducer;
