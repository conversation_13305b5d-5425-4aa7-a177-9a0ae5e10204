import {createSlice} from '@reduxjs/toolkit';
import {fetchCheckingPhonemes} from './fetchData';
import {calculateAccuracy} from '../../utils/quotes';
import {EnhancedDataFinish} from '../../types/answer.types';

interface ScoreData {
  word: string;
  score_grade: string;
}

interface QuestionSliceData {
  dataPhonemes?: {
    word_scores?: ScoreData[];
  };
  loading: boolean;
  error: string;
  isShowAnswer: boolean;
  isShowQuotes: boolean;
  dataFinish: DataFinish[];
  answer: EnhancedDataFinish | undefined;
  isDone: boolean;
  heart: any;
  isTimeOut: boolean;
}

const initialState: QuestionSliceData = {
  dataPhonemes: undefined,
  loading: false,
  error: '',
  isShowQuotes: false,
  isShowAnswer: false,
  dataFinish: [],
  answer: undefined,
  isDone: false,
  heart: null,
  isTimeOut: false,
};

const QuestionSlice = createSlice({
  name: 'questions',
  initialState,
  reducers: {
    resetDataQuestion: () => initialState,

    cleanDataPhonemes: state => {
      state.dataPhonemes = undefined;
    },

    completeQuestion: (state, action) => {
      const answer = action.payload;
      const accuracy = calculateAccuracy(answer);
      state.isShowQuotes = true;
      state.dataFinish = [...state.dataFinish, answer];
      state.answer = {
        ...answer,
        accuracy,
      };
    },
    wrongQuestion: (state, action) => {
      const answer = action.payload;
      const accuracy = calculateAccuracy(answer);
      state.isShowQuotes = true;
      state.heart = state.heart - 1;
      state.dataFinish = [...state.dataFinish, answer];
      state.answer = {
        ...answer,
        accuracy,
      };
    },
    skipQuestion: (state, action) => {
      state.dataFinish = [...state.dataFinish, action.payload];
      state.answer = action.payload;
    },
    hideModalQuestion: state => {
      state.isShowQuotes = false;
      state.answer = undefined;
    },
    showAnswer: (state, action) => {
      state.isShowAnswer = true;
      state.answer = action.payload;
    },
    hiddenAnswer: state => {
      state.isShowAnswer = false;
      state.answer = undefined;
    },
    setDoneQuestion: (state, action) => {
      state.isDone = action.payload;
    },
    setHeart: (state, action) => {
      state.heart = action.payload;
    },
    setTimeOutQuest: (state, action) => {
      state.isTimeOut = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchCheckingPhonemes.pending, state => {
        state.loading = true;
        state.error = '';
      })
      .addCase(fetchCheckingPhonemes.fulfilled, (state, action) => {
        state.loading = false;
        state.dataPhonemes = action.payload?.data;
      })
      .addCase(fetchCheckingPhonemes.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});
export const {
  resetDataQuestion,
  cleanDataPhonemes,
  wrongQuestion,
  completeQuestion,
  hideModalQuestion,
  skipQuestion,
  showAnswer,
  hiddenAnswer,
  setDoneQuestion,
  setHeart,
  setTimeOutQuest,
} = QuestionSlice.actions;
export default QuestionSlice.reducer;
