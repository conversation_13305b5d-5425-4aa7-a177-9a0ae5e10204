import {createSlice} from '@reduxjs/toolkit';

interface RefreshState {
  needsDataRefresh: number;
}

const initialState: RefreshState = {
  needsDataRefresh: 0,
};

const RefreshSlice = createSlice({
  name: 'refresh',
  initialState,
  reducers: {
    triggerDataRefresh: (state) => {
      state.needsDataRefresh += 1;
    },
    resetDataRefresh: (state) => {
      state.needsDataRefresh = 0;
    },
  },
});

export const {
  triggerDataRefresh,
  resetDataRefresh,
} = RefreshSlice.actions;

export default RefreshSlice.reducer;
