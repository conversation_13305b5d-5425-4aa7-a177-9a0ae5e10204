import i18n from 'i18next';
import {initReactI18next} from 'react-i18next';
import vi from './vi.json';
import en from './en.json';
import AsyncStorage from '@react-native-async-storage/async-storage';

const STORE_LANGUAGE_KEY = 'settings.lang';

const languageDetectorPlugin = {
  type: 'languageDetector' as const,
  async: true,
  init: () => {},
  detect: async (callback: (lang: string) => void) => {
    try {
      const language = await AsyncStorage.getItem(STORE_LANGUAGE_KEY);
      console.log('Detected language:', language);
      if (language) {
        callback(language);
      } else {
        console.log('No language found, defaulting to en');
        callback('en');
      }
    } catch (error) {
      console.error('Error reading language from AsyncStorage:', error);
      callback('en');
    }
  },
  cacheUserLanguage: async (language: string) => {
    try {
      console.log('Saving language:', language);
      await AsyncStorage.setItem(STORE_LANGUAGE_KEY, language);
    } catch (error) {
      console.error('Error saving language to AsyncStorage:', error);
    }
  },
};

i18n
  .use(languageDetectorPlugin)
  .use(initReactI18next)
  .init({
    fallbackLng: 'vi',
    resources: {
      en: {translation: en},
      vi: {translation: vi},
    },
    interpolation: {
      escapeValue: false,
    },
    initImmediate: false,
  });

export default i18n;
