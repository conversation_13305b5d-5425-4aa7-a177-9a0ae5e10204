import Sound from 'react-native-sound';
import {AppState} from 'react-native';
import {APP_SCREEN} from '../navigation/screenType';

Sound.setCategory('Playback');

let bgSound: Sound | null = null;
let currentFile: string | null = null;
let isSoundEnabled: boolean = false;
let shouldBlockAutoResume = false;

const MUTED_SCREENS: APP_SCREEN[] = [
  APP_SCREEN.QUESTIONS,
  APP_SCREEN.REVIEW_QUESTIONS,
  APP_SCREEN.ROLE_PLAY,
  APP_SCREEN.ROLE_PLAY_COMPLETED,
  APP_SCREEN.LOGIN,
  APP_SCREEN.REGISTER,
  APP_SCREEN.FORGOT_PASSWORD,
  APP_SCREEN.WELLCOME,
];

let currentScreen: APP_SCREEN | null = null;

const setBlockAutoResume = (block: boolean) => {
  shouldBlockAutoResume = block;
};

const isScreenMuted = (screenName: APP_SCREEN) => {
  return MUTED_SCREENS.includes(screenName);
};

const handleScreenChange = (
  screenName: APP_SCREEN,
  isSoundBgEnabled: boolean = true,
) => {
  currentScreen = screenName;

  if (isScreenMuted(screenName)) {
    if (bgSound && isSoundEnabled) {
      pause(true);
    }
  } else {
    if (isSoundBgEnabled && currentFile && shouldBlockAutoResume) {
      setBlockAutoResume(false);
      resume();
    }
  }
};

AppState.addEventListener('change', nextAppState => {
  if (nextAppState !== 'active') {
    if (bgSound) {
      bgSound.stop();
      bgSound.release();
      bgSound = null;
    }
  } else if (isSoundEnabled && currentFile && !shouldBlockAutoResume) {
    loadAndPlay(currentFile);
  }
});

const loadAndPlay = (filename: string) => {
  bgSound = new Sound(filename, Sound.MAIN_BUNDLE, error => {
    if (error) {
      return;
    }

    if (typeof bgSound?.setNumberOfLoops === 'function') {
      bgSound.setNumberOfLoops(-1);
      bgSound?.setVolume(0.6);
    }

    bgSound?.play(success => {
      if (!success) {
        console.log('Background music play failed');
      }
    });
  });
  currentFile = filename;
};

const play = (filename: string) => {
  isSoundEnabled = true;
  if (bgSound && currentFile === filename) {
    return;
  }
  if (bgSound) {
    bgSound.stop();
    bgSound.release();
    bgSound = null;
  }
  loadAndPlay(filename);
};

const pause = (manual = false) => {
  if (manual) shouldBlockAutoResume = true;
  bgSound?.pause();
};

const resume = () => {
  if (bgSound) {
    isSoundEnabled = true;
    bgSound.play();
  } else if (currentFile) {
    loadAndPlay(currentFile);
  }
};

const stop = () => {
  isSoundEnabled = false;
  if (bgSound) {
    bgSound.stop();
    bgSound.release();
    bgSound = null;
  }
};

export default {
  play,
  pause,
  resume,
  stop,
  setBlockAutoResume,
  handleScreenChange,
  isScreenMuted,
};
