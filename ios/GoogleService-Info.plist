<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>73836829827-3u3tv2p8ep1bs8rh60ankni0rbmi4lsn.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.73836829827-3u3tv2p8ep1bs8rh60ankni0rbmi4lsn</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>73836829827-5kqgjd72mqrudhj4m7gfidrlifk6jrqs.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyACjuqFu93EizrzxC3zlK_gODBf3uROv4E</string>
	<key>GCM_SENDER_ID</key>
	<string>73836829827</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.app.ispeaking.demo</string>
	<key>PROJECT_ID</key>
	<string>i-speaking-a1731</string>
	<key>STORAGE_BUCKET</key>
	<string>i-speaking-a1731.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:73836829827:ios:4f215210496fc0c24d4fb6</string>
</dict>
</plist>