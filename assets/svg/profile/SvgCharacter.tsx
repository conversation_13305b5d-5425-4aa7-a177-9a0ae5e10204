import * as React from 'react';
import Svg, {G, Image, Path} from 'react-native-svg';
import {heightScreen, widthScreen} from '../../../src/utils/Scale.ts';
import {getImages} from '../../../src/utils/getImages.ts';

interface SvgCharacterProps {
  shirt: any;
  pants: any;
  accessory: any;
  face: any;
}

const scaleX = widthScreen / 1125;
const scaleY = heightScreen / 2436;
const SvgCharacter: React.FC<SvgCharacterProps> = ({
  shirt,
  pants,
  accessory,
  face,
}) => (
  <Svg
    width={417 * scaleX}
    height={460 * scaleY}
    preserveAspectRatio="xMidYMid meet"
    viewBox="0 0 486 752"
    fill="none">
    <G id="Group 14301">
      <G id="AAAA">
        <Path
          id="Vector"
          fill="#FED3AA"
          d="M233.459 608.567v-162.04c19.09 2.87 38.66 1.1 58.03.99 4.58-.03 9.27.07 13.91.48l-7.81-2.61c9.23.7 18.52.1 27.77.44 9.25.34 18.73 1.71 26.63 5.95 4.13 2.21 7.7 5.14 11.17 8.09 7.72 6.57 14.92 13.59 21.62 20.96 7.7 8.47 13.04 15.71 23.12 22l26.92 23.52c-6.24 9.83 5.42 19.44-.5 29.42-.7 1.18-14.29-1.68-15.9-2.04-12.82-2.8-24.66-9.37-35.52-15.58-6.61-3.78-11.54-8.07-17.63-12.42-2.73-1.95-5.68-3.89-8.14-6.08-4.87-4.33-5.86-2.34-6.9 3.8-.96 5.68-.48 11.54.3 17.22 1.97 14.43 3.72 28.63 5.62 43.07.71 5.35 4.98 25.14-2.03 28.27-35.82 2.47-81.86-5.84-117.35-.88-1.1.15-2.33.3-3.24-.26-.91-.56-1.2-2.3-.08-2.3h.01Z"
        />
        <Path
          id="Vector_2"
          fill="#FED3AA"
          d="M253.82 608.567v-162.04c-19.09 2.87-38.66 1.1-58.03.99-4.58-.03-9.27.07-13.91.48l7.81-2.61c-9.23.7-18.52.1-27.77.44-9.25.34-18.73 1.71-26.63 5.95-4.13 2.21-7.7 5.14-11.17 8.09a235.565 235.565 0 0 0-21.62 20.96c-7.7 8.47-13.04 15.71-23.12 22l-26.92 23.52c6.24 9.83-5.42 19.44.5 29.42.7 1.18 14.29-1.68 15.9-2.04 12.82-2.8 24.66-9.37 35.52-15.58 6.61-3.78 11.54-8.07 17.63-12.42 2.73-1.95 5.68-3.89 8.14-6.07 1.94-1.72 4.05-5.02 5.6-1.24 6.85 16.74 5.02 37.53 1.56 54.85-.82 4.1-8.76 38.19-.71 38.75 35.82 2.47 78.42-5.84 113.91-.88 1.1.15 2.33.3 3.24-.26.91-.56 1.2-2.3.08-2.3l-.01-.01Z"
        />
        <Path
          id="Vector_3"
          fill="#FED3AA"
          d="M134 605.077c-2.35 16.95-5.49 34.48-.51 51.03.55 1.84 1.11 3.69 1.35 5.57.38 2.94-.03 5.91-.41 8.86-1.95 15.15-3.17 24.13 1.54 38.86 25.19 3.92 51.05 4.89 76.57 2.88 2.19-.17 4.51-.41 6.21-1.57 1.87-1.28 2.55-3.38 3.07-5.34 3.04-11.44 4.43-16.62 4.11-28.33-.04-1.48-.13-3.02-.99-4.31-.69-1.04-1.87-1.9-2.12-3.07-.36-1.7 1.36-3.18 2.92-4.32 2.12-1.55 4.25-3.09 6.37-4.64.54-.4 1.11-.8 1.81-.97.82-.19 1.69-.02 2.52.13a85.053 85.053 0 0 0 28.34.53c-2 .53-3.31-1.7-3.66-3.41a194.15 194.15 0 0 1-3.29-54.18l-105.07 3.51"
        />
        <Path
          id="Vector_4"
          fill="#FED3AA"
          d="M357.88 607.957c2.35 16.95 5.77 31.6.8 48.15-.55 1.84-1.11 3.69-1.35 5.57-.38 2.94.03 5.91.41 8.86 1.95 15.15 3.17 24.13-1.54 38.86-25.19 3.92-51.05 4.89-76.57 2.88-2.19-.17-4.51-.41-6.21-1.57-1.87-1.28-2.55-3.38-3.07-5.34-3.04-11.44-4.43-16.62-4.11-28.33.04-1.48.13-3.02.99-4.31.69-1.04 1.87-1.9 2.12-3.07.36-1.7-1.36-3.18-2.92-4.32-2.12-1.55-4.25-3.09-6.37-4.64-.54-.4-1.11-.8-1.81-.97-.82-.19-1.69-.02-2.52.13a85.053 85.053 0 0 1-28.34.53c2 .53 3.31-1.7 3.66-3.41a194.15 194.15 0 0 0 3.29-54.18l105.07 3.51"
        />
        <Path
          id="Vector_5"
          fill="#FED3AA"
          d="M424.81 520.097c3.12.81 6.38-.65 9.1-2.27 5.93-3.53 11.18-8.03 15.4-13.21.43-.53.88-1.08 1.51-1.4 1.37-.68 3.15.02 4.14 1.13 2.65 2.95 1.05 7.33-.65 10.82-2.04 4.16-3.18 6.5-5.21 10.66 8.95 4.99 18.37 9.31 28.5 11.82 1.82.45 3.68.92 5.21 1.94 1.53 1.02 2.66 2.69 2.38 4.39-.39 2.4-3.41 3.8-6.08 3.69-2.67-.11-5.04-1.68-7.66-2.06-3.07-.45-8.41-1.69-10.7-.43 5.39 2.92 13.73 6.33 19.12 9.25.66.36 1.34.73 1.73 1.33.56.85.4 1.93.22 2.91-.37 2.01-.91 4.28-2.83 5.34-1.75.97-4 .56-5.95-.03-6.27-1.9-11.84-5.3-17.32-8.66-.45-.27-.96-.56-1.49-.48-3.5.53 2.21 4.93 2.67 5.32 3.08 2.63 6.08 5.35 9.01 8.15 1.74 1.66 3.59 3.86 2.96 6.18-.45 1.63-2.08 2.72-3.75 2.98-1.67.26-3.37-.17-4.97-.7a36.991 36.991 0 0 1-14.73-9.21c-1.08-1.11-2.14-2.32-3.58-2.93-5.49-2.34-.9 4.42.66 5.98 2.21 2.21 8.06 7.67 3.56 10.5-5.15 3.52-8.34 2.63-13.1-1.16-5.62-4.47-11.23-8.94-16.85-13.41-7.84-6.24-13.53-13.65-12.52-24.29"
        />
        <Path
          id="Vector_6"
          fill="#FFBA78"
          d="M247.32 474.828c25.096 0 45.44-11.345 45.44-25.34 0-13.994-20.344-25.34-45.44-25.34-25.096 0-45.44 11.346-45.44 25.34 0 13.995 20.344 25.34 45.44 25.34Z"
        />
        <Path
          id="Vector_7"
          fill="#FED3AA"
          d="M60.88 520.097c-3.12.81-6.38-.65-9.1-2.27-5.93-3.53-11.18-8.03-15.4-13.21-.43-.53-.88-1.08-1.51-1.4-1.37-.68-3.15.02-4.14 1.13-2.65 2.95-1.05 7.33.65 10.82 2.04 4.16 3.18 6.5 5.21 10.66-8.95 4.99-18.37 9.31-28.5 11.82-1.82.45-3.68.92-5.21 1.94-1.52 1.01-2.66 2.69-2.38 4.39.39 2.4 3.41 3.8 6.08 3.69 2.67-.11 5.04-1.68 7.66-2.06 3.07-.45 8.41-1.69 10.7-.43-5.39 2.92-13.73 6.33-19.12 9.25-.66.36-1.34.73-1.73 1.33-.56.85-.4 1.93-.22 2.91.37 2.01.91 4.28 2.83 5.34 1.75.97 4 .56 5.95-.03 6.27-1.9 11.84-5.3 17.32-8.66.45-.27.96-.56 1.49-.48 3.5.53-2.21 4.93-2.67 5.32-3.08 2.63-6.08 5.35-9.01 8.15-1.74 1.66-3.59 3.86-2.96 6.18.45 1.63 2.08 2.72 3.75 2.98 1.67.26 3.37-.17 4.97-.7a36.991 36.991 0 0 0 14.73-9.21c1.08-1.11 2.14-2.32 3.58-2.93 5.49-2.34.9 4.42-.66 5.98-2.21 2.21-8.06 7.67-3.56 10.5 5.15 3.52 8.34 2.63 13.1-1.16 5.62-4.47 11.23-8.94 16.85-13.41 7.84-6.24 13.53-13.65 12.52-24.29"
        />
        <G id="Group">
          <Path
            id="Vector_8"
            fill="#FED3AA"
            d="M134.63 703.629c.71 2.07.32 4.15 1.03 6.22-5.75 4.18-11.5 8.35-17.24 12.53-4.51 3.28-9.5 7.71-8.15 12.71 1.39 5.15 8.479 7.08 14.449 7.88 27.65 3.72 56.1 2.8 83.36-2.71 2.72-.55 5.46-1.16 7.88-2.37 6.08-3.05 9.071-9.5 9.201-15.63.13-6.13-2.14-12.09-4.39-17.9"
          />
          <Path
            id="Vector_9"
            fill="#A86300"
            d="M164.19 729.19c-2.76 1.21-4.93 3.64-5.84 6.52-.97 3.1-.36 6.6 1.52 9.24.56.78 1.86.03 1.3-.76-1.66-2.33-2.23-5.33-1.37-8.08.8-2.54 2.73-4.56 5.15-5.62.88-.39.12-1.68-.76-1.3Z"
          />
          <Path
            id="Vector_10"
            fill="#A86300"
            d="M149.099 729.197c-2.55 1.71-4.88 3.92-5.99 6.85-1.05 2.77-.739 6.01 1.171 8.33.61.75 1.669-.32 1.059-1.06-1.61-1.96-1.649-4.82-.699-7.08 1.03-2.44 3.059-4.3 5.219-5.75.8-.54.05-1.84-.76-1.3v.01Z"
          />
          <Path
            id="Vector_11"
            fill="#A86300"
            d="M135.02 729.488c-1.18.31-2 1.18-2.69 2.14-.8 1.11-1.57 2.27-2.2 3.49-1.27 2.43-1.87 5.28-.62 7.85.42.87 1.72.11 1.3-.76-1.04-2.12-.34-4.56.72-6.52.55-1.01 1.19-1.97 1.84-2.92.52-.77 1.11-1.58 2.05-1.83.94-.25.54-1.69-.4-1.45Z"
          />
          <Path
            id="Vector_12"
            fill="#A86300"
            d="M120.41 728.719c-4.04 3.66-4.37 9.85-.91 14 .62.74 1.67-.32 1.06-1.06-2.96-3.55-2.49-8.8.91-11.88.72-.65-.35-1.71-1.06-1.06Z"
          />
        </G>
        <G id="Group_2">
          <Path
            id="Vector_13"
            fill="#FED3AA"
            d="M357.84 703.629c-.71 2.07-.32 4.15-1.03 6.22 5.75 4.18 11.5 8.35 17.24 12.53 4.51 3.28 9.5 7.71 8.15 12.71-1.39 5.15-8.48 7.08-14.45 7.88-27.65 3.72-56.1 2.8-83.36-2.71-2.72-.55-5.46-1.16-7.88-2.37-6.08-3.05-9.07-9.5-9.2-15.63-.13-6.13 2.14-12.09 4.39-17.9"
          />
          <Path
            id="Vector_14"
            fill="#A86300"
            d="M327.53 730.479c2.42 1.06 4.35 3.09 5.15 5.62.86 2.75.3 5.75-1.37 8.08-.56.79.74 1.54 1.3.76 1.88-2.63 2.49-6.14 1.52-9.24-.9-2.88-3.07-5.31-5.84-6.52-.88-.38-1.64.91-.76 1.3Z"
          />
          <Path
            id="Vector_15"
            fill="#A86300"
            d="M342.61 730.489c2.16 1.45 4.19 3.31 5.22 5.75.95 2.26.91 5.12-.7 7.08-.61.74.45 1.81 1.06 1.06 1.91-2.32 2.23-5.56 1.17-8.33-1.11-2.93-3.44-5.13-5.99-6.85-.8-.54-1.56.76-.76 1.3v-.01Z"
          />
          <Path
            id="Vector_16"
            fill="#A86300"
            d="M357.05 730.928c.94.25 1.53 1.06 2.05 1.83.65.95 1.29 1.91 1.84 2.92 1.06 1.96 1.76 4.4.72 6.52-.42.86.87 1.62 1.3.76 1.25-2.56.66-5.42-.62-7.85-.62-1.19-1.37-2.32-2.15-3.41-.7-.99-1.53-1.9-2.74-2.22-.93-.25-1.33 1.2-.4 1.45Z"
          />
          <Path
            id="Vector_17"
            fill="#A86300"
            d="M371 729.779c3.4 3.07 3.87 8.33.91 11.88-.61.74.44 1.8 1.06 1.06 3.46-4.15 3.13-10.34-.91-14-.71-.65-1.78.41-1.06 1.06Z"
          />
        </G>
        <Path
          id="Vector_18"
          fill="#A86300"
          d="M36.6 526.56c-.49 4.22-.29 8.84 2.01 12.55 1.97 3.18 5.45 5.54 9.28 5.39.96-.04.97-1.54 0-1.5-3.49.13-6.59-2.13-8.25-5.09-1.92-3.43-1.99-7.55-1.55-11.35.11-.96-1.39-.95-1.5 0h.01Z"
        />
        <Path
          id="Vector_19"
          fill="#A86300"
          d="M448.71 527.519c.34 3.64-.13 7.3-2.15 10.41-1.78 2.75-4.54 4.8-7.65 5.81-.91.3-.52 1.75.4 1.45 3.41-1.11 6.39-3.31 8.4-6.29 2.27-3.36 2.87-7.42 2.5-11.39-.09-.95-1.59-.96-1.5 0v.01Z"
        />
      </G>

      <Image
        id="quan"
        width={241.39}
        height={111.22}
        x={124}
        y={600}
        preserveAspectRatio="none"
        href={pants ? getImages(pants) : require('./quan1.webp')}
      />
      <Image
        id="ao"
        width={291.71}
        height={182.23}
        x={95}
        y={438}
        preserveAspectRatio="none"
        href={shirt ? getImages(shirt) : require('./ao3.webp')}
      />
      <Image
        id="giay"
        width={292.43}
        height={62.1}
        x={99}
        y={690}
        preserveAspectRatio="none"
        href={accessory ? getImages(accessory) : require('./giay2.webp')}
      />
      <Image
        id="face"
        width={393.28}
        height={270.28}
        x={49}
        y={190}
        preserveAspectRatio="none"
        href={face ? getImages(face) : require('./face1.webp')}
      />
      <G id="BBBB">
        {/*<Path*/}
        {/*  id="Vector_23"*/}
        {/*  fill="#FED3AA"*/}
        {/*  d="M247.055 459.543c14.067 0 25.47-3.362 25.47-7.51 0-4.147-11.403-7.51-25.47-7.51-14.067 0-25.47 3.363-25.47 7.51 0 4.148 11.403 7.51 25.47 7.51Z"*/}
        {/*/>*/}
        <G id="Group_3">
          <Path
            id="Toc"
            fill="#1E1D1B"
            d="M429.885 240.344c1.99-1.53 7.22.12 8.27-3.52 1.29-11.85-4.46-24-9.45-34.53-10.06-19.16-25.81-34.35-42.71-47.61-50.48-40.33-114.23-60.96-175.65-43.36-23.85 6.42-48.43 8.3-72.87 10.97-32.15 3.17-60.22 25.23-69.96 57.03-7.83 24.46-4.61 50.6-3.75 75.8.23 4.72 0 9.12-.65 13.2-3.87 24.17 5.25 49.19 19.92 68.38 3.14 4.11 6.72 8.48 10.47 11.97.59.53 1.44.85 2.17.59.85-.29 1.29-1.26 1.29-2.14-.03-10.24-1.35-20.62-1.11-30.95.21-9.12.38-20.21 5.37-28.25 1.53-2.02 3.49-1.09 5.34-.09 6.28 3.46 21.97 11.68 37.37 14.99 3.2.7 7.74 1.53 10.38.91 7.6-1.7 3.23-19.68 2.29-25.05-.41-1.97-.03-1.76 1.06-1.09 9.71 6.22 60.78 37.49 104.23 30.45 6.72-1.14 15.99-2.64 16.37-9.47.94-4.34 0-10-1.2-13.96-.67-2.41-1.53-4.58 1.5-2.58 10.3 7.13 33.91 17.81 46.32-.06 1.17-1.29 2.7-3.64 4.37-3.78 1.99-.23 4.87 2.43 6.78 3.96 7.1 5.9 14.96 11.5 22.82 13.9 4.78 1.58 10.03 1.99 14.67 4.08 10.62 4.37 11.85 19.92 10.77 30.8-.41 5.63-1.47 16.19 6.54 13.2 6.54-2.9 10.88-8.45 15.84-13.7 10.41-11.29 19.74-25.29 21.18-40.42 1.91-13.41 3.78-27.55 1.55-40.66-.21-2.58-1.5-7.07.21-8.83l.21-.18h.09-.03Z"
          />
        </G>
        <G id="Hat">
          <G id="Vector_43">
            <Path
              fill="#FFE28D"
              d="M142.718 61.028c-6.389 1.642-13.414.555-19.577-1.593-6.162-2.148-11.718-5.272-17.593-7.914-7.903-3.568-16.73-6.296-25.739-5.9-9.024.394-18.199 4.505-21.56 11.345-.636 1.308-1.06 2.703-1 4.11.182 3.544 3.422 6.606 3.65 10.15.287 4.542-4.452 8.308-9.448 10.32-4.996 2.012-10.644 3-15.247 5.593-7.615 4.284-10.977 12.518-9.614 19.975 1.363 7.457 6.859 14.099 13.793 19.087 19.577-25.815 49.782-46.173 84.56-56.988"
            />
            <Path
              stroke="#FF8431"
              strokeMiterlimit={10}
              strokeWidth={3}
              d="M142.718 61.028c-6.389 1.642-13.414.555-19.577-1.593-6.162-2.148-11.718-5.272-17.593-7.914-7.903-3.568-16.73-6.296-25.739-5.9-9.024.394-18.199 4.505-21.56 11.345-.636 1.308-1.06 2.703-1 4.11.182 3.544 3.422 6.606 3.65 10.15.287 4.542-4.452 8.308-9.448 10.32-4.996 2.012-10.644 3-15.247 5.593-7.615 4.284-10.977 12.518-9.614 19.975 1.363 7.457 6.859 14.099 13.793 19.087 19.577-25.815 49.782-46.173 84.56-56.988"
            />
          </G>
          <Path
            id="Vector_44"
            fill="#FF8431"
            d="M47.197 95.275c2.196.741 5.481 1.988 8.055 3.05a141.37 141.37 0 0 1 8.16 3.703c1.363.667 3.392.284 4.15-.889.756-1.172.363-2.666-1.09-3.382-5.648-2.79-11.553-5.186-17.655-7.247-1.468-.494-3.391.53-3.724 1.728-.41 1.445.545 2.506 2.12 3.037h-.016Z"
          />
          <Path
            id="Vector_45"
            fill="#FF8431"
            d="M81.626 65.24c4.618 1.148 8.782 3.284 12.188 6.198 3.362 2.864 5.754 6.37 6.965 10.246.409 1.297 2.165 2.075 3.725 1.729 1.559-.346 2.513-1.74 2.119-3.037-2.922-9.383-12.052-17.087-23.377-19.89-1.56-.382-3.3.47-3.724 1.73-.424 1.258.545 2.641 2.12 3.036l-.016-.012Z"
          />
          <Path
            id="Vector_46"
            fill="#FFAD0E"
            stroke="#C44F00"
            strokeMiterlimit={10}
            strokeWidth={2}
            d="M388.54 169.385c-18.713.802-37.472 2.148-56.11 4.173-1.545.16-3.059.345-4.588.518-46.587 5.321-92.312 15-134.418 31.371l-1.226.481a381.268 381.268 0 0 0-22.211 9.519c-32.34 15.111-61.607 34.592-86.24 57.407-7.844 7.259-16.14 15.333-28.57 16.037-7.147.408-14.066-2.815-16.504-8.296-2.362-5.272 1.362-8.827 3.967-12.667 5.375-7.951 5.526-8.778 1.135-17.111-7.66-14.951-7.918-10.605-12.9-28.704-5.314-19.383-5.041-39.457-4.738-59.321.166-10.716.348-21.543 3.754-31.877 6.375-19.333 23.665-35.345 44.06-46.456 16.775-9.149 35.61-15.346 54.808-20.556a668.773 668.773 0 0 1 11.885-3.111c.182-.05.394-.111.576-.148a683.322 683.322 0 0 1 12.385-3.05c4.224-1 8.479-1.975 12.733-2.876 1.696-.358 3.391-.716 5.087-1.062 8.04-1.63 16.155-3.012 24.346-4.062 25.648-3.271 51.75-3.074 77.701-2.864l6.541.074c10.038.16 20.107.74 29.554 3.432 28.071 8 43.333 32.037 56.338 53.84 6.299 10.555 13.052 21.123 21.939 30.42a94.866 94.866 0 0 0 3.164 3.185c.727.42 1.499.852 2.302 1.272 1.105.604 2.301 1.234 3.512 1.864 2.302 1.197 4.724 2.432 7.162 3.691 7.207 3.716 14.398 7.642 17.866 11.272 10.401 11.913-24.876 12.95-33.264 13.617l-.046-.012Z"
          />
          <Path
            id="Vector_47"
            fill="#FFE28D"
            d="M398.23 147.003c-9.327 10.494-31.69 7.111-44.726 14.568-8.175 4.679-16.79 8.778-25.678 12.494-46.587 5.321-92.312 15-134.418 31.37l-1.226.482c-1.529-.284-3.043-.667-4.466-1.21-4.21-1.568-7.631-4.235-10.811-6.963-41.47-35.617-55.535-90.58-36.292-136.976.182-.049.394-.11.576-.148a683.322 683.322 0 0 1 12.385-3.05 550.405 550.405 0 0 1 17.82-3.937c8.04-1.63 16.155-3.013 24.346-4.062 25.648-3.272 51.75-3.074 77.701-2.864l6.541.074c10.038.16 20.107.74 29.554 3.432 28.071 8 43.333 32.037 56.338 53.839 6.299 10.556 17.457 32.766 32.356 42.939v.012Z"
          />
          <Path
            id="Vector_48"
            fill="#FFE28D"
            d="M248.491 23.658c-18.804-5.913-40.546-4.679-59.32 1.284-9.024 2.864-17.775 6.963-23.347 13.185-3.028 3.383-5.027 7.272-6.541 11.21-4.118 10.667-5.011 22.075-2.574 33.05.333 1.494 37.11-2.087 40.683-2.482a389.868 389.868 0 0 0 40.985-6.852c13.158-2.925 26.557-6.333 38.942-10.9 11.249-4.161 5.178-13.038-.091-19.717-6.647-8.42-16.957-15.086-28.706-18.778h-.031Z"
          />
          <Path
            id="Vector_49"
            fill="#fff"
            stroke="#FF8431"
            strokeMiterlimit={10}
            strokeWidth={3}
            d="M219.497 105.987c25.913 0 46.92-17.129 46.92-38.259s-21.007-38.26-46.92-38.26c-25.914 0-46.921 17.13-46.921 38.26s21.007 38.259 46.921 38.259Z"
          />
          <Path
            id="Vector_50"
            fill="#FFAD0E"
            d="M298.213 74.498c-3.346 22.68 5.905 50.173 29.918 60.593 16.291 7.346 38.851 5.827 51.024-7.235 10.795-11.679 10.916-28.444 7.313-44.827-4.316-18.704-13.96-35.704-32.825-43.05-27.813-10.42-52.386 8.766-55.369 34.124l-.061.395Z"
          />
          <Path
            id="Vector_51"
            fill="#BF4700"
            d="M351.583 104.878c9.871 11.605 22.756 4.556 25.057-5.63 2.408-8.962.924-19.308-5.163-27.32-3.739-5.37-12.491-9.025-18.577-3.754-2.407 1.902-4.33 4.84-5.542 8.26-2.997 8.716-2.331 20.222 3.937 28.099l.288.345Z"
          />
          <G id="Group_6">
            <Path
              id="Vector_52"
              fill="#000"
              d="M234.244 99.856c18.028 0 32.643-11.917 32.643-26.618 0-14.7-14.615-26.617-32.643-26.617-18.029 0-32.643 11.917-32.643 26.617 0 14.7 14.614 26.618 32.643 26.618Z"
            />
            <Path
              id="Vector_53"
              fill="#fff"
              d="M199.783 66.821c6.208 2.63 12.4 5.26 18.608 7.889.151.309-.303.568-.666.691a121.566 121.566 0 0 1-20.833 5.111l2.907-13.703-.016.012Z"
            />
          </G>
          <Path
            id="Vector_54"
            fill="#FFAD0E"
            stroke="#C44F00"
            strokeMiterlimit={10}
            strokeWidth={2}
            d="M36.87 275.68c3.029-15.654 23.908-23.58 40.017-30.877a291.041 291.041 0 0 0 19.47-9.691c7.647-4.148 15.066-8.556 22.484-12.988 71.267-47.086 164.79-63.704 255.346-77.877 14.217-1.913 31.477-4.679 45.346-.086 6.828 2.259 12.702 7.395 12.46 13.235-.621 14.864-27.48 14.555-40.834 15.284-78.231 4.321-145.137 28.95-212.452 58.432-13.308 5.827-31.765 16.704-44.77 22.852a298.364 298.364 0 0 0-40.305 23c-6.692 4.531-13.248 9.259-20.803 12.901-8.085 3.84-18.138 6.938-26.798 3.136-6.995-3.136-10.326-10.716-9.19-17.124l.03-.197Z"
          />
          <G id="Group_7">
            <Path
              id="Vector_55"
              fill="#FFE28D"
              d="M53.344 156.633c-10.674 8.21-13.111 23.692 1.393 29.951-6.54 3.482-11.083 9.766-10.537 16.309.5 8.136 9.084 15.062 18.85 16.024.106.05.06.099-.106.186-1.47.543-3.165 1.173-4.437 2.037-1.665 1.049-2.77 2.716-2.71 4.444.06 5.296 7.056 8.259 13.021 7.593 9.66-.753 17.911-5.309 24.997-10.395 14.005-10.173 25.8-23.185 30.009-38.136.318-1.21-2.408-1.531-3.346-2.173-.909-.198-3.407-2.037-3.801-.901-3.437 7.666-16.866 10.963-20.833 2.024-.908-1.975-.484-4.185.908-5.839a3.651 3.651 0 0 1-.12-.222c-4.119-.84-8.858-1.482-11.826-4.161-1.559-1.654-2.377-3.889-4.875-4.975-8.221-4.445-17.926-7.543-26.405-11.778h-.166l-.016.012Z"
            />
            <Path
              id="Vector_56"
              fill="#FF8431"
              d="M80.143 166.007c-4.33 1.79-8.827-3.457-11.265-5.543-3.664-3.136-8.145-5.148-13.58-4.457-4.664.593-8.707 3.074-11.371 6.185-3.029 3.544-3.558 8.025-2.347 12.161 1.453 4.988 5.435 9.395 10.16 12.667v-3.494c-6.784 4.802-10.039 12.321-8.631 19.531 1.317 6.753 7.116 12.839 14.701 15.703-.363-1.123-.726-2.247-1.09-3.382-2.558 3.061-4.754 6.79-4.406 10.592.303 3.358 2.65 6.408 6.33 7.963 6.979 2.951 15.473 1.222 22.407-.901 15.292-4.679 28.343-13.593 37.019-24.84 4.89-6.333 8.327-13.444 9.902-20.814.287-1.309-.439-2.655-2.12-3.037-1.453-.334-3.437.407-3.725 1.728-2.437 11.37-9.356 21.79-19.425 29.889-5.011 4.037-10.734 7.42-17.003 10.049-5.753 2.408-12.52 4.679-19.092 4.531-2.892-.062-6.283-.728-7.676-3.037-1.923-3.197.833-7.025 3.013-9.629.863-1.025.333-2.84-1.09-3.383-6.117-2.309-10.66-6.84-11.825-12.309-1.211-5.667 1.756-11.457 6.995-15.173 1.166-.827 1.18-2.666 0-3.494-4.497-3.111-8.615-7.469-8.979-12.456-.302-4.099 2.332-8.037 7.071-9.692 5.663-1.987 9.327 1.556 12.855 4.692 4.118 3.666 10.159 6.716 16.23 4.197 3.483-1.444.409-5.704-3.058-4.259v.012Z"
            />
            <Path
              id="Vector_57"
              fill="#FF8431"
              d="M99.825 169.992c-3.467 2.173-7.298 4.691-8.115 8.42-.863 3.913 1.665 7.716 5.708 9.79 4.315 2.21 9.871 1.914 14.398.333 4.527-1.58 8.403-4.666 11.719-7.79l-3.664.383c.969.543 1.454 1.667 1.938 2.494.666 1.136 1.363 2.259 2.423 3.197 2.543 2.222 6.54 2.605 10.023 3.05 7.252.913 15.019 1.012 21.393-2.371 3.255-1.728.227-6-3.058-4.259-5.33 2.827-11.901 2.556-17.942 1.716-2.831-.395-5.541-.506-6.995-2.741-1.332-2.061-2.301-3.963-4.754-5.333-1.029-.58-2.846-.395-3.664.383-4.057 3.839-10.462 9.333-17.472 7.222-2.377-.716-4.587-2.765-4.209-4.951.394-2.259 3.24-3.975 5.299-5.259 1.287-.815 1.984-2.123 1.09-3.383-.741-1.037-2.831-1.703-4.148-.889l.03-.012Z"
            />
            <Path
              id="Vector_58"
              fill="#FF8431"
              d="M82.52 159.102c-4.028 2.679-3.165 7.457-.94 10.728 2.226 3.272 5.043 6.679 9.463 8 1.484.445 3.377-.494 3.725-1.728.394-1.395-.515-2.568-2.12-3.037-.514-.161.122.099-.151-.05-.151-.074-.318-.135-.47-.209a18.819 18.819 0 0 1-.56-.297c.243.136.167.111-.03-.024a13.408 13.408 0 0 1-1.695-1.47c.196.198.09.099-.03-.049-.137-.148-.273-.309-.41-.469-.242-.284-.469-.556-.711-.84-.5-.617-1.015-1.234-1.469-1.876-.212-.284-.409-.58-.59-.877 0 .013-.349-.58-.243-.382.106.185-.106-.235-.136-.284a9.225 9.225 0 0 1-.378-1.074c-.197-.593 0 .296-.03-.05 0-.136-.03-.271-.03-.407v-.519c0-.284.03-.173-.031.05.076-.297.181-.593.288-.889.106-.297-.319.444.045-.05.076-.111.182-.222.257-.345-.106.173-.257.222-.015.037.182-.148.348-.272.545-.408 1.227-.815 1.12-2.654 0-3.494-1.302-.975-2.967-.876-4.285 0v.013Z"
            />
            <Path
              id="Vector_59"
              fill="#FF8431"
              d="M108.654 187.6c-5.178 9.938-14.55 18.037-26.466 22.765-1.469.58-1.862 2.284-1.09 3.383.878 1.222 2.665 1.469 4.148.889 12.718-5.05 23.105-13.926 28.646-24.531.636-1.222.455-2.642-1.09-3.383-1.272-.605-3.497-.346-4.148.889v-.012Z"
            />
            <Path
              id="Vector_60"
              fill="#FF8431"
              d="M93.723 180.08c-7.721 2.791-16.245 3.803-24.633 3.186-1.636-.124-3.029 1.222-3.029 2.469 0 1.432 1.393 2.345 3.029 2.469 9.508.704 19.001-.704 27.692-3.852 1.499-.543 1.847-2.321 1.09-3.383-.894-1.259-2.65-1.432-4.149-.889Z"
            />
          </G>
          <Path
            id="Vector_61"
            fill="#fff"
            d="M245.357 67.791c4.624 0 8.373-3.057 8.373-6.827s-3.749-6.827-8.373-6.827c-4.624 0-8.373 3.056-8.373 6.827 0 3.77 3.749 6.827 8.373 6.827Z"
          />
          <Path
            id="Vector_62"
            fill="#FF8431"
            d="M353.218 39.114c1.999-4.901.742-10.37-3.209-14.383-3.937-4-10.129-5.95-16.004-7.284-6.056-1.37-13.006-2.247-17.624-6.037-1.786-1.469-3.255-3.383-3.391-5.506-.015-.309 0-.605 0-.914.151-.37.045-.296-.303.223.621.061 1.56-.223 2.226-.235 1.695-.025 3.27.309 4.83.827 1.559.519 3.331-.543 3.724-1.728.47-1.408-.59-2.531-2.12-3.037-4.496-1.494-13.338-2.074-14.307 3.173-.788 4.271 2.059 8.543 5.874 11.32 4.315 3.124 9.842 4.58 15.323 5.766 5.056 1.087 10.492 2.037 14.746 4.68 4.497 2.802 6.147 7.456 4.376 11.802-1.226 3.024 4.618 4.32 5.844 1.308l.015.025Z"
          />
        </G>
      </G>
    </G>
  </Svg>
);
export default SvgCharacter;
