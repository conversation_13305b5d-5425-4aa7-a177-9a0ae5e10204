<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="256" height="256" viewBox="0 0 256 256" xml:space="preserve">
<g style="stroke: none; stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;" transform="translate(1.4065934065934016 1.4065934065934016) scale(2.81 2.81)">
	<path d="M 39.871 16.76 C 39.939 11.936 36.928 6.113 33.023 0 c 8.181 -0.069 16.928 7.395 17.026 21.104 l -6.117 6.015 L 39.871 16.76 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(149,196,78); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 52.904 33.339 l 6.627 -17.026 c -8.967 1.786 -15.187 7.832 -17.995 19.167 L 52.904 33.339 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(170,217,98); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 37.611 31.841 l -8.768 -19.81 c 8.076 1.694 14.174 5.573 17.206 12.84 l 1.852 8.06 l -9.673 0.408 L 37.611 31.841 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(170,217,98); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 53.765 31.884 c 0.002 -0.014 0.005 -0.029 0.007 -0.043 h -2.055 h -5.667 h -2.031 h -5.435 h -2.03 c 0.001 0.009 0.003 0.018 0.004 0.027 c -8.258 0.286 -14.894 7.076 -14.894 15.403 v 19.808 C 21.663 79.718 31.946 90 44.585 90 h 0.83 c 12.639 0 22.922 -10.282 22.922 -22.921 V 47.271 C 68.337 39.053 61.873 32.334 53.765 31.884 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(255,222,85); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 50.418 45.858 c -0.228 0 -0.457 -0.077 -0.647 -0.234 L 45 41.682 l -4.77 3.942 c -0.433 0.358 -1.075 0.297 -1.434 -0.136 c -0.358 -0.433 -0.297 -1.075 0.136 -1.433 l 5.419 -4.479 c 0.377 -0.311 0.921 -0.311 1.298 0 l 5.419 4.479 c 0.434 0.358 0.495 1 0.136 1.433 C 51.002 45.732 50.711 45.858 50.418 45.858 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(255,169,65); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 48.919 55.196 c -0.293 0 -0.585 -0.126 -0.786 -0.37 c -0.359 -0.433 -0.297 -1.075 0.136 -1.433 l 5.419 -4.479 c 0.376 -0.312 0.921 -0.312 1.297 0 l 5.418 4.479 c 0.434 0.358 0.495 1 0.136 1.433 c -0.357 0.433 -1.001 0.495 -1.433 0.136 l -4.77 -3.942 l -4.771 3.942 C 49.377 55.119 49.147 55.196 48.919 55.196 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(255,169,65); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 41.491 55.196 c -0.229 0 -0.458 -0.077 -0.648 -0.234 l -4.77 -3.942 l -4.77 3.942 c -0.434 0.359 -1.075 0.297 -1.433 -0.136 c -0.358 -0.433 -0.298 -1.075 0.136 -1.433 l 5.419 -4.479 c 0.377 -0.312 0.921 -0.312 1.298 0 l 5.419 4.479 c 0.434 0.358 0.494 1 0.136 1.433 C 42.075 55.069 41.784 55.196 41.491 55.196 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(255,169,65); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 39.582 65.57 c -0.293 0 -0.584 -0.126 -0.785 -0.37 c -0.358 -0.433 -0.298 -1.075 0.136 -1.433 l 5.419 -4.479 c 0.377 -0.312 0.921 -0.312 1.298 0 l 5.419 4.479 c 0.434 0.358 0.495 1 0.136 1.433 c -0.358 0.433 -0.998 0.495 -1.433 0.136 L 45 61.394 l -4.77 3.942 C 40.04 65.494 39.81 65.57 39.582 65.57 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(255,169,65); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 48.919 75.945 c -0.293 0 -0.585 -0.126 -0.786 -0.37 c -0.359 -0.433 -0.297 -1.075 0.136 -1.433 l 5.419 -4.479 c 0.376 -0.312 0.921 -0.312 1.297 0 l 5.418 4.479 c 0.434 0.358 0.495 1 0.136 1.433 c -0.357 0.433 -1.001 0.495 -1.433 0.136 l -4.77 -3.942 l -4.771 3.942 C 49.377 75.869 49.147 75.945 48.919 75.945 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(255,169,65); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 41.491 75.945 c -0.229 0 -0.458 -0.077 -0.648 -0.234 l -4.77 -3.942 l -4.77 3.942 c -0.434 0.358 -1.075 0.297 -1.433 -0.136 c -0.358 -0.433 -0.298 -1.075 0.136 -1.433 l 5.419 -4.479 c 0.377 -0.312 0.921 -0.312 1.298 0 l 5.419 4.479 c 0.434 0.358 0.494 1 0.136 1.433 C 42.075 75.819 41.784 75.945 41.491 75.945 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(255,169,65); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 39.582 85.283 c -0.293 0 -0.584 -0.126 -0.785 -0.37 c -0.358 -0.433 -0.298 -1.075 0.136 -1.433 l 5.419 -4.479 c 0.377 -0.312 0.921 -0.312 1.298 0 l 5.419 4.479 c 0.434 0.358 0.495 1 0.136 1.433 c -0.358 0.433 -0.998 0.495 -1.433 0.136 L 45 81.107 l -4.77 3.942 C 40.04 85.206 39.81 85.283 39.582 85.283 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(255,169,65); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 68.337 61.908 l -3.169 -2.62 c -0.376 -0.312 -0.921 -0.312 -1.297 0 l -5.419 4.479 c -0.434 0.358 -0.495 1 -0.136 1.433 c 0.201 0.244 0.492 0.37 0.786 0.37 c 0.228 0 0.457 -0.077 0.647 -0.234 l 4.771 -3.942 l 3.817 3.155 V 61.908 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(255,169,65); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 68.122 44.803 c -0.194 -1.199 -0.515 -2.354 -0.97 -3.443 l -2.158 -1.784 c -0.377 -0.311 -0.921 -0.311 -1.298 0 l -5.419 4.479 c -0.434 0.358 -0.494 1 -0.136 1.433 c 0.359 0.434 1.001 0.494 1.434 0.136 l 4.77 -3.942 L 68.122 44.803 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(255,169,65); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 31.072 45.858 c 0.293 0 0.585 -0.126 0.786 -0.37 c 0.359 -0.433 0.297 -1.075 -0.136 -1.433 l -5.419 -4.479 c -0.377 -0.311 -0.921 -0.311 -1.298 0 l -2.163 1.788 c -0.453 1.087 -0.77 2.242 -0.963 3.438 l 3.775 -3.12 l 4.771 3.942 C 30.615 45.781 30.845 45.858 31.072 45.858 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(255,169,65); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 21.663 64.693 l 3.991 -3.299 l 4.771 3.942 c 0.435 0.359 1.075 0.296 1.433 -0.136 c 0.359 -0.433 0.297 -1.075 -0.136 -1.433 l -5.419 -4.479 c -0.377 -0.312 -0.921 -0.312 -1.298 0 l -3.342 2.763 V 64.693 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(255,169,65); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
</g>
</svg>