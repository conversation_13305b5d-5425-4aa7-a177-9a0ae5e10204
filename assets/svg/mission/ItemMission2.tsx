import * as React from 'react';
import Svg, {ClipPath, Defs, G, Image, Path, SvgProps} from 'react-native-svg';

import {Dimensions, View} from 'react-native';
import BtnMission from './BtnMission.tsx';

const {width, height} = Dimensions.get('screen');
const dataBtn = [
  {id: 'b7', x: 218, y: 935},
  {id: 'b8', x: 348, y: 1449},
  {id: 'b9', x: 208, y: 1963},
  {id: 'b10', x: 754, y: 2020},
  {id: 'b11', x: 792, y: 1220},
  {id: 'b12', x: 504, y: 762},
  {id: 'b13', x: 779, y: 416},
];

interface ItemMission2 extends SvgProps {
  data: Array<any>;
  onItemPress: (item: any, index: number) => void;
  active: number;
}

const ItemMission2: React.FC<ItemMission2> = ({onItemPress, data, active}) => {
  return (
    <View style={{width, height}}>
      <Svg
        width={'100%'}
        height={'100%'}
        viewBox="0 0 1179 2556"
        preserveAspectRatio="none"
        fill="none">
        <G id="m2" clipPath="url(#clip0_392_540)">
          <Image
            x={0}
            y={0}
            id="image0_206_1003"
            width={1179}
            height={2556}
            preserveAspectRatio="none"
            href={require('./bgItem2.webp')}
          />
          <G id="Group">
            <Path
              id="Vector"
              fill="#FDDE5F"
              d="M1002.92 2058.08c20.37-31.13 27.01-95.74 19.71-132.21-1.74-8.67-12 14.46-20.84 14.31-31.63-.54-66.52-7.45-97.76-2.44.75 74.79-44.76 143.1-68.75 210.06-7.29 20.36-28.19 33.38-49.45 37.35-21.26 3.97-43.13.47-64.47-3.03-86.67-14.22-173.33-28.45-260-42.67-38.48-6.32-77.49-12.79-112.98-28.94-51.14-23.28-92.73-66.81-113.65-118.97-9.22-23-14.54-48.59-8.44-72.61 6.3-24.83 24.02-45.18 43.23-62.13 56.56-49.92 129.47-78.43 185.92-128.47 98.98-87.72 132.63-227.04 147.81-358.42 5.29-45.82 7.24-97.53-23.29-132.11-12.24-13.86-28.53-23.37-44.52-32.65-24.2-14.04-48.41-28.08-72.61-42.12-26.47-15.35-55.2-33.52-63.41-63-5.36-19.25-.76-39.85-3.21-59.69-5.54-44.87-44.99-77.1-83.21-101.25C197.22 865.94 66.05 821.51-63.72 777.74c-137.35-46.33-282.18-93.28-424.65-66.58-35.27 6.61-71.33 18.7-95.75 44.99-39.31 42.32-36.55 112.2-5.47 160.88 31.08 48.68 84.33 79.04 138.79 98.29 35.71 12.62 74.74 22.47 100.5 50.22 17.18 18.5.15 82.35 3.97 107.3 6.25 40.82 29.08 71.43 58.16 100.75 42.69 43.04 96.6 60.68 110.51 119.68 3.95 16.75 5.98 36.26-5.07 49.46-14.48 17.3-43.62 14.47-59.35 30.64-7.61 7.82-10.79 18.86-16.87 27.91-12.09 18.02-34.84 26.51-56.53 26.87-21.69.36-42.84-6.21-63.54-12.72-29.96-9.42-61.77-20.07-81.19-44.75-5.92-7.52-10.44-16.09-16.5-23.51-27.09-33.17-76.69-35.67-112.86-58.61-36.1-22.88-56-64.46-67.18-105.71-11.18-41.25-15.57-84.34-30.08-124.55-14.5-40.21-41.9-78.93-82.46-92.41-5.74-1.91-11.64-3.28-17.57-4.49-39.39-8.06-80.89-9.14-119.31 2.67s-83.41 37.64-101.39 73.6c-13.32 26.65-22.37 94.81-22.15 124.61.95 126.49 80.31 209.85 172.58 296.38 24.95 23.4 52.92 45.92 65.67 77.65 4.68 11.64 7.08 24.07 11.39 35.86 17.47 47.84 40.95 105.14 80.98 136.64 40.02 31.5 101.9 46.92 100.26 97.82-.45 13.94-17.31 20.32-31.04 22.79-163.37 29.42-270.35-145.44-421.25-133.17-.19.01-.38.02-.54.04-.03 0-.06 0-.09.01-.11.01-.22.02-.31.04-31.73 5.26-62.81 11.82-95.15 11.74-10.58-.02-21.17-.57-31.7-1.64v135.31c25.2 2.07 51.15 3.07 76.31 1.68 20.79-1.14 41.44-4.76 61.37-10.76 1.21-.36 2.35-.74 3.45-1.15 11.03-4.04 28.11-1.13 39.35.76 11.33 1.91 22.93 5.85 33.78 9.68 33.43 11.8 55.07 38.9 81.58 60.47 26.12 21.24 57.89 34.79 90.41 42.74 36.94 9.03 75.06 13.43 113.09 12.57 71.24-1.62 146.16-22.23 205.05-62.96 27.8-19.23 62.52-43.01 64.27-80.28 1.03-21.85-9.9-42.71-24.07-59.37-12.16-14.31-26.77-26.31-41.28-38.23-45.31-37.2-90.62-74.41-135.94-111.61-14.17-11.64-29.17-24.53-33.54-42.34-2.17-8.86-1.47-18.14-2.43-27.21-2.68-25.31-17.95-47.26-32.74-67.97-30.23-42.34-60.6-84.85-96.86-122.16-13.76-14.16-28.33-27.52-42.17-41.6-35.49-36.09-60.87-82.02-72.54-131.28-5.69-24.01-8.06-49.8.49-72.95 8.55-23.15 30.29-42.73 54.97-42.43 29.93.36 52.9 28.91 59.6 58.08 6.7 29.17 1.74 59.56 1.33 89.49-.55 41.02 8.51 83.91 34.98 115.26 49.98 59.2 138.62 67.79 187.96 127.52 68.77 83.25 166.85 124.73 272.79 103.81 22.17-4.38 70.03-18.81 87.97-32.54 17.94-13.73 31.17-35.03 30.79-57.62-.14-8.61-.93-19.66 6.9-23.25 42.17-19.31 95.97-26.75 118.09-67.51 9.19-16.93 10.85-36.95 9.87-56.19-3.13-61.18-32.33-120.6-78.84-160.47-22.22-19.04-48.12-33.78-68.45-54.84-20.33-21.06-34.76-51.4-26.39-79.44 4.15-13.9 13.39-25.67 19.33-38.9 13.26-29.56 8.19-66.47-12.56-91.36-20.65-24.77-53.26-36.14-84.98-41.94-31.72-5.8-64.43-7.22-95.06-17.31-40.9-13.48-76.4-42.66-97.53-80.17-4.28-7.6-8.1-16.22-6.48-24.8 4.41-23.41 34.72-29.61 58.55-29.61 111.89.02 223.72 22.72 326.77 66.32 28.34 11.99 56 25.53 83.63 39.06 96.29 47.15 192.58 94.29 288.88 141.44 16.92 8.29 21.88 30.44 19.73 49.16-2.15 18.72-9.28 36.94-8.38 55.77 1.58 33.12 27.87 60.33 57.03 76.12 29.16 15.79 62.05 23.36 92.14 37.29 30.09 13.94 58.99 36.9 66.71 69.15 6.4 26.75-2.87 54.48-12.03 80.41-30.66 86.77-62.97 176.02-125.12 243.9-32.17 35.13-71.24 63.23-111.43 88.81-37.33 23.76-76.18 45.81-108.78 75.73-32.6 29.92-58.98 69.3-63.15 113.35-3.53 37.22 15.49 73.07 38.99 102.14 23.51 29.08 52.04 54.06 73.13 84.94 11.89 17.41 21.31 36.55 34.68 52.85 26.03 31.75 65.27 50.33 105.3 59.46 40.03 9.14 81.42 9.76 122.44 11.52 90.67 3.9 181.1 13.59 270.54 28.98 61.87 10.65 126.98 23.8 186.08 2.61 21.78-7.81 43.38-21.93 50.63-43.9 10.78-32.67-13.9-67.7-8.52-101.68 4.35-27.45 26.93-47.71 42.15-70.97l-.01.06Z"
            />
            <Path
              id="Vector_2"
              fill="#FDDE5F"
              d="M901.02 1966c11.59-48.54-9.04-98.09-22.77-146.07a539.37 539.37 0 0 1-18.58-99.35c-.97-10.61-1.62-21.26-1.4-31.91 1.31-62.18 32.14-125.12 13.37-184.41-8.01-25.3-20.86-46.57-39.02-65.04-17.72-18.04-29.89-42.28-30.26-67.87-.27-18.98 6.7-38.08 18.95-52.56 13.54-16 35.61-18.17 48.8-33.28 12.78-14.64 7.4-38.56-5.67-52.93-13.07-14.37-31.69-22.07-48.81-31.26-45.98-24.71-79.72-61.23-114.45-99.23-34.41-37.65-68.83-75.29-103.24-112.951-29.82-32.63-64.46-59.65-84.55-100.4-18.4-37.32-22.63-80.13-21.23-121.72.25-7.56.68-15.12 1.36-22.66 2.96-32.84 22.99-62.57 49.24-82.53 23.99-18.24 52.66-29.08 81.04-39.23 57.14-20.44 115.13-39.15 174.79-50.24 20.35-3.78 40.85-6.67 61.34-9.53 173.37-24.18 352.87-46.08 521.09 2.35 23.8 6.85 47.49 15.22 68.15 28.89 9.44 6.25 18.16 13.55 26.33 21.39 17.04 16.35 32.32 35.48 53.52 45.89 16.54 8.11 35.36 10.19 53.77 10.74 42.27 1.25 84.81-4.91 126.75.5 41.95 5.4 85.42 25.47 103.64 63.63 7.84 16.43 11.98 36.98 28.06 45.52 5.39 2.86 12.72 5.3 13.2 11.39.12 1.58-.3 3.15-.72 4.68a398.162 398.162 0 0 1-27.88 72.02c-2.42 4.8-5.09 9.73-9.46 12.86-3.29 2.35-7.28 3.48-11.24 4.29-31.62 6.47-66.43-6.72-85.83-32.51-9.11-12.11-14.97-26.56-25.17-37.76-15.68-17.21-39.68-24.52-62.88-26.38-23.21-1.86-46.54.97-69.82.34-45.32-1.22-90.19-15.89-127.48-41.67-20.4-14.1-38.51-31.38-59.41-44.72-63.07-40.25-138.99-53.6-212.51-53.93a1870.753 1870.753 0 0 0-339.11 29.47c-28 5.03-56.24 8.27-81.1 23.5-20.82 12.75-44.49 21.08-64.78 35.29-19.06 13.35-35.56 31.53-42.68 53.68-10.69 33.22 5.11 52.78 23.49 77.46 22.53 30.26 45.79 59.97 69.76 89.1 22.04 26.8 44.74 53.161 70 76.941 44.33 41.74 96 75.02 141.05 116 45.05 40.97 84.52 92.49 93.54 152.73 3.46 23.11-12.78 50.17-32.82 61.89-14.48 8.47-33 11.77-42.8 25.38-9.29 12.9-7.14 31.37.97 45.04 8.48 14.32 23.37 21.51 32.92 34.39 7.76 10.47 12.98 23.93 15.58 36.59 6.16 29.94 2.05 61.61-2.3 91.51-3.45 23.76-14.18 45.84-19.47 69.26-20.7 91.59 43.6 182.79 38.56 276.56-1.64 30.57-10.65 60.21-19.71 89.46L901.02 1966Z"
            />
            <G id="Group_2">
              <Path
                id="Vector_3"
                fill="#D66112"
                d="M427.82 1207.79c-21.77-5.73-44.85-12.45-61.3-28.7-18.03-17.81-23.26-43.82-12.69-66.82 5.37-11.68 12.54-22.55 16.93-34.67 4.16-11.48 3.94-24.3.75-35.99-1.43-5.26-3.48-10.37-5.95-15.23-2.81-5.54-7.71-10.06-12.18-14.31-8.1-7.72-17.12-14.51-26.63-20.4-10.98-6.81-22.68-12.38-34.66-17.18-5.99-2.4-12.08-4.76-18.26-6.62-6.99-2.11-15.83-2.58-21.86-7.01-4.67-3.43-9.17 4.38-4.54 7.77 4.63 3.39 11.42 4.86 17.06 6.27 6.3 1.57 12.31 3.39 18.42 5.66 11.17 4.15 22.07 9.01 32.46 14.85s20.42 12.76 29.32 20.86c3.86 3.51 8.01 7.21 11.17 11.39 4.08 5.39 6.37 13.13 7.77 19.64 2.94 13.56.03 25.76-6.05 37.96-5.69 11.42-12.88 22.5-16.36 34.88-6.22 22.11 1.37 46.5 16.77 63.08 17.58 18.93 43.2 26.87 67.44 33.25 5.61 1.48 8-7.2 2.39-8.68Z"
              />
              <Path
                id="Vector_4"
                fill="#D66112"
                d="M509.33 1237.37c-13.26-7.59-27.61-12.81-42.09-17.54-5.52-1.8-7.88 6.88-2.39 8.68 13.76 4.5 27.33 9.41 39.94 16.63 5.03 2.88 9.57-4.89 4.54-7.77Z"
              />
              <Path
                id="Vector_5"
                fill="#D66112"
                d="M560.5 1318.69c-1.47-20.02-8.6-38.61-20.96-54.43-3.56-4.56-9.89 1.85-6.36 6.36 10.77 13.79 17.04 30.65 18.32 48.07.42 5.74 9.43 5.79 9 0Z"
              />
              <Path
                id="Vector_6"
                fill="#D66112"
                d="M555.26 1395.76c1.39-13.41 2.5-26.84 3.5-40.29.43-5.78-8.57-5.75-9 0-1 13.44-2.11 26.88-3.5 40.29-.6 5.76 8.41 5.71 9 0Z"
              />
              <Path
                id="Vector_7"
                fill="#D66112"
                d="M542.77 1468.66c2.75-11.4 5.25-22.85 7.45-34.36 1.08-5.66-7.59-8.08-8.68-2.39-2.2 11.52-4.7 22.97-7.45 34.36-1.36 5.63 7.32 8.03 8.68 2.39Z"
              />
              <Path
                id="Vector_8"
                fill="#D66112"
                d="M527.07 1524.79c4.17-13.39 8.1-26.85 11.7-40.4 1.49-5.6-7.19-7.99-8.68-2.39-3.6 13.55-7.53 27.01-11.7 40.4-1.73 5.54 6.96 7.91 8.68 2.39Z"
              />
              <Path
                id="Vector_9"
                fill="#D66112"
                d="M497.96 1587.97c10.12-14.67 17.25-30.97 23.52-47.59 2.05-5.43-6.65-7.77-8.68-2.39-6 15.91-12.93 31.4-22.61 45.44-3.3 4.78 4.5 9.29 7.77 4.54Z"
              />
              <Path
                id="Vector_10"
                fill="#D66112"
                d="M461.77 1648.05c9.98-14.77 19.89-29.67 26.85-46.14 2.23-5.27-5.52-9.87-7.77-4.54-6.96 16.48-16.87 31.38-26.85 46.14-3.25 4.81 4.54 9.32 7.77 4.54Z"
              />
              <Path
                id="Vector_11"
                fill="#D66112"
                d="M412.33 1705.11c13.14-13.72 26.09-27.61 38.09-42.35 3.63-4.45-2.7-10.86-6.36-6.36-12 14.74-24.95 28.63-38.09 42.35-4.01 4.18 2.35 10.56 6.36 6.36Z"
              />
              <Path
                id="Vector_12"
                fill="#D66112"
                d="M360.95 1747.65c3.26-2.53 7.12-4.16 10.32-6.77 2.84-2.31 5.88-3.83 8.8-5.98 6.88-5.06 12.99-11.15 19.04-17.15 4.12-4.08-2.25-10.45-6.36-6.36-5.93 5.88-12.3 13.32-19.68 17.35-2.99 1.63-5.22 3.4-7.89 5.48-3.35 2.6-7.22 4.46-10.59 7.08-4.57 3.54 1.84 9.87 6.36 6.36v-.01Z"
              />
              <Path
                id="Vector_13"
                fill="#D66112"
                d="M294.6 1789.82c5.37-4.04 10.12-8.81 15.47-12.86 5.35-4.05 11.89-7.24 18.18-10.24 5.23-2.49.66-10.25-4.54-7.77-6.2 2.96-12.39 5.95-17.94 10.05-5.55 4.1-10.27 8.95-15.71 13.05-4.57 3.44-.09 11.26 4.54 7.77Z"
              />
              <Path
                id="Vector_14"
                fill="#D66112"
                d="M199.99 1833.73c18.51-10.92 39.14-17.25 58.76-25.77 5.3-2.3.72-10.06-4.54-7.77-19.63 8.53-40.24 14.85-58.76 25.77-4.99 2.94-.46 10.72 4.54 7.77Z"
              />
              <Path
                id="Vector_15"
                fill="#D66112"
                d="M150.96 1900.58c2.94-13.45 8.38-25.85 16.35-37.08 3.36-4.73-4.44-9.23-7.77-4.54-8.37 11.8-14.16 25.11-17.25 39.23-1.24 5.65 7.44 8.06 8.68 2.39h-.01Z"
              />
              <Path
                id="Vector_16"
                fill="#D66112"
                d="M163.5 1980.83c-1.13-6.37-4.57-12.33-6.98-18.28-2.82-6.97-5.32-14.15-6.18-21.65-.65-5.69-9.66-5.75-9 0 .89 7.78 2.97 15.11 5.84 22.39 2.57 6.53 6.41 13.01 7.64 19.94 1.01 5.69 9.69 3.28 8.68-2.39v-.01Z"
              />
              <Path
                id="Vector_17"
                fill="#D66112"
                d="M215.31 2045.33c-5.49-5.94-11.49-11.3-17.11-17.08-4.97-5.11-7.51-12-12.69-17.01-4.17-4.03-10.54 2.33-6.36 6.36 5.14 4.97 7.7 11.88 12.69 17.01 5.63 5.78 11.63 11.15 17.11 17.08 3.94 4.26 10.29-2.11 6.36-6.36Z"
              />
              <Path
                id="Vector_18"
                fill="#D66112"
                d="M385.07 2183.74c-26.05-3.87-51.57-12.1-74.11-25.88-11.41-6.98-21.91-15.36-30.99-25.2-9.49-10.29-15.64-21.93-22.64-33.88-4.5-7.68-9.58-15-15-22.06-2.55-3.32-5.06-6.56-8.17-9.38-2.89-2.62-5.84-4.94-7.59-8.5-2.56-5.2-10.32-.64-7.77 4.54 2.98 6.06 8.85 9.47 13.1 14.51 5.67 6.73 10.75 14.15 15.37 21.64 7.68 12.41 13.81 25.11 23.45 36.26 17.79 20.59 41.51 35.3 66.77 44.96 14.58 5.58 29.77 9.39 45.2 11.68 5.67.84 8.11-7.83 2.39-8.68l-.01-.01Z"
              />
              <Path
                id="Vector_19"
                fill="#D66112"
                d="M434.86 2190.46c-11.47-1.1-22.92-2.39-34.32-4.1-5.67-.85-8.11 7.82-2.39 8.68 12.2 1.82 24.44 3.24 36.72 4.42 5.76.55 5.72-8.45 0-9h-.01Z"
              />
              <Path
                id="Vector_20"
                fill="#D66112"
                d="M514.94 2196c-15.45-.97-30.9-1.84-46.35-2.78-5.78-.35-5.76 8.65 0 9 15.45.94 30.9 1.81 46.35 2.78 5.78.36 5.76-8.64 0-9Z"
              />
              <Path
                id="Vector_21"
                fill="#D66112"
                d="M620.12 2206.1c-22.92-1.23-45.6-4.93-68.4-7.42-5.75-.63-5.71 8.38 0 9 22.8 2.49 45.47 6.18 68.4 7.42 5.79.31 5.77-8.69 0-9Z"
              />
              <Path
                id="Vector_22"
                fill="#D66112"
                d="M697.95 2211.24c-14.38-1.93-28.81-3.38-43.32-3.9-5.79-.21-5.78 8.79 0 9 14.51.52 28.93 1.97 43.32 3.9 5.72.77 5.67-8.24 0-9Z"
              />
              <Path
                id="Vector_23"
                fill="#D66112"
                d="M935.8 2042.38c-5.32 4.39-10.57 8.87-15.84 13.33-4.95 4.19-10.54 7.99-14.72 12.92-1.45 1.71-3.14 3.25-4.39 5.13-1.33 1.99-3.07 2.85-4.66 4.67-3.76 4.33-6.31 10.08-8.33 15.4-9.26 24.37.19 50.19 4.21 74.63 1.47 8.98 1.29 17.53-3.74 25.4-4.43 6.93-11.47 11.79-18.68 15.49-7.79 3.99-16.23 6.65-24.71 8.71-11.49 2.79-22.95 2.19-34.72 2.19-31.65 0-63.44-.62-94.64-6.46-5.66-1.06-8.09 7.61-2.39 8.68 28.51 5.35 57.51 6.59 86.46 6.76 6.72.04 13.44.03 20.16.01 6.72-.02 13.24.47 19.71-.79 8.86-1.73 17.64-4.06 26.02-7.43 15.4-6.2 31.06-16.52 35.31-33.63 2.55-10.29.15-20.87-1.91-31.03-2.77-13.68-6.21-27.36-6.01-41.42.11-7.69 1.74-15.02 5.01-22 1.31-2.79 2.85-6.3 5-8.54 1.76-1.83 3.28-2.66 4.82-4.82 4.29-6.05 10.09-10.35 15.72-15.08 6.24-5.24 12.41-10.55 18.69-15.74 4.47-3.69-1.93-10.02-6.36-6.36l-.01-.02Z"
              />
              <Path
                id="Vector_24"
                fill="#D66112"
                d="M961.78 2006.11c-5.75 7.63-9.47 16.51-15.11 24.22-3.42 4.68 4.38 9.17 7.77 4.54 5.64-7.7 9.36-16.59 15.11-24.22 3.48-4.63-4.33-9.12-7.77-4.54Z"
              />
              <Path
                id="Vector_25"
                fill="#D66112"
                d="M968.53 1927.06c4.67 14.87 7.02 30.39 4.41 45.89-.96 5.67 7.72 8.1 8.68 2.39 2.89-17.15.77-34.17-4.41-50.68-1.73-5.51-10.42-3.16-8.68 2.39v.01Z"
              />
              <Path
                id="Vector_26"
                fill="#D66112"
                d="M949.17 1870.16c2.64 7.56 5.28 15.11 7.79 22.72 1.8 5.48 10.5 3.13 8.68-2.39-2.5-7.6-5.14-15.16-7.79-22.72-1.9-5.43-10.6-3.09-8.68 2.39Z"
              />
              <Path
                id="Vector_27"
                fill="#D66112"
                d="M927.9 1795.39c3.04 13.5 6.45 26.9 9.99 40.28 1.48 5.59 10.16 3.22 8.68-2.39-3.54-13.37-6.95-26.78-9.99-40.28-1.27-5.65-9.95-3.26-8.68 2.39Z"
              />
              <Path
                id="Vector_28"
                fill="#D66112"
                d="M917.79 1696.29c-1.11 21.72-.18 43.42 3.57 64.87.99 5.69 9.67 3.28 8.68-2.39-3.61-20.64-4.31-41.58-3.25-62.47.3-5.79-8.7-5.77-9 0v-.01Z"
              />
              <Path
                id="Vector_29"
                fill="#D66112"
                d="M926.77 1630.18c-1.96 10.11-3.85 20.22-5.42 30.4-.88 5.67 7.79 8.11 8.68 2.39 1.58-10.17 3.46-20.29 5.42-30.4 1.1-5.66-7.58-8.08-8.68-2.39Z"
              />
              <Path
                id="Vector_30"
                fill="#D66112"
                d="M936.75 1574.88c-.84 7.04-2.02 14.02-3.5 20.96-1.2 5.65 7.47 8.06 8.68 2.39 1.64-7.73 2.89-15.51 3.82-23.35.68-5.74-8.32-5.69-9 0Z"
              />
              <Path
                id="Vector_31"
                fill="#D66112"
                d="M917.83 1224.3c10.97 10.18 19.12 22.19 26.52 35.16 7.53 13.18 12.54 28.68 9.73 43.96-2.23 12.11-9.49 23.91-20.86 29.32-12.64 6.02-27.67 2.64-41.07 5.27-11.35 2.23-22.81 7.84-30.01 17.11-8.24 10.61-10.26 25.35-7.39 38.24 5.58 25.02 27.2 42.69 44.54 59.83 11.48 11.35 21.97 23.66 28.87 38.38 3.4 7.25 5.79 14.97 7.29 22.83 1.63 8.54 1.63 17.2 2.67 25.8.68 5.68 9.69 5.75 9 0-1.79-14.85-2.02-29.21-7.17-43.48-4.76-13.19-12.1-25.04-21.09-35.75-17.09-20.36-41.76-36.11-52.54-61.2-5.53-12.88-5.7-29.93 4.3-40.81 9.28-10.1 23.22-12.94 36.34-13.37 13.82-.44 27.23-1.03 38.47-10.07 9.3-7.48 15.49-18.88 17.48-30.59 2.75-16.2-1.39-32.49-9.01-46.82-7.88-14.81-17.39-28.75-29.7-40.18-4.24-3.94-10.62 2.41-6.36 6.36l-.01.01Z"
              />
              <Path
                id="Vector_32"
                fill="#D66112"
                d="M852.96 1170.83c12.06 8.87 24.3 17.5 35.24 27.76 4.22 3.96 10.6-2.39 6.36-6.36-11.5-10.78-24.39-19.85-37.06-29.17-4.67-3.44-9.16 4.37-4.54 7.77Z"
              />
              <Path
                id="Vector_33"
                fill="#D66112"
                d="M803.98 1122.59c4.31 8.15 11.01 14.47 17.02 21.33 3.83 4.36 10.17-2.02 6.36-6.36-5.52-6.3-11.66-12.03-15.62-19.51-2.71-5.12-10.48-.58-7.77 4.54h.01Z"
              />
              <Path
                id="Vector_34"
                fill="#D66112"
                d="M753.9 1077.79c8.34 6.16 16.9 12.01 25.43 17.9 4.78 3.3 9.28-4.5 4.54-7.77-8.53-5.89-17.09-11.74-25.43-17.9-4.66-3.44-9.16 4.37-4.54 7.77Z"
              />
              <Path
                id="Vector_35"
                fill="#D66112"
                d="M693.63 1016.59c10.62 9.36 21.22 18.87 28.94 30.85 3.13 4.85 10.92.34 7.77-4.54-8.16-12.66-19.13-22.78-30.35-32.67-4.33-3.82-10.72 2.52-6.36 6.36Z"
              />
              <Path
                id="Vector_36"
                fill="#D66112"
                d="M570.04 783.8c-9.1 32.25-8.78 67.5 3.62 98.86 6.36 16.1 15.77 30.65 25.72 44.72s21.23 27.83 33.76 40.04c14.16 13.79 30.44 25.12 45.71 37.6 4.45 3.64 10.85-2.69 6.36-6.36-12.73-10.41-26.14-19.99-38.47-30.89-12.33-10.9-24.1-23.99-34.24-37.55-9.9-13.24-19.44-27.11-26.66-42.02-7.22-14.91-11.41-31.2-12.7-47.64-1.45-18.35.58-36.68 5.57-54.38 1.58-5.58-7.11-7.97-8.68-2.39l.01.01Z"
              />
              <Path
                id="Vector_37"
                fill="#D66112"
                d="M608.78 710.63c-4.34 5.64-8.49 11.42-12.6 17.23-2.06 2.91-4.12 5.82-6.19 8.71-2.52 3.51-3.39 4.83-3.39 9.12l1.32-3.18c-2.05 2.09-3.62 4.2-4.43 7.04-1.59 5.58 7.09 7.96 8.68 2.39.51-1.78 1.98-2.46 2.81-3.98.77-1.41.18-2.34.61-3.77.93-3.07 4.99-7.04 6.85-9.67 4.62-6.52 9.24-13.02 14.11-19.35 3.53-4.59-4.29-9.08-7.77-4.54Z"
              />
              <Path
                id="Vector_38"
                fill="#D66112"
                d="M669.83 666c-11.27 5.84-22.12 12.4-32.87 19.15-4.89 3.07-.38 10.86 4.54 7.77 10.75-6.75 21.6-13.31 32.87-19.15 5.14-2.67.6-10.44-4.54-7.77Z"
              />
              <Path
                id="Vector_39"
                fill="#D66112"
                d="M760.02 632.76c-18.03 6-36.02 12.14-53.96 18.44-5.43 1.91-3.09 10.6 2.39 8.68 17.93-6.3 35.92-12.43 53.96-18.44 5.47-1.82 3.12-10.52-2.39-8.68Z"
              />
              <Path
                id="Vector_40"
                fill="#D66112"
                d="M994.34 581.479c-73.2 8.75-146.06 21.85-216.11 45.27-5.47 1.83-3.12 10.52 2.39 8.68 69.27-23.16 141.32-36.29 213.72-44.95 5.68-.68 5.75-9.69 0-9Z"
              />
              <Path
                id="Vector_41"
                fill="#D66112"
                d="M1056.55 576.49c-14.48.5-28.93 1.52-43.35 2.93-5.71.56-5.77 9.56 0 9 14.42-1.4 28.87-2.43 43.35-2.93 5.78-.2 5.8-9.2 0-9Z"
              />
              <Path
                id="Vector_42"
                fill="#D66112"
                d="M1136.67 575.92c-15.21-.46-30.43-.58-45.65-.55-5.79.01-5.8 9.01 0 9 15.22-.03 30.43.09 45.65.55 5.79.18 5.79-8.82 0-9Z"
              />
              <Path
                id="Vector_43"
                fill="#D66112"
                d="M1256.59 591.14c-28.26-7.07-57.17-10.96-86.16-13.46-5.77-.5-5.74 8.51 0 9 28.21 2.43 56.28 6.26 83.77 13.14 5.62 1.41 8.02-7.27 2.39-8.68Z"
              />
              <Path
                id="Vector_44"
                fill="#D66112"
                d="M9.97 855.809c11.29 2.89 22.28 6.71 32.88 11.58 5.23 2.4 9.81-5.36 4.54-7.77-11.31-5.19-22.97-9.4-35.03-12.49-5.61-1.44-8.01 7.24-2.39 8.68Z"
              />
              <Path
                id="Vector_45"
                fill="#D66112"
                d="M81.78 884.27c9.24 2.71 18.02 6.38 26.44 11.04 5.07 2.81 9.62-4.96 4.54-7.77-9.08-5.03-18.64-9.02-28.59-11.95-5.57-1.64-7.95 7.05-2.39 8.68Z"
              />
              <Path
                id="Vector_46"
                fill="#D66112"
                d="M144.59 914.82c9.71 4.01 18.83 9.23 27.35 15.35 4.71 3.39 9.21-4.42 4.54-7.77-9.2-6.61-19.04-11.94-29.5-16.26-5.35-2.21-7.68 6.49-2.39 8.68Z"
              />
              <Path
                id="Vector_47"
                fill="#D66112"
                d="M196.36 945.019c6.52 3.11 13.03 6.22 19.55 9.33 5.2 2.48 9.77-5.27 4.54-7.77l-19.55-9.33c-5.2-2.48-9.77 5.27-4.54 7.77Z"
              />
            </G>
          </G>
          <G id="Group_3">
            {dataBtn.map((item, index) => (
              <BtnMission
                status={
                  data[index]?.numOfQuestion > 0 &&
                  data[index]?.numOfPass == data[index]?.numOfQuestion
                    ? 'complete'
                    : data[index]?.id
                      ? 'progress'
                      : 'inactive'
                }
                progress={data[index]?.numOfPass / data[index]?.numOfQuestion}
                key={item.id}
                x={item.x}
                y={item.y}
                title={`Mission ${index + 7}`}
                onItemPress={() =>
                  onItemPress(
                    data[index],
                    data[index]?.id ? data[index]?.id : -1,
                  )
                }
              />
            ))}
          </G>
        </G>
        <Defs>
          <ClipPath id="clip0_392_540">
            <Path fill="#fff" d="M0 0h1179v2556H0z" />
          </ClipPath>
        </Defs>
      </Svg>
    </View>
  );
};
export default ItemMission2;
