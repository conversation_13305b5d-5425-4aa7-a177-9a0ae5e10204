import * as React from 'react';
import {useRef} from 'react';
import {G, Image, Path, Rect, Text} from 'react-native-svg';
import {GestureResponderEvent} from 'react-native';

interface BtnMissionProps {
  x: number;
  y: number;
  title: string;
  onItemPress: () => void;
  status: 'progress' | 'complete' | 'inactive';
  progress?: number;
}

const TAP_THRESHOLD = 10;
const BtnMission: React.FC<BtnMissionProps> = ({
  x,
  y,
  title,
  onItemPress,
  status,
  progress,
}) => {
  const startX = useRef(0);
  const startY = useRef(0);
  const isTapValid = useRef(false);
  const numberProgress = progress && progress > 0 ? progress : 0.05;
  const handlePressIn = (e: GestureResponderEvent) => {
    const {locationX, locationY} = e.nativeEvent;
    startX.current = locationX;
    startY.current = locationY;
    isTapValid.current = true;
  };

  const handleMove = (e: GestureResponderEvent) => {
    const {locationX, locationY} = e.nativeEvent;
    const dx = Math.abs(locationX - startX.current);
    const dy = Math.abs(locationY - startY.current);
    if (dx > TAP_THRESHOLD || dy > TAP_THRESHOLD) {
      isTapValid.current = false;
    }
  };

  const handlePressOut = (e: GestureResponderEvent) => {
    if (isTapValid.current) {
      onItemPress();
    }
    isTapValid.current = false;
  };
  if (status == 'inactive') {
    return (
      <G
        transform={`translate(${x}, ${y})`}
        onStartShouldSetResponder={() => true}
        onMoveShouldSetResponder={() => true}
        onResponderGrant={handlePressIn}
        onResponderMove={handleMove}
        onResponderRelease={handlePressOut}>
        <Image
          id="image_1390"
          width={258}
          height={260}
          preserveAspectRatio="none"
          href={require('./inactive.webp')}
        />
        <Text
          x={130}
          y={245}
          fontWeight={'bold'}
          textAnchor="middle"
          fontSize={26}
          fill={'#5B5B5B'}>
          {title}
        </Text>
      </G>
    );
  }
  if (status == 'complete') {
    return (
      <G
        transform={`translate(${x}, ${y})`}
        onStartShouldSetResponder={() => true}
        onMoveShouldSetResponder={() => true}
        onResponderGrant={handlePressIn}
        onResponderMove={handleMove}
        onResponderRelease={handlePressOut}>
        <Image
          id="image_1390"
          width={258}
          height={260}
          preserveAspectRatio="none"
          href={require('./complete.webp')}
        />
        <Text
          x={130}
          y={245}
          fontWeight={'bold'}
          textAnchor="middle"
          fontSize={26}
          fill={'#FFFFFF'}>
          {title}
        </Text>
      </G>
    );
  }
  return (
    <G
      transform={`translate(${x}, ${y})`}
      onStartShouldSetResponder={() => true}
      onMoveShouldSetResponder={() => true}
      onResponderGrant={handlePressIn}
      onResponderMove={handleMove}
      onResponderRelease={handlePressOut}>
      <Image
        id="image0_392_1390"
        width={258}
        height={260}
        preserveAspectRatio="none"
        href={require('./bgBtn.webp')}
      />
      <G id="Group_2">
        <Path
          id="Vector"
          fill="#D67F00"
          stroke="#75390C"
          strokeMiterlimit={10}
          strokeWidth={3}
          d="M228.1 213.951H53.85c-4 0-7.24-3.24-7.24-7.24v-31.51c0-4 3.24-7.24 7.24-7.24H228.1c12.7 0 23 10.3 23 23s-10.3 23-23 23v-.01Z"
        />
        <Path
          id="Vector_2"
          stroke="#75390C"
          strokeMiterlimit={10}
          strokeWidth={3}
          d="M227.4 200.851H58.97c-.99 0-1.79-.8-1.79-1.79v-16.22c0-.99.8-1.79 1.79-1.79H227.4c5.47 0 9.9 4.43 9.9 9.9 0 5.47-4.43 9.9-9.9 9.9Z"
        />
        <G id="controlle">
          <Rect
            x="65"
            y="180.6"
            width={171.8 * numberProgress}
            height="20.553"
            rx="9.5"
            fill="#FFF700"
          />
        </G>
        <Path
          id="Vector_5"
          fill="#D67F00"
          stroke="#75390C"
          strokeMiterlimit={10}
          strokeWidth={3}
          d="M37.81 222.88c17.634 0 31.93-14.296 31.93-31.931 0-17.634-14.296-31.929-31.93-31.929-17.635 0-31.93 14.295-31.93 31.929 0 17.635 14.295 31.931 31.93 31.931Z"
        />
        <G id="Group_3">
          <Path
            id="Vector_6"
            fill="#FFF200"
            d="M60.92 184.201a3.117 3.117 0 0 0-2.54-2.14l-11.73-1.66-5.28-10.69a3.116 3.116 0 0 0-2.82-1.75c-1.21 0-2.29.67-2.82 1.75l-5.21 10.64-11.8 1.71c-1.19.17-2.17.99-2.54 2.14-.37 1.15-.07 2.38.8 3.23l8.51 8.24-2.02 11.75a3.142 3.142 0 0 0 3.1 3.68c.5 0 1-.12 1.47-.36l10.47-5.55 10.55 5.55c1.07.56 2.34.47 3.31-.24a3.11 3.11 0 0 0 1.25-3.08l-2.04-11.67 8.54-8.32c.86-.84 1.17-2.08.8-3.23Z"
          />
          <Path
            id="Vector_7"
            fill="#FFF200"
            d="m39.92 170.421 5.28 10.69c.22.45.65.76 1.15.84l11.8 1.71c1.25.18 1.75 1.72.85 2.6l-8.54 8.32c-.36.35-.52.86-.44 1.35l2.02 11.75c.21 1.25-1.1 2.2-2.22 1.61l-10.55-5.55c-.44-.23-.98-.23-1.42 0l-10.55 5.55c-1.12.59-2.43-.36-2.22-1.61l2.02-11.75c.08-.5-.08-1-.44-1.35l-8.54-8.32c-.91-.88-.41-2.42.85-2.6l11.8-1.71c.5-.07.93-.38 1.15-.84l5.28-10.69c.56-1.14 2.18-1.14 2.74 0h-.02Z"
          />
          <Path
            id="Vector_8"
            fill="#FFF200"
            d="m55.57 184.432-10.25-1.49c-.43-.06-.81-.33-1-.73l-4.58-9.29c-.49-.99-1.89-.99-2.38 0l-4.58 9.29c-.19.39-.57.66-1 .73l-10.25 1.49c-.34.05-.61.23-.81.46 5.43 1.72 11.46 2.68 17.83 2.68 6.37 0 12.4-.96 17.83-2.68-.2-.23-.46-.41-.81-.46Z"
          />
        </G>
      </G>
      <Text
        x={130}
        y={245}
        fontWeight={'bold'}
        textAnchor="middle"
        fontSize={26}
        fill={'#FFFFFF'}>
        {title}
      </Text>
    </G>
  );
};
export default BtnMission;
