import React from 'react';
import {Pressable, View} from 'react-native';
import {heightScreen, widthScreen} from '../../../src/utils/Scale';
import Svg, {G, Image, Text} from 'react-native-svg';
import {FontFamily} from '../../../src/themes/typography';
import {moderateVerticalScale, scale} from 'react-native-size-matters';
import {navigate} from '../../../src/navigation/NavigationServices';
import {APP_SCREEN} from '../../../src/navigation/screenType';
import FastImage from 'react-native-fast-image';

type Props = {
  className: string;
  teacher: string;
  numberStudent: string;
  joinSince: string;
  classId: string;
};

const SvgYouClassClosed = ({
  className,
  teacher,
  numberStudent,
  joinSince,
  classId,
}: Props) => {
  const handleClassLeaderBoard = () => {
    navigate(APP_SCREEN.CLASS_LEADERBOARD, {classId});
  };

  const handleMission = () => {
    navigate(APP_SCREEN.UNIT, {classId});
  };
  return (
    <View
      style={{
        width: widthScreen,
        height: heightScreen,
      }}>
      <FastImage
        source={require('./bg-class-close.webp')}
        style={{
          position: 'absolute',
          width: widthScreen,
          height: heightScreen,
        }}
        resizeMode="cover"
      />
      <Svg
        width={'100%'}
        height={'100%'}
        viewBox="0 0 375 812"
        preserveAspectRatio="none"
        fill="none">
        <G id="">
          <G id="">
            <Text
              x={195.97}
              y={205.731}
              fontWeight={'bold'}
              textAnchor="middle"
              fontSize={20}
              fontFamily={FontFamily.bold}
              fill={'#fff'}>
              Your Class Information
            </Text>
          </G>
          <G id="Frame 427320188">
            <Text
              x={190.47}
              y={255.635}
              fontWeight={'bold'}
              textAnchor="middle"
              fontSize={30}
              fontFamily={FontFamily.bold}
              fill={'#fff'}>
              {className}
            </Text>
            <G id="Frame 427320182">
              <Text
                x={85.97}
                y={280.731}
                fontWeight={'normal'}
                textAnchor="start"
                fontSize={14}
                fontFamily={FontFamily.regular}
                fill={'#fff'}>
                Teacher:
              </Text>
              <Text
                x={301.971}
                y={280.731}
                fontWeight={'normal'}
                textAnchor="end"
                fontSize={14}
                fontFamily={FontFamily.regular}
                fill={'#fff'}>
                {teacher}
              </Text>
            </G>
            <G id="Frame 427320185">
              <Text
                x={85.97}
                y={305.731}
                fontWeight={'normal'}
                textAnchor="start"
                fontSize={14}
                fontFamily={FontFamily.regular}
                fill={'#fff'}>
                Number of students:
              </Text>
              <Text
                x={301.971}
                y={305.731}
                fontWeight={'normal'}
                textAnchor="end"
                fontSize={14}
                fontFamily={FontFamily.regular}
                fill={'#fff'}>
                {numberStudent}
              </Text>
            </G>
            <G id="Frame 427320186">
              <Text
                x={85.97}
                y={330.799}
                fontWeight={'normal'}
                textAnchor="start"
                fontSize={14}
                fontFamily={FontFamily.regular}
                fill={'#fff'}>
                Joined since:
              </Text>
              <Text
                x={301.971}
                y={330.799}
                fontWeight={'normal'}
                textAnchor="end"
                fontSize={14}
                fontFamily={FontFamily.regular}
                fill={'#fff'}>
                {joinSince}
              </Text>
            </G>
            <G id="Frame 427320186">
              <Text
                x={85.97}
                y={355.799}
                fontWeight={'normal'}
                textAnchor="start"
                fontSize={14}
                fontFamily={FontFamily.regular}
                fill={'#fff'}>
                Status:
              </Text>
              <Text
                x={301.971}
                y={355.799}
                fontWeight={'normal'}
                textAnchor="end"
                fontSize={14}
                fontFamily={FontFamily.regular}
                fill={'#fff'}>
                Closed
              </Text>
            </G>
          </G>
          <G id="Frame 427320186">
            <Text
              x={190.47}
              y={385.799}
              fontWeight={'bold'}
              textAnchor="middle"
              fontSize={16}
              fontFamily={FontFamily.bold}
              fill={'#FFD400'}>
              You can still access to
            </Text>
            <Text
              x={190.47}
              y={410.799}
              fontWeight={'bold'}
              textAnchor="middle"
              fontSize={16}
              fontFamily={FontFamily.bold}
              fill={'#FFD400'}>
              previous mission for practicing
            </Text>
          </G>
          <G id="">
            <G id="Group 14358">
              <Image
                x={83}
                y={432.622}
                id="image0_367_1053"
                width={218.27}
                height={48}
                preserveAspectRatio="none"
                href={require('./mission.webp')}
              />
            </G>
            <G id="Group 14358">
              <Image
                x={83}
                y={492.622}
                id="image0_367_1053"
                width={218.27}
                height={48}
                preserveAspectRatio="none"
                href={require('./class-leaderboard.webp')}
              />
            </G>
          </G>
        </G>
      </Svg>
      <Pressable
        style={{
          width: scale(205.27),
          height: moderateVerticalScale(48),
          top: (432.622 / 812) * heightScreen,
          left: (83 / 375) * widthScreen,
          position: 'absolute',
          zIndex: 1,
        }}
        onPress={handleMission}
      />
      <Pressable
        style={{
          width: scale(205.27),
          height: moderateVerticalScale(48),
          top: (492.622 / 812) * heightScreen,
          left: (83 / 375) * widthScreen,
          position: 'absolute',
          zIndex: 1,
        }}
        onPress={handleClassLeaderBoard}
      />
    </View>
  );
};
export default SvgYouClassClosed;
