import React from 'react';
import {StyleSheet} from 'react-native';
import {G, Image, Text} from 'react-native-svg';
import {FontFamily} from '../../../src/themes/typography';

type Props = {
  id: string;
  y: number;
  numOfPass: number;
  numOfQuestion: number;
};

export const MissionCompletedItem = ({
  id,
  y,
  numOfPass,
  numOfQuestion,
}: Props) => {
  const isDone = numOfPass === numOfQuestion && numOfQuestion > 0;

  const missionIcon = isDone
    ? require('./mission-done.webp')
    : require('./mission-notdone.webp');

  return (
    <G id={id}>
      <Image
        x={64.941}
        y={y}
        width={252}
        height={72}
        preserveAspectRatio="none"
        href={require('./bg-mission-completed.webp')}
      />
      <G id="Frame 427320191">
        <G id="Frame 427320190">
          <G id="Mission Icons" clipPath="url(#clip2_1_1036)">
            <Image
              x={80}
              y={y + 12}
              width={48}
              height={48}
              preserveAspectRatio="none"
              href={missionIcon}
            />
          </G>
        </G>
      </G>
    </G>
  );
};
