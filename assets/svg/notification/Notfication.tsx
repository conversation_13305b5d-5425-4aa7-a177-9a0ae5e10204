import * as React from 'react';
import { View } from 'react-native';
import { moderateVerticalScale, scale } from 'react-native-size-matters';
import Svg, {
  G,
  Image
} from 'react-native-svg';
import { heightScreen, widthScreen } from '../../../src/utils/Scale';

type Props = {
  children: React.ReactNode;
};
const SVGNotification = ({children}: Props) => (
  <View
    style={{
      width: widthScreen,
      height: heightScreen,
    }}>
    <Svg
      width={'100%'}
      height={'100%'}
      viewBox="0 0 375 812"
      preserveAspectRatio="none"
      fill="none">
      <G id="Class Leaderboard" clipPath="url(#clip0_33_1049)">
        <G id="background">
          <Image
            x={0}
            y={0}
            id="image0_367_1053"
            width={375}
            height={812}
            preserveAspectRatio="xMidYMid slice"
            href={require('./bg-notification.webp')}
          />
        </G>
        <G id="table">
          <G id="Component 14">
            <Image
              x={43.727}
              y={79.99}
              id="image0_367_1053"
              width={296}
              height={67.82}
              preserveAspectRatio="none"
              href={require('./notification-header.webp')}
            />
          </G>
        </G>
      </G>
    </Svg>
    <View
      style={{
        position: 'absolute',
        left: (70 / 375) * widthScreen,
        top: (190 / 812) * heightScreen,
        width: scale(220),
        height: moderateVerticalScale(350),
      }}>
      {children}
    </View>
  </View>
);
export default SVGNotification;
