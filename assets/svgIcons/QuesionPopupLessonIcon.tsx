import * as React from 'react';
import Svg, {<PERSON><PERSON><PERSON><PERSON>, Defs, G, <PERSON>, SvgProps} from 'react-native-svg';

const QuesionPopupLessonIcon = (props: SvgProps) => (
  <Svg width={16} height={16} fill="none" {...props}>
    <G fill="#DB7310" clipPath="url(#a)">
      <Path d="M8 0a8 8 0 1 0 .001 16.001A8 8 0 0 0 8 0Zm0 14.643A6.644 6.644 0 0 1 8 1.357a6.644 6.644 0 0 1 0 13.286Z" />
      <Path d="M9.993 4.512A3.01 3.01 0 0 0 8 3.785a3.02 3.02 0 0 0-1.993.727c-.557.487-.864 1.143-.864 1.845v.135c0 .***************.143h.858a.143.143 0 0 0 .142-.143v-.135c0-.788.77-1.429 1.715-1.429.944 0 1.714.641 1.714 1.429 0 .555-.393 1.064-1.002 1.298a1.996 1.996 0 0 0-1.285 1.89v.383c0 .***************.143h.858a.143.143 0 0 0 .142-.143v-.405a.862.862 0 0 1 .552-.8c1.054-.406 1.734-1.334 1.734-2.366.002-.702-.305-1.358-.862-1.845Zm-2.708 7.416a.714.714 0 1 0 1.429 0 .714.714 0 0 0-1.429 0Z" />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M0 0h16v16H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default QuesionPopupLessonIcon;
