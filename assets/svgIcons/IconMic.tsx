import * as React from 'react';
import Svg, {<PERSON>lip<PERSON><PERSON>, Defs, G, Path, SvgProps} from 'react-native-svg';

const IconMic: React.FC<SvgProps> = props => (
  <Svg width={49} height={48} fill="none" {...props}>
    <G clipPath="url(#a)">
      <Path
        fill="#fff"
        d="M38.64 24.02a2 2 0 0 1 1.697 2.263c-1.016 7.104-6.695 12.703-13.838 13.593V42a2 2 0 1 1-4 0v-2.124c-7.143-.89-12.822-6.489-13.838-13.593a2 2 0 0 1 3.96-.566C13.452 31.53 18.456 36 24.499 36c6.044 0 11.047-4.47 11.878-10.283a2 2 0 0 1 2.263-1.697ZM24.5 4c5.523 0 10 4.477 10 10v10c0 5.523-4.477 10-10 10s-10-4.477-10-10V14c0-5.523 4.477-10 10-10Z"
      />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M.5 0h48v48H.5z" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default IconMic;
