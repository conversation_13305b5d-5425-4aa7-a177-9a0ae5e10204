import * as React from 'react';
import Svg, {Circle, <PERSON>lipPath, Defs, G, Path, SvgProps} from 'react-native-svg';

const IconIncorrectItem: React.FC<SvgProps> = props => (
  <Svg width={20} height={20} fill="none" {...props}>
    <G clipPath="url(#a)">
      <Circle cx={10.379} cy={9.744} r={8.333} fill="#fff" />
      <Path
        fill="#F04438"
        d="M10.379 1.41a8.333 8.333 0 1 1 0 16.667 8.333 8.333 0 0 1 0-16.667ZM8.61 6.797a.833.833 0 0 0-1.248 1.1l.07.079L9.2 9.744l-1.768 1.768a.833.833 0 0 0 1.1 1.247l.079-.069 1.768-1.768 1.768 1.768a.833.833 0 0 0 1.247-1.1l-.069-.079-1.768-1.767 1.768-1.768a.833.833 0 0 0-1.1-1.248l-.079.07-1.767 1.767L8.61 6.797Z"
      />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M0 0h20v20H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default IconIncorrectItem;
