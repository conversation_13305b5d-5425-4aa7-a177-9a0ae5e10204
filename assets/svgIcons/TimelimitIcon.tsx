import * as React from 'react';
import Svg, {Path, SvgProps} from 'react-native-svg';

const TimelimitIcon = (props: SvgProps) => (
  <Svg width={20} height={20} fill="none" {...props}>
    <Path
      fill="#DB7310"
      d="M15.179 4.645h-1.786a.18.18 0 0 0-.178.179v10.357c0 .098.08.179.178.179h1.786a.18.18 0 0 0 .178-.179V4.824a.18.18 0 0 0-.178-.179Zm-4.286 6.25H9.107a.18.18 0 0 0-.178.179v4.107c0 .098.08.179.178.179h1.786a.18.18 0 0 0 .179-.179v-4.107a.18.18 0 0 0-.179-.179ZM6.607 9.288H4.822a.18.18 0 0 0-.179.179v5.714c0 .098.08.179.179.179h1.785c.099 0 .179-.08.179-.179V9.467a.18.18 0 0 0-.179-.179Zm-4.821 9.643h16.429c.395 0 .714-.319.714-.714V1.788a.714.714 0 0 0-.714-.714H1.786a.714.714 0 0 0-.714.714v16.429c0 .395.319.714.714.714Zm.893-16.25h14.643v14.643H2.679V2.681Z"
    />
  </Svg>
);
export default TimelimitIcon;
