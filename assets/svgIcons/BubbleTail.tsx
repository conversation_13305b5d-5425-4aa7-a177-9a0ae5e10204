import React from 'react';
import Svg, {Path} from 'react-native-svg';

type Props = {
  isIdea?: boolean;
};

export const BubbleTail = ({isIdea}: Props) => {
  return (
    <>
      {isIdea ? (
        <Svg width="33" height="53" viewBox="0 0 33 53" fill="none">
          <Path
            d="M16.0601 43C18.8215 43 21.0601 45.2386 21.0601 48C21.0601 50.7614 18.8215 53 16.0601 53C13.2986 53 11.0601 50.7614 11.0601 48C11.0601 45.2386 13.2986 43 16.0601 43ZM25.0601 21C28.9261 21 32.0601 24.134 32.0601 28C32.0601 31.866 28.9261 35 25.0601 35C21.1941 35 18.0601 31.866 18.0601 28C18.0601 24.134 21.1941 21 25.0601 21ZM8.06006 0C12.4783 0 16.0601 3.58172 16.0601 8C16.0601 12.4183 12.4783 16 8.06006 16C3.64178 16 0.0600586 12.4183 0.0600586 8C0.0600586 3.58172 3.64178 0 8.06006 0Z"
            fill="white"
          />
        </Svg>
      ) : (
        <Svg width="30" height="18" viewBox="0 0 30 18" fill="none">
          <Path
            d="M26.7141 0H22.1682H15.0333C14.21 0 13.4718 0.503 13.1684 1.269C11.9037 4.46 9.87478 9.45 8.18054 13.257C7.54438 14.687 8.67719 16.296 10.2058 15.962C19.635 13.902 25.5692 7.394 28.3531 2.641C29.0682 1.42 28.1291 0 26.7141 0Z"
            fill="white"
          />
        </Svg>
      )}
    </>
  );
};
