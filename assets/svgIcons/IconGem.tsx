import * as React from 'react';
import Svg, {
  <PERSON>,
  Defs,
  G,
  <PERSON>,
  <PERSON>,
  RadialGradient,
  Stop,
  SvgProps,
} from 'react-native-svg';
/* SVGR has dropped some elements not supported by react-native-svg: filter */
const IconGem: React.FC<SvgProps> = props => (
  <Svg width={44} height={44} fill="none" {...props}>
    <G filter="url(#a)">
      <Path
        fill="url(#b)"
        d="M29.825 23.665a8 8 0 1 1-15.65-3.33 8 8 0 0 1 15.65 3.33Z"
      />
    </G>
    <Path
      stroke="#fff"
      d="M19.144 13.994a8.5 8.5 0 0 1 4.622-.309l.407.097c.942.25 1.836.66 2.64 1.212l.34.246a8.503 8.503 0 0 1 1.976 2.131l.219.356c.49.842.832 1.764 1.01 2.725l.066.413c.111.828.1 1.669-.033 2.493l-.077.412a8.501 8.501 0 0 1-11.378 6.159l-.291-.12a8.503 8.503 0 0 1-5.144-7.92l.01-.315a8.502 8.502 0 0 1 1.483-4.388l.184-.256a8.501 8.501 0 0 1 3.672-2.825l.294-.11Z"
    />
    <Mask
      id="d"
      width={16}
      height={16}
      x={14}
      y={14}
      maskUnits="userSpaceOnUse"
      style={{
        maskType: 'alpha',
      }}>
      <Path
        fill="url(#c)"
        d="M29.825 23.665a8 8 0 1 1-15.65-3.33 8 8 0 0 1 15.65 3.33Z"
      />
    </Mask>
    <G filter="url(#e)" mask="url(#d)">
      <Path
        fill="url(#f)"
        d="M26.343 15.286c-.032.028-3.53 3.004-4.346 6.691.787-.495 3.755-2.126 7.852-1.482l2.27 3.815-3.375 2.2c-.03-.036-3.002-3.52-6.675-4.337.524.773 2.151 3.558 1.514 7.605l-3.868 2.497-2.056-3.542c.032-.027 3.385-2.836 4.196-6.505-.767.523-3.554 2.159-7.61 1.517l-2.496-3.863 3.544-2.06c.029.034 2.838 3.393 6.508 4.198-.5-.797-2.12-3.76-1.477-7.845l3.82-2.26 2.2 3.371Z"
      />
    </G>
    <G
      filter="url(#g)"
      style={{
        mixBlendMode: 'hard-light',
      }}>
      <Circle cx={21.931} cy={21.93} r={4.323} fill="#FEC6E9" />
    </G>
    <Path
      fill="#fff"
      fillRule="evenodd"
      d="M21.664 16.366c0-.5.406-.907.907-.907 1.43 0 2.612.436 3.53 1.177.91.732 1.512 1.725 1.866 2.776a.907.907 0 0 1-1.72.578c-.267-.794-.698-1.47-1.284-1.942-.577-.466-1.352-.775-2.392-.775a.907.907 0 0 1-.907-.907Z"
      clipRule="evenodd"
      opacity={0.6}
    />
    <Defs>
      <RadialGradient
        id="b"
        cx={0}
        cy={0}
        r={1}
        gradientTransform="matrix(-.03911 7.9019 -6.8312 -.03381 22.04 22.098)"
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#FF4880" />
        <Stop offset={1} stopColor="#FF94F6" />
      </RadialGradient>
      <RadialGradient
        id="c"
        cx={0}
        cy={0}
        r={1}
        gradientTransform="matrix(0 8 -8 0 22 22)"
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#52ACFF" />
        <Stop offset={1} stopColor="#06F" />
      </RadialGradient>
      <RadialGradient
        id="f"
        cx={0}
        cy={0}
        r={1}
        gradientTransform="matrix(0 7.55961 -8.31712 0 21.827 22.186)"
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#F4628F" />
        <Stop offset={1} stopColor="#FFB3CA" />
      </RadialGradient>
    </Defs>
  </Svg>
);
export default IconGem;
