import * as React from 'react';
import {FC} from 'react';
import Svg, {Defs, Image, Path, Pattern, SvgProps, Use} from 'react-native-svg';

const IconClock: FC<SvgProps> = props => (
  <Svg width={24} height={24} fill="none" {...props}>
    <Path fill="url(#a)" d="M0 0h24v24H0z" />
    <Defs>
      <Pattern
        id="a"
        width={1}
        height={1}
        patternContentUnits="objectBoundingBox">
        <Use xlinkHref="#b" transform="scale(.00625)" />
      </Pattern>
      <Image
        xlinkHref="data:image/png;base64,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"
        id="b"
        width={160}
        height={160}
        preserveAspectRatio="none"
      />
    </Defs>
  </Svg>
);
export default IconClock;
