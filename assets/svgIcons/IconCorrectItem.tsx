import * as React from 'react';
import Svg, {Circle, ClipPath, Defs, G, Path, SvgProps} from 'react-native-svg';

const IconCorrectItem: React.FC<SvgProps> = props => (
  <Svg width={20} height={20} fill="none" {...props}>
    <G clipPath="url(#a)">
      <Circle cx={10.151} cy={9.744} r={8.333} fill="#fff" />
      <Path
        fill="#66C61C"
        d="M10.151 1.41a8.333 8.333 0 1 1 0 16.667 8.333 8.333 0 0 1 0-16.667Zm2.946 5.318-4.124 4.125-1.768-1.768a.833.833 0 0 0-1.179 1.179l2.298 2.298a.917.917 0 0 0 1.297 0l4.655-4.655a.833.833 0 0 0-1.179-1.179Z"
      />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M0 0h20v20H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default IconCorrectItem;
