import * as React from 'react';
import Svg, {<PERSON><PERSON><PERSON><PERSON>, De<PERSON>, G, <PERSON>, SvgProps} from 'react-native-svg';

const IconSpeak: React.FC<SvgProps> = props => (
  <Svg width={121} height={120} fill="none" {...props}>
    <G clipPath="url(#a)">
      <Path
        fill="#FF8612"
        d="M66.803 16.496c3.482-2.488 8.26-.215 8.669 3.902l.028.573v78.057c0 4.279-4.626 6.846-8.215 4.785l-.482-.31-32.905-23.504H20.5c-5.272 0-9.59-4.079-9.973-9.253L10.5 70V50c0-5.272 4.08-9.591 9.254-9.973l.746-.028h13.398l32.905-23.503Zm32.032 17.417C105.987 40.314 110.5 49.633 110.5 60c0 10.366-4.513 19.684-11.665 26.086a5 5 0 1 1-6.669-7.451C97.287 74.05 100.5 67.403 100.5 60c0-7.404-3.212-14.051-8.335-18.636a5 5 0 1 1 6.67-7.451Zm-10 11.18A19.961 19.961 0 0 1 95.5 60a19.962 19.962 0 0 1-6.665 14.907 5 5 0 0 1-7.11-7.004l.44-.448A9.963 9.963 0 0 0 85.5 60a9.952 9.952 0 0 0-2.742-6.88l-.593-.575a5 5 0 1 1 6.67-7.452Z"
      />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M.5 0h120v120H.5z" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default IconSpeak;
