import * as React from 'react';
import Svg, {ClipP<PERSON>, Defs, G, Path, SvgProps} from 'react-native-svg';

const TimeLessonIcon = (props: SvgProps) => (
  <Svg width={16} height={16} fill="none" {...props}>
    <G fill="#DB7310" clipPath="url(#a)">
      <Path d="M8 0a8 8 0 1 0 .001 16.001A8 8 0 0 0 8 0Zm0 14.643A6.644 6.644 0 0 1 8 1.357a6.644 6.644 0 0 1 0 13.286Z" />
      <Path d="M11.12 10.259 8.573 8.418v-4.42a.143.143 0 0 0-.143-.143h-.858a.143.143 0 0 0-.143.143v4.918c0 .**************.116l2.953 2.154a.144.144 0 0 0 .2-.03l.51-.697a.142.142 0 0 0-.031-.2Z" />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M0 0h16v16H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default TimeLessonIcon;
